# 🚀 ANSISS Website - Plesk Deployment Guide

## ✅ Your App is Ready for Plesk!

Your application is perfectly suited for Plesk hosting because:
- ✅ Simple Node.js + Express structure
- ✅ No database dependencies
- ✅ Static files + SPA routing
- ✅ Production-ready configuration

## 📋 Step-by-Step Deployment

### **1. Prepare Files for Upload**

Create a ZIP file with these folders/files:
```
ansiss-website.zip
├── html/              # All your HTML pages
├── public/            # CSS, JS, images
├── routes/            # Client-side routing
├── app.js             # Main server file
├── package.json       # Dependencies
└── README.md          # Optional
```

**Exclude these files/folders:**
- `node_modules/` (will be installed on server)
- `.git/` (version control)
- `*.log` files
- Development scripts

### **2. Plesk Setup Steps**

#### **Step A: Create Domain/Subdomain**
1. Log into Plesk
2. Go to "Websites & Domains"
3. Click "Add Domain" or "Add Subdomain"
4. Enter your domain (e.g., `ansiss.yourdomain.com`)

#### **Step B: Enable Node.js**
1. Go to your domain settings
2. Click "Node.js" in the sidebar
3. Enable Node.js
4. Select Node.js version (14.x or higher)
5. Set Document Root: `/httpdocs`
6. Set Application Root: `/httpdocs`
7. Set Startup File: `app.js`

#### **Step C: Upload Files**
1. Go to "File Manager"
2. Navigate to `httpdocs` folder
3. Upload your ZIP file
4. Extract the ZIP file
5. Move all contents to the root of `httpdocs`

#### **Step D: Install Dependencies**
1. In Node.js settings, click "NPM Install"
2. Or use SSH: `cd httpdocs && npm install`

#### **Step E: Configure Environment**
1. In Node.js settings, add environment variables:
   - `NODE_ENV=production`
   - `PORT=3000` (or whatever Plesk assigns)

#### **Step F: Start Application**
1. Click "Enable Node.js" 
2. Click "Restart App"
3. Your site should now be live!

### **3. Domain Configuration**

#### **Option A: Main Domain**
- Upload to main domain's `httpdocs`
- Access via: `https://yourdomain.com`

#### **Option B: Subdomain**
- Create subdomain: `ansiss.yourdomain.com`
- Upload to subdomain's `httpdocs`
- Access via: `https://ansiss.yourdomain.com`

### **4. SSL Certificate**

1. Go to "SSL/TLS Certificates"
2. Click "Get free certificate" (Let's Encrypt)
3. Enable "Secure your website with HTTPS"
4. Force HTTPS redirect

### **5. Performance Optimization**

#### **Enable Gzip Compression:**
1. Go to "Apache & nginx Settings"
2. Add to "Additional nginx directives":
```nginx
gzip on;
gzip_types text/css application/javascript text/javascript application/json;
gzip_min_length 1000;
```

#### **Enable Caching:**
Add to "Additional nginx directives":
```nginx
location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

### **6. Monitoring & Maintenance**

#### **Check Application Status:**
1. Go to Node.js settings
2. View "Application Status"
3. Check logs for errors

#### **Update Application:**
1. Upload new files via File Manager
2. Click "Restart App" in Node.js settings

#### **View Logs:**
1. Go to "Logs" in domain settings
2. Check "Error Logs" and "Access Logs"

## 🔧 Troubleshooting

### **Common Issues:**

#### **App Won't Start:**
- Check Node.js version compatibility
- Verify `package.json` syntax
- Check file permissions (755 for folders, 644 for files)

#### **Routes Not Working:**
- Ensure all HTML files are in `html/` folder
- Check `routes/simple-router.js` is accessible
- Verify file paths are correct

#### **Static Files Not Loading:**
- Check `public/` folder structure
- Verify CSS/JS file paths
- Check file permissions

#### **Performance Issues:**
- Enable Gzip compression
- Optimize images
- Enable browser caching

### **Support Commands (if SSH access available):**

```bash
# Check Node.js version
node --version

# Install dependencies
npm install

# Start application manually
npm start

# Check running processes
pm2 list

# View application logs
pm2 logs
```

## 📞 Final Notes

- **Backup**: Always backup before deployment
- **Testing**: Test on staging environment first
- **Monitoring**: Set up uptime monitoring
- **Updates**: Plan regular updates and maintenance

Your ANSISS website is now ready for professional hosting on Plesk! 🎉

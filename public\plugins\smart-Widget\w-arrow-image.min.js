!function(t) {
    var n = t;
    n._N2 = n._N2 || {
        _r: [],
        _d: [],
        r: function() {
            this._r.push(arguments)
        },
        d: function() {
            this._d.push(arguments)
        }
    };
    var i = t.document
      , o = (i.documentElement,
    t.setTimeout)
      , c = t.clearTimeout
      , r = n._N2;
    t.requestAnimationFrame,
    Object.assign,
    navigator.userAgent.indexOf("+http://www.google.com/bot.html") > -1 || n.requestIdleCallback,
    n.cancelIdleCallback;
    !function(t) {
        if ("complete" === i.readyState || "interactive" === i.readyState)
            t();
        else if (Document && Document.prototype && Document.prototype.addEventListener && Document.prototype.addEventListener !== i.addEventListener) {
            const n = ()=>{
                t(),
                t = ()=>{}
            }
            ;
            i.addEventListener("DOMContentLoaded", n),
            i.addEventListener("readystatechange", (()=>{
                "complete" !== i.readyState && "interactive" !== i.readyState || n()
            }
            )),
            Document.prototype.addEventListener.call(i, "DOMContentLoaded", n)
        } else
            i.addEventListener("DOMContentLoaded", t)
    }((function() {
        i.body
    }
    )),
    r.d("SmartSliderWidgetArrowImage", "SmartSliderWidget", (function() {
        function t(t, n, i) {
            this.tn = i,
            r.SmartSliderWidget.prototype.constructor.call(this, n, t, "#" + n.elementID + "-arrow-" + t)
        }
        return t.prototype = Object.create(r.SmartSliderWidget.prototype),
        t.prototype.constructor = t,
        t.prototype.onStart = function() {
            var t, n, i, o;
            t = this.widget,
            n = "click",
            i = function(e) {
                e.stopPropagation(),
                this.slider[this.tn]()
            }
            .bind(this),
            o = o || {},
            t.addEventListener(n, i, o)
        }
        ,
        function(n) {
            this.key = "arrow",
            this.previous = new t("previous",n,"previousWithDirection"),
            this.next = new t("next",n,"nextWithDirection")
        }
    }
    ))
}(window);

<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />

  <title>Bihar State Madrasa Education Board, Patna</title>

  <link href="/assets/img/favicon.jpg" rel="icon" type="image/png" />

  <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700" rel="stylesheet" />

  <link type="text/css" href="/assets/css/design.css" rel="stylesheet" />
 
  <link type="text/css" rel="stylesheet" href="/assets/css/dashboard-style.css" />
      <script type="text/javascript">
      </script>
  <style>
    
	label {
		font-size: 1rem;
	}
  </style>
  <style>
  
.top-nav {
   
    background-color: #466F05;
    padding-top: 5px;
    padding-bottom: 5px;
}

.col-auto p {
    display: inline-block;
    margin-bottom: 0;
    columns: #fff;
    margin-right: 10px;
}

.top-nav span,
.top-nav i {
    vertical-align: middle;
}

.notice-board {
            height: 400px; 
            overflow: hidden;
            position: relative;
        }
.scrolling-content {
            position: absolute;
            width: 100%;
            animation: scroll 10s linear infinite;
        }
@keyframes scroll {
            0% {
                top: 100%;
            }
            100% {
                top: -100%;
            }
        }
        .text-bolder {
            font-weight: bold;
        }
        .text-danger {
            color: red;
        }

    ::-webkit-scrollbar-track {
      box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
      -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
      background-color: #f5f5f5;
      border-radius: 10px;
    }

    ::-webkit-scrollbar {
      width: 10px;
      background-color: #f5f5f5;
    }

    ::-webkit-scrollbar-thumb {
      border-radius: 10px;
      background-color: #fff;
      background-image: -webkit-gradient(linear,
          40% 0%,
          75% 84%,
          from(#9c415f),
          to(#19911d),
          color-stop(0.6, #de8754));
    }

    :root {
      --circle-size: clamp(1.5rem, 5vw, 3rem);
      --spacing: clamp(0.25rem, 2vw, 0.5rem);
    }

    .nav-link a:hover {
      color: #ff0000; 
    }
    .form-container {
      border: 2px solid #007bff;
      border-radius: 8px;
      padding: 10px;
    }
  </style>
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-Q702VS189C"></script>

</head>

<body>

  <div class="site-mobile-menu site-navbar-target">
    <div class="site-mobile-menu-header">
      <div class="site-mobile-menu-close mt-3">
        <span class="fas fa-times js-menu-toggle"></span>
      </div>
    </div>
    <div class="site-mobile-menu-body"></div>
  </div>

   <div class="top-nav" id="home">
    <div class="container">
        <div class="row justify-content-between">
            <div class="col-auto">

            </div>
            <div class="col-auto social-icons">
              <div class="col-auto">
                <p style="color: #fff;"> <EMAIL></p>
                <p style="color: #fff;"> </i> ************</p>
            </div>
            </div>
        </div>
    </div>
</div>
  <header class="site-navbar js-sticky-header site-navbar-target shadow-lg" role="banner">
    <div class="container">
      <div class="row">
        <div class="site-logo col-3">
          <a href="/">
            <img src="/images/logo111.png" alt="BSMEBMain logo" width="268px" height="67px" />
          </a>
        </div>

        <div class="col-9">
          <nav class="site-navigation text-end ml-auto" role="navigation">
            <ul class="
                  site-menu
                  main-menu
                  js-clone-nav
                  ml-auto
                  d-none d-lg-block
                ">
              <li>
                <a href="/" class="nav-link" >Home</a>
              </li>
              <li >
                <a href="javascript:;" class="nav-link">About Board</a>
               
              </li>

              <li >
                <a href="javascript:;" class="nav-link">Contact Us</a>
              
              </li>
            </ul>
          </nav>
        </div>

        <div class="toggle-button d-inline-block d-lg-none text-end">
          <a href="javascript:;" class="site-menu-toggle py-5 js-menu-toggle text-black"><span class="fas fa-bars h3"></span></a>
        </div>
      </div>
    </div>
  </header>
<main class="main-content mt-0">
	<section>
		<div class="page-header min-vh-75">
			<div class="container">
				<div class="row">
					<div class="col-xl-7 col-lg-6 col-md-6">
						<div class="card my-4">
							<div class="card-header border-bottom-lg text-center bg-gradient-light">
								<h4 style="text-align: center;">Notice Board :-</h4>
							</div>
						  <div class="card-body notice-board">
                <div class="scrolling-content">
								<ol>
									<li class="text-bolder text-info ">
									कृपया फॉर्म  को ध्यानपूर्वक भरे !
									</li>
							
									<li  class="text-danger text-bolder">
								कृपया ध्यान दे Registration करते समय आप जहा के स्थायी(Permanent ) निवासी है</br >वही District चुने !
									</li>
									<li>
										<small>
											कृपया आवेदन पत्र या किसी भी दस्तावेज की हार्ड कॉपी मदरसा बोर्ड कार्यालय को न भेजें।
										</small>
									</li>
									<li>
										<small>
											किसी ऑनलाइन आवेदन के लिए भुगतान की गई राशि वापस नहीं की जाएगी।
										</small>
									</li>
								</ol>
							</div>
						</div>
            </div>
					</div>
          
					<div class="col-xl-5 col-lg-6 col-md-6">
            <div class="card-body form-container">
							<div class="card-header pb-0 text-left bg-transparent justify-content-center align-items-center">
        
								<h3 class="font-weight-bolder " style="color: rgb(0, 17, 255);">Start Registration</h3>
								<p class="mb-0" style="color: rgb(38, 0, 255);">Enter your Mobile Number,Name,And Home District To Register.</p>
							</div>
							<div class="card-body">
								<form action="" method="post" role="form" id="form" class="needs-validation" novalidate>
									<label>Mobile Number<em style="color: red;">*</em></label>
									<div class="mb-3">
										<input type="text" class="form-control" placeholder="Enter Mobile Number " name="Mobileno" aria-label="Mobileno" aria-describedby="Mobileno" maxlength="10" minlength="10" required>
									
									</div>
									<div class="form-group">
										<label>Full Name<em style="color: red;">*</em></label>
										<div class="input-group">
											<input type="text" id="Name" name="Name" class="form-control" placeholder="Enter Full Name"required>
										
										</div>
									</div>
                  <div class="form-group">
                    <label>Choose Home District<em style="color: red;">*</em> </label>
                 
                    <div class="input-group mt-3">
                        <select id="district" name="district" class="form-control" required>
                            <option value="">Select District</option>
                            <option value="district1">District 1</option>
                            <option value="district2">District 2</option>
                            <option value="district3">District 3</option>
                            <!-- Add more options as needed -->
                        </select>
                    </div>
                </div>
									<button type="submit" name="login" class="btn bg-gradient-info w-100 my-3 mb-0">Register</button>
								</form>
							</div>

						</div>
            </div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</section>
</main>

</body>

</html>

const fs = require('fs');
const path = require('path');

// Create html directory if it doesn't exist
if (!fs.existsSync('html')) {
    fs.mkdirSync('html');
}

// Read all EJS files from views directory
const viewsDir = 'views';
const htmlDir = 'html';

function convertEjsToHtml(filePath, outputPath) {
    let content = fs.readFileSync(filePath, 'utf8');
    
    // Remove EJS includes and replace with actual content
    content = content.replace(/<%- include\(['"](.+?)['"]\) %>/g, (match, includePath) => {
        const includeFile = path.join(viewsDir, includePath + '.ejs');
        if (fs.existsSync(includeFile)) {
            return fs.readFileSync(includeFile, 'utf8');
        }
        return '';
    });
    
    // Remove other EJS syntax (variables, logic, etc.)
    content = content.replace(/<%[\s\S]*?%>/g, '');
    content = content.replace(/<%=[\s\S]*?%>/g, '');
    content = content.replace(/<%-[\s\S]*?%>/g, '');
    
    // Clean up extra whitespace
    content = content.replace(/\n\s*\n\s*\n/g, '\n\n');
    
    fs.writeFileSync(outputPath, content);
    console.log(`Converted: ${filePath} -> ${outputPath}`);
}

// Get all EJS files
function getAllEjsFiles(dir) {
    const files = [];
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && item !== 'partials') {
            files.push(...getAllEjsFiles(fullPath));
        } else if (stat.isFile() && item.endsWith('.ejs')) {
            files.push(fullPath);
        }
    }
    
    return files;
}

// Convert all EJS files
const ejsFiles = getAllEjsFiles(viewsDir);

ejsFiles.forEach(ejsFile => {
    const relativePath = path.relative(viewsDir, ejsFile);
    const htmlFile = relativePath.replace('.ejs', '.html');
    const outputPath = path.join(htmlDir, htmlFile);
    
    // Create subdirectories if needed
    const outputDir = path.dirname(outputPath);
    if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
    }
    
    convertEjsToHtml(ejsFile, outputPath);
});

// Create a simple router.js for client-side routing
const routerJs = `
// Simple client-side router
class Router {
    constructor() {
        this.routes = {};
        this.init();
    }
    
    init() {
        window.addEventListener('popstate', () => this.handleRoute());
        document.addEventListener('DOMContentLoaded', () => this.handleRoute());
        
        // Handle all anchor clicks
        document.addEventListener('click', (e) => {
            if (e.target.tagName === 'A' && e.target.href.startsWith(window.location.origin)) {
                e.preventDefault();
                this.navigate(e.target.pathname);
            }
        });
    }
    
    addRoute(path, htmlFile) {
        this.routes[path] = htmlFile;
    }
    
    navigate(path) {
        history.pushState(null, null, path);
        this.handleRoute();
    }
    
    async handleRoute() {
        const path = window.location.pathname;
        const htmlFile = this.routes[path] || this.routes['/'] || 'homepage.html';
        
        try {
            const response = await fetch(htmlFile);
            const html = await response.text();
            document.documentElement.innerHTML = html;
        } catch (error) {
            console.error('Error loading page:', error);
        }
    }
}

// Initialize router with your routes
const router = new Router();
${ejsFiles.map(file => {
    const route = '/' + path.relative(viewsDir, file).replace('.ejs', '').replace(/\\\\/g, '/');
    const htmlFile = path.relative(viewsDir, file).replace('.ejs', '.html').replace(/\\\\/g, '/');
    return `router.addRoute('${route === '/homepage' ? '/' : route}', '${htmlFile}');`;
}).join('\n')}
`;

fs.writeFileSync(path.join('public', 'js', 'router.js'), routerJs);

console.log('\\nConversion complete!');
console.log('\\nNext steps:');
console.log('1. Review the converted HTML files in the html/ directory');
console.log('2. Update any hardcoded links to use the new routing');
console.log('3. Test your application by running: node app.js');

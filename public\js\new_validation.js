$(document).ready(function () {

	jQuery.validator.addMethod("common_regex", function(value, element) {

  		//var regex2 = /^[a-zA-Z0-9\-\_\-\.\, ]+$/;
		var regex2 = /^\\\"<>|$/;

			if (regex2.test(value)) {

				return true;

			} else {

				return this.optional(element )

			}

	}, 'Please Enter Valid Value.');

	

	jQuery.validator.addMethod("common_name_regex", function(value, element) {

  		var regex3 = /^[a-zA-Z0-9\-\. ]+$/;

			if (regex3.test(value)) {

				return true;

			} else {

				return this.optional(element )

			}

	}, 'Please Enter Valid Value.');

	

	jQuery.validator.addMethod("date_regex", function(value, element) {

  		var regex4 = /^(0[1-9]|[1-2][0-9]|3[0-1])-(0[1-9]|1[0-2])-[0-9]{4}$/;

			if (regex4.test(value)) {

				return true;

			} else {

				return this.optional(element )

			}

	}, 'Please Enter Valid Value.');

	

	jQuery.validator.addMethod("only_number_regx", function(value, element) {

  		var regex5 = /^[0-9]+$/;

			if (regex5.test(value)) {

				return true;

			} else {

				return this.optional(element )

			}

	}, 'Please Enter Valid Value.');

	

	jQuery.validator.addMethod("subject_regex", function(value, element) {

  		var regex6 = /^[a-zA-Z\-\_\.\, ]+$/;

			if (regex6.test(value)) {

				return true;

			} else {

				return this.optional(element )

			}

	}, 'Please Enter Valid Value.');

	

	jQuery.validator.addMethod("flog_number_regex", function(value, element) {

  		var regex7 = /^\d{0,3}(\.\d{0,2}){0,1}$/;

			if (regex7.test(value)) {

				return true;

			} else {

				return this.optional(element )

			}

	}, 'Please Enter Valid Value.');

	

	jQuery.validator.addMethod("regexmatchmail", function(value, element) {

  		var regex8 = /^[a-zA-Z0-9]+(\.[_a-zA-Z0-9-]+)*@[a-zA-Z0-9]+(\.[a-zA-Z0-9-]+)*(\.[A-Z a-z]{2,3})$/;

			if (regex8.test(value)) {

				return true;

			} else {

				return this.optional(element )

			}

	}, 'Please Enter Valid Value.');
	
	jQuery.validator.addMethod("message_regex", function(value, element) {

  		//var regexsdf8 = /^[a-zA-Z0-9\-\_\,\n\r ]+$/;
		var regexsdf8 = /^\\\"<>|$/;

			if (regexsdf8.test(value)) {

				return true;

			} else {

				return this.optional(element )

			}

	}, 'Please Enter Valid Value.');

	$('#rec_registeration_form').validate({
		rules:{
			adv_number:{required:true,common_regex:true},
			applied_for_post:{required:true},
			'functional_areass[]':{required:true},
			spacific_subjects_area:{required:true,message_regex:true},
			applicant_image: {required: true, accept: "jpg,JPEG,png",},
			applicant_name:{required:true,common_regex:true},
			applicant_dob:{required:true,date_regex:true},
			applicant_father_name:{required:true,common_regex:true},
			
			candiate_corres_addr1:{required:true,common_regex:true},
			candiate_corres_addr2:{common_regex:true},
			candiate_corres_pincode:{required:true,only_number_regx:true,number:true,maxlength:6},
			candiate_corres_stdcode:{only_number_regx:true,number:true,maxlength:5},
			candiate_corres_stdno:{only_number_regx:true,number:true,maxlength:8},
			
			candiate_permanent_addr1:{required:true,common_regex:true},
			candiate_permanent_addr2:{common_regex:true},
			candiate_permanent_pincode:{required:true,only_number_regx:true,number:true,maxlength:6},
			candiate_permanent_stdcode:{only_number_regx:true,number:true,maxlength:5},
			candiate_permanent_stdno:{only_number_regx:true,number:true,maxlength:8},
			
			candiate_corres_mobno:{required:true,only_number_regx:true,minlength:10,maxlength:15},
			candiate_corres_emailid:{required:true,email:true,regexmatchmail:true},
			gender:{required:true},
			marital_status:{required:true},
			candiate_nationality:{required:true,common_regex:true},
			candiate_state:{required:true},
			candidate_category:{required:true},
			handicapt_certificate:{accept: "jpg,JPEG,png,pdf"},
			present_employer:{required:true,common_regex:true},
			
			phd_board_collage_university:{common_regex:true},
			phd_percent_marks:{number:true,only_number_regx:true,},
			phd_subjects:{message_regex:true},
			phd_document_file:{accept: "jpg,JPEG,png,pdf,doc,docx"},
			
			pgard_board_collage_university:{common_regex:true},
			pgard_percent_marks:{number:true,only_number_regx:true,},
			pgard_subjects:{message_regex:true},
			pgard_year:{},
			pgard_document_file:{accept: "jpg,JPEG,png,pdf,doc,docx"},
			
			gard_board_collage_university:{common_regex:true},
			gard_percent_marks:{number:true,only_number_regx:true,},
			gard_subjects:{message_regex:true},
			gard_year:{},
			gard_document_file:{accept: "jpg,JPEG,png,pdf,doc,docx"},
			
			xii_board_collage_university:{common_regex:true},
			xii_percent_marks:{number:true,only_number_regx:true,},
			xii_subjects:{message_regex:true},
			xii_year:{},
			xii_document_file:{accept: "jpg,JPEG,png,pdf,doc,docx"},
			
			x_board_collage_university:{common_regex:true},
			x_percent_marks:{number:true,only_number_regx:true,},
			x_subjects:{message_regex:true},
			x_year:{},
			x_document_file:{accept: "jpg,JPEG,png,pdf,doc,docx"},
			
			other1_board_collage_university:{common_regex:true},
			other1_percent_marks:{number:true,only_number_regx:true,},
			other1_subjects:{message_regex:true},
			other1_document_file:{accept: "jpg,JPEG,png,pdf,doc,docx"},
			
			other2_board_collage_university:{common_regex:true},
			other2_percent_marks:{number:true,only_number_regx:true,},
			other2_subjects:{message_regex:true},
			other2_document_file:{accept: "jpg,JPEG,png,pdf,doc,docx"},
			
			phd_awared_status:{},
			title_of_phd_thesis:{message_regex:true},
			qualified_status:{},
			
			'name_of_employer_status[]':{common_regex:true},
			'designation[]':{common_regex:true},
			'workexp_from_month[]':{},
			'workexp_from_year[]':{},
			'workexp_to_mn[]':{},
			'workexp_to_yr[]':{},
			'basic_salary[]':{number:true,only_number_regx:true},
			'nature_of_duties[]':{common_regex:true},
			'experiance_cerficate_job[]':{accept: "jpg,JPEG,png,pdf,doc,docx"},
			
			under_grad_exp_from:{date_regex:true,},
			under_grad_exp_to:{date_regex:true,},
			under_g_total_year:{},
			under_g_total_month:{},
			exper_cerf_under_g:{accept: "jpg,JPEG,png,pdf,doc,docx"},
			
			under_pgrad_exp_from:{date_regex:true,},
			under_pgrad_exp_to:{date_regex:true,},
			under_pg_total_year:{},
			under_pg_total_month:{},
			exper_cerf_under_pg:{accept: "jpg,JPEG,png,pdf,doc,docx"},
			
			tt_exp_from:{date_regex:true,},
			tt_exp_to:{date_regex:true,},
			tt_total_year:{},
			tt_total_month:{},
			exper_cerf_tt:{accept: "jpg,JPEG,png,pdf,doc,docx"},
			
			spac_course_exp_from:{date_regex:true,},
			spac_course_exp_to:{date_regex:true,},
			spac_course_total_year:{},
			spac_course_total_month:{},
			exper_cerf_spac_course:{accept: "jpg,JPEG,png,pdf,doc,docx"},
			
			research_exp_from:{date_regex:true,},
			research_exp_to:{date_regex:true,},
			research_total_year:{},
			research_total_month:{},
			exper_cerf_research:{},
			
			rofessional_development_desc:{message_regex:true},
			admin_commit_desc:{message_regex:true},
			
			'research_book_title[]':{common_regex:true},
			'research_book_author[]':{},
			'research_book_pub_cc[]':{message_regex:true},
			'research_book_mn[]':{},
			'research_book_yr[]':{},
			'research_book_isbn_no[]':{common_regex:true},
			'research_book_image[]':{accept: "jpg,JPEG,png,pdf,doc,docx"},
			
			
			'research_book_1_chapter[]':{common_regex:true},
			'research_book_1_title[]':{common_regex:true},
			'research_book_1_author[]':{},
			'research_book_1_pub_cc[]':{message_regex:true},
			'research_book_1_mn[]':{},
			'research_book_1_yr[]':{},
			'research_book_1_isbn_no[]':{common_regex:true},
			'research_book_1_image[]':{accept: "jpg,JPEG,png,pdf,doc,docx"},
			
			'article_book_title[]':{common_regex:true},
			'name_of_journal[]':{common_regex:true},
			'article_book_author[]':{},
			'article_my_vol_page[]':{message_regex:true},
			'article_referred[]':{},
			'article_isbn_no[]':{common_regex:true},
			'article_level[]':{common_regex:true},
			'article_rating[]':{common_regex:true},
			'article_image[]':{accept: "jpg,JPEG,png,pdf,doc,docx"},
			
			
			'project_title[]':{common_regex:true},
			'project_type[]':{},
			'project_date_comm[]':{date_regex:true,},
			'project_date_complete[]':{date_regex:true},
			'project_funding_receive[]':{common_regex:true},
			'peroject_funding_agency[]':{common_regex:true},
			'project_outcome[]':{},
			'project_final_report[]':{common_regex:true},
			'project_image[]':{accept: "jpg,JPEG,png,pdf,doc,docx"},
			
			'paper_presented_title[]':{common_regex:true},
			'paper_presented_subject[]':{common_regex:true},
			'paper_present_institute_info[]':{message_regex:true},
			'paper_present_from[]':{date_regex:true},
			'paper_present_to[]':{date_regex:true},
			'paper_present_status[]':{},
			'paper_present_image[]':{accept: "jpg,JPEG,png,pdf,doc,docx"},
			
			
			membership_institutions_info:{message_regex:true},
			other_act_responsibiltities:{message_regex:true},
			other_relevant_information:{message_regex:true},
			any_break_academic:{message_regex:true},
			punished_in_collage:{message_regex:true},
			punished_in_collage_by_law:{message_regex:true},
			medically_unfit_stauts:{message_regex:true},
			reference_address_one:{message_regex:true},
			reference_address_two:{message_regex:true},
			desirable_qualifications:{message_regex:true},
			place:{common_regex:true},
			appdate:{date_regex:true},
			employer_proof_document_compy:{accept: "jpg,JPEG,png,pdf,doc,docx"},
			captcha:{common_regex:true}
			
		},
		messages:{
			adv_number:{required:"Please Enter Advertisement Number",common_regex:'Please Enter Valid Advertisement Number'},
			applied_for_post:{required:"Please Select Post Applied for"},
			'functional_areass[]':{required:"Please select Functional Areas"},
			spacific_subjects_area:{required:"Please Enter Specific Subject Area's Description",message_regex:"Please Enter Valid Specific Subject Area's Description"},
			applicant_image: {required: "<br>Please Select Applicant Image<br><br>",},
			applicant_name:{required:"Please Enter Full Name (In Block Latters)",common_regex:"Please Enter Valid Full Name (In Block Latters)"},
			applicant_dob:{required:"Please Enter Date of Birth",date_regex:"Please Enter Valid Date of Birth in the DD-MM-YYY Format"},
			applicant_father_name:{required:"Please Enter Father's / Spouse Name",common_regex:"Please Enter Valid Father's / Spouse Name"},
			candiate_corres_addr1:{required:"Please Enter Mailing Address",common_regex:"Please Enter Valid Mailing Address"},
			candiate_corres_addr2:{common_regex:"Please Enter Valid Mailing Address"},
			candiate_corres_pincode:{required:"Please Enter Mailing Address Pin Code",only_number_regx:"Please Enter Valid Mailing Address Pin Code",number:true,maxlength:"Please Enter Maximum 6 Digit in Pin Code"},
			candiate_corres_stdcode:{only_number_regx:"Please Enter Valid STD Code",number:"Please Enter Valid STD Code",maxlength:"Please Enter Maximum 5 Digit in STC Code"},
			candiate_corres_stdno:{only_number_regx:"Please Enter Valid Phone Number",number:"Please Enter Valid Phone Number",maxlength:"Please Enter Maximum 8 Digit in Phone Number"},
			candiate_permanent_addr1:{required:"Please Enter Permanent  Address",common_regex:"Please Enter Valid Permanent  Address"},
			candiate_permanent_addr2:{common_regex:"Please Enter Valid Permanent  Address"},
			candiate_permanent_pincode:{required:"Please Enter Permanent  Address Pin Code",only_number_regx:"Please Enter Valid Permanent  Address Pin Code",number:true,maxlength:"Please Enter Maximum 6 Digit in Pin Code"},
			candiate_permanent_stdcode:{only_number_regx:"Please Enter Valid STD Code",number:"Please Enter Valid STD Code",maxlength:"Please Enter Maximum 5 Digit in STC Code"},
			candiate_permanent_stdno:{only_number_regx:"Please Enter Valid Phone Number",number:"Please Enter Valid Phone Number",maxlength:"Please Enter Maximum 8 Digit in Phone Number"},
			candiate_corres_mobno:{required:"Please Enter Mobile Number",only_number_regx:"Please Enter Valid Mobile Number",minlength:"Please Enter Minimum 10 Digit Mobile Number",maxlength:"Please Enter only 10 Digit Mobile Number"},
			candiate_corres_emailid:{required:"Please Enter Email Address",email:"Please Enter Valid Email Address",regexmatchmail:"Please Enter Valid Email Address"},
			gender:{required:"Please Select Gender"},
			marital_status:{required:"Please Select Marital Status"},
			candiate_nationality:{required:"Please Enter Nationality",common_regex:"Please Enter Valid Nationality"},
			candiate_state:{required:"Please Select State of Domicile"},
			candidate_category:{required:"Please Select Category"},
			present_employer:{required:"Please Enter Present Employer",common_regex:"Please Enter Valid Present Employer"},
			phd_board_collage_university:{common_regex:"Please Enter Valid PhD. Board/College/University"},
			phd_percent_marks:{number:"Please Enter Valid PhD. % of Marks",only_number_regx:"Please Enter Valid PhD. % of Marks",},
			phd_subjects:{message_regex:"Please Enter Valid PhD Subjects"},
			pgard_board_collage_university:{required:"Please Enter Post Graduation Board/College/University",common_regex:"Please Enter Valid Post Graduation Board/College/University"},
			pgard_percent_marks:{number:"Please Enter Valid Post Graduation % of Marks",only_number_regx:"Please Enter Valid Post Graduation % of Marks",},
			pgard_subjects:{message_regex:"Please Enter Valid Post Graduation Subjects"},
			pgard_year:{},
			pgard_document_file:{},
			gard_board_collage_university:{common_regex:"Please Enter Valid  Graduation Board/College/University"},
			gard_percent_marks:{number:"Please Enter Valid  Graduation % of Marks",only_number_regx:"Please Enter Valid Graduation % of Marks",},
			gard_subjects:{message_regex:"Please Enter Valid Graduation Subjects"},
			gard_year:{},
			gard_document_file:{},
			xii_board_collage_university:{common_regex:"Please Enter Valid  XII Board/College/University"},
			xii_percent_marks:{number:"Please Enter Valid  XII % of Marks",only_number_regx:"Please Enter Valid XII % of Marks",},
			xii_subjects:{message_regex:"Please Enter Valid XII Subjects"},
			xii_year:{},
			xii_document_file:{},
			x_board_collage_university:{common_regex:"Please Enter Valid  X Board/College/University"},
			x_percent_marks:{number:"Please Enter Valid  X % of Marks",only_number_regx:"Please Enter Valid X % of Marks",},
			x_subjects:{message_regex:"Please Enter Valid X Subjects"},
			x_year:{},
			x_document_file:{},			
			other1_board_collage_university:{common_regex:"Please Enter Valid  Other Board/College/University"},
			other1_percent_marks:{number:"Please Enter Valid  Other % of Marks",only_number_regx:"Please Enter Valid Other % of Marks",},
			other1_subjects:{message_regex:"Please Enter Valid Other Subjects"},
			other2_board_collage_university:{common_regex:"Please Enter Valid  Other Board/College/University"},
			other2_percent_marks:{number:"Please Enter Valid  Other % of Marks",only_number_regx:"Please Enter Valid Other % of Marks",},
			other2_subjects:{message_regex:"Please Enter Valid Other Subjects"},
			
			
			phd_awared_status:{},
			title_of_phd_thesis:{message_regex:"Please Enter Valid Title of Ph.D. thesis awarded"},
			qualified_status:{},
			
			'name_of_employer_status[]':{common_regex:"Please Enter Valid Name of Employer/Status of Institute/University"},
			'designation[]':{common_regex:"Please Enter Valid Post held/Designation"},
			'workexp_from_month[]':{},
			'workexp_from_year[]':{},
			'workexp_to_mn[]':{},
			'workexp_to_yr[]':{},
			'basic_salary[]':{number:"Please Enter Valid Basic salary last drawn",only_number_regx:"Please Enter Valid Basic salary last drawn"},
			'nature_of_duties[]':{common_regex:"Please Enter Valid Nature of duties"},
			'experiance_cerficate_job[]':{required:"Please Enter Employment Experience Documents"},
			
			under_grad_exp_from:{date_regex:"Please Enter Valid From Under Graduate Experience/Performance in DD_MM_YYYY Format",},
			under_grad_exp_to:{date_regex:"Please Enter Valid To Under Graduate Experience/Performance in DD_MM_YYYY Format",},
			under_g_total_year:{},
			under_g_total_month:{},
			exper_cerf_under_g:{},
			
			
			under_pgrad_exp_from:{date_regex:"Please Enter Valid From Post Graduate Experience/Performance in DD_MM_YYYY Format",},
			under_pgrad_exp_to:{date_regex:"Please Enter Valid To Post Graduate Experience/Performance in DD_MM_YYYY Format",},
			under_pg_total_year:{},
			under_pg_total_month:{},
			exper_cerf_under_pg:{},
			
			
			tt_exp_from:{date_regex:"Please Enter Valid From Total Teaching Experience/Performance in DD_MM_YYYY Format",},
			tt_exp_to:{date_regex:"Please Enter Valid To Total Teaching Experience/Performance in DD_MM_YYYY Format",},
			tt_total_year:{},
			tt_total_month:{},
			exper_cerf_tt:{},
			
			
			
			spac_course_exp_from:{date_regex:"Please Enter Valid From Short term/Continuing Education/Specialist Courses conducted Experience/Performance in DD_MM_YYYY Format",},
			spac_course_exp_to:{date_regex:"Please Enter Valid To Short term/Continuing Education/Specialist Courses conducted Experience/Performance in DD_MM_YYYY Format",},
			spac_course_total_year:{},
			spac_course_total_month:{},
			exper_cerf_spac_course:{},
			
			
			research_exp_from:{date_regex:"Please Enter Valid From Research Experience/Performance in DD_MM_YYYY Format",},
			research_exp_to:{date_regex:"Please Enter Valid To Research Experience/Performance in DD_MM_YYYY Format",},
			research_total_year:{},
			research_total_month:{},
			exper_cerf_research:{},
			
			rofessional_development_desc:{message_regex:"Please Enter Valid Student related co-curricular, extension and field based activities and counseling"},
			admin_commit_desc:{message_regex:"Please Enter Valid Contribution to corporate sector-management of the department and institution through participation in academic and administrative committees and responsibilities"},
			
			'research_book_title[]':{common_regex:"Please Enter Valid Title of the Book(s)"},
			'research_book_author[]':{},
			'research_book_pub_cc[]':{message_regex:"Please Enter Name of Publisher (with city/country)"},
			'research_book_mn[]':{},
			'research_book_yr[]':{},
			'research_book_isbn_no[]':{common_regex:"Please Enter Valid ISBN/ISSN No"},
			'research_book_image[]':{},
			
			'research_book_1_chapter[]':{common_regex:"Please Enter Valid Title of Chapter(s)"},
			'research_book_1_title[]':{common_regex:"Please Enter Valid Title of the Book(s)"},
			'research_book_1_author[]':{},
			'research_book_1_pub_cc[]':{message_regex:"Please Enter Valid Name of Publisher (with city/country)"},
			'research_book_1_mn[]':{},
			'research_book_1_yr[]':{},
			'research_book_1_isbn_no[]':{common_regex:"Please Enter Valid Year of publication"},
			'research_book_1_image[]':{},
			
			'article_book_title[]':{common_regex:"Please Enter Valid Title of research article/paper(s)"},
			'name_of_journal[]':{common_regex:"Please Enter Valid Name of journal"},
			'article_book_author[]':{},
			'article_my_vol_page[]':{message_regex:"Please Enter Valid Month & year of publication, volume, no. & page nos."},
			'article_referred[]':{},
			'article_isbn_no[]':{common_regex:"Please Enter Valid ISBN/ISSN No."},
			'article_level[]':{common_regex:"Please Enter Valid Artical Level"},
			'article_rating[]':{common_regex:"Please Enter Valid NAAS Rating/Impact Factor"},
			'article_image[]':{},
			
			
			'project_title[]':{common_regex:"Please Enter Valid Title/ Subject of Research Project(s)"},
			'project_type[]':{},
			'project_date_comm[]':{date_regex:"Please Select Valid Date of commencement in the DD-MM-YYY",},
			'project_date_complete[]':{date_regex:"Please Select Valid Date of Completion in the DD-MM-YYY"},
			'project_funding_receive[]':{common_regex:"Please Enter Valid Total Grants/Funding received (Rs.)"},
			'peroject_funding_agency[]':{common_regex:"Please Enter Valid Name of Sponsoring/Funding Agency"},
			'project_outcome[]':{},
			'project_final_report[]':{common_regex:"Please Select final report published as monograph book"},
			'project_image[]':{},
			
			
			
			'paper_presented_title[]':{common_regex:"Please Enter Valid Title/Subject of paper presented"},
			'paper_presented_subject[]':{common_regex:"Please Enter Valid Subject of Conference/Seminar/Symposium/Workshop"},
			'paper_present_institute_info[]':{message_regex:"Please Enter Valid Organizing Institution/and Name of City/Country"},
			'paper_present_from[]':{date_regex:"Please Enter Valid Duration From in the DD-MM-YYY Format"},
			'paper_present_to[]':{date_regex:"Please Enter Valid Duration To in the DD-MM-YYY Format"},
			'paper_present_status[]':{},
			'paper_present_image[]':{},
			
			
			membership_institutions_info:{message_regex:"Please Enter Valid Membership/Fellowship of other institutions/professionals societies"},
			other_act_responsibiltities:{message_regex:"Please Enter Valid Other activities/Responsibilities"},
			other_relevant_information:{message_regex:"Please Enter Valid Any other relevant information"},
			any_break_academic:{message_regex:"Please Enter Valid any break in your academic career "},
			punished_in_collage:{message_regex:"Please Enter Valid punished during your studies at College/University "},
			punished_in_collage_by_law:{message_regex:"Please Enter  Valid punished during your services or convicted by a court of law "},
			medically_unfit_stauts:{message_regex:"Please Enter Valid Details"},
			reference_address_one:{message_regex:"Please Enter Valid Details"},
			reference_address_two:{message_regex:"Please Enter Valid Details"},
			desirable_qualifications:{message_regex:"Please Enter Valid Details"},
			place:{required:"Please Enter Details",message_regex:"Please Enter Valid Details"},
			appdate:{required:"Please Enter Details",date_regex:"Please Enter Details in DD-MM-YYYY Format"},
			employer_proof_document_compy:{},
			captcha:{required:"Enter Captcha Code",common_regex:"Enter Valid Captcha Code"}
			
		}

	});
	
	$('#registeration_form').validate({
		rules:{
			'ref_jour_title_page[]':{required:true},
			'ref_jour_name[]':{required:true},
			'ref_jour_issn_no[]':{required:true},
			'ref_jour_review_status[]':{required:true},
			'ref_jour_no_coauthor[]':{required:true},
			'ref_jour_main_auth[]':{required:true},
			'ref_jour_apiscore[]':{required:true},
			tr_row_ref_jour_count:{required:true},
			
			'reputed_jour_title_page[]':{required:true},
			'reputed_jour_name[]':{required:true},
			'reputed_jour_issn_no[]':{required:true},
			'reputed_jour_reviewedstatus[]':{required:true},
			'reputed_jour_noco_auth[]':{required:true},
			'reputed_jour_main_auth[]':{required:true},
			'reputed_jour_apiscore[]':{required:true},
			'confer_proce_title_page[]':{required:true},
			'confer_proce_details[]':{required:true},
			'confer_proce_issn_no[]':{required:true},
			'confer_proce_no_coauth[]':{required:true},
			'confer_proce_main_auth[]':{required:true},
			'confer_proce_apiscore[]':{required:true},
			
			'chap_pub_title_page[]':{required:true},
			'chap_pub_book_title[]':{required:true},
			'chap_pub_issn_no[]':{required:true},
			'chap_pub_reviewed_status[]':{required:true},
			'chap_pub_co_auth[]':{required:true},
			'chap_pub_main_auth[]':{required:true},
			'chap_pub_apiscore[]':{required:true},
			'books_pub_single_title_page[]':{required:true},
			'books_pub_single_type[]':{required:true},
			'books_pub_single_issn_no[]':{required:true},
			'books_pub_single_reviewed_status[]':{required:true},
			'books_pub_single_co_auth[]':{required:true},
			'books_pub_single_main_auth[]':{required:true},
			'books_pub_single_apiscore[]':{required:true},
			
			'ongoing_res_title[]':{required:true},
			'ongoing_res_agency[]':{required:true},
			'ongoing_res_period[]':{required:true},
			'ongoing_res_amount[]':{required:true},
			'ongoing_res_apiscore[]':{required:true},
			
			'projects_com_title[]':{required:true},
			'projects_com_agency[]':{required:true},
			'projects_com_period[]':{required:true},
			'projects_com_amount[]':{required:true},
			'projects_com_doc_status[]':{required:true},
			'projects_com_apiscore[]':{required:true},
			
			'research_guide_course[]':{required:true},
			'research_guide_enrol[]':{required:true},
			'research_guide_thesis[]':{required:true},
			'research_guide_degre_aw[]':{required:true},
			'research_guide_apiscore[]':{required:true},
			
			'faculty_deve_program[]':{required:true},
			'faculty_deve_duration[]':{required:true},
			'faculty_deve_orgnizer[]':{required:true},
			'faculty_deve_apiscore[]':{required:true},
			
			'workshops_present_papertitle[]':{required:true},
			'workshops_present_seminar_title[]':{required:true},
			'workshops_present_date[]':{required:true},
			'workshops_present_organize[]':{required:true},
			'workshops_present_level[]':{required:true},
			'workshops_present_apiscore[]':{required:true},
			
			'invited_lectures_papertitle[]':{required:true},
			'invited_lectures_seminar_title[]':{required:true},
			'invited_lectures_date[]':{required:true},
			'invited_lectures_organize[]':{required:true},
			'invited_lectures_level[]':{required:true},
			'invited_lectures_apiscore[]':{required:true},
			
			nature_activity_student:{required:true},
			nature_activity_administrative:{required:true},
			nature_activity_deve_act:{required:true},
			acti_total_score:{required:true},
			nature_activity1_tutorial:{required:true},
			nature_activity1_teaching:{required:true},
			nature_activity1_knowledge:{required:true},
			nature_activity1_participatory:{required:true},
			nature_activity1_exam_duties:{required:true},
			acti_total_score1:{required:true},
			
			
			
		},
		messages:{
			'ref_jour_title_page[]':{required:"Please Enter Research Papers published in Refereed  Title with page no."},
			'ref_jour_name[]':{required:"Please Enter Research Papers published in Refereed  Journal"},
			'ref_jour_issn_no[]':{required:"Please Enter Research Papers published in Refereed ISSN/ISBN No."},
			'ref_jour_no_coauthor[]':{required:"Please Enter Research Papers published in Refereed No. of Co- authors"},
			'ref_jour_main_auth[]':{required:"Please Enter Research Papers published in Refereed Whether you are the main author"},
			'ref_jour_apiscore[]':{required:"Please Enter Research Papers published in Refereed API Score"},
			
			'reputed_jour_title_page[]':{required:"Please Enter Research Papers published in Non-Refereed Title with page no."},
			'reputed_jour_name[]':{required:"Please Enter Research Papers published in Non-Refereed Journal"},
			'reputed_jour_issn_no[]':{required:"Please Enter Research Papers published in Non-Refereed ISSN/ISBN No."},
			'reputed_jour_noco_auth[]':{required:"Please Enter Research Papers published in Non-Refereed No. of Co- authors"},
			'reputed_jour_main_auth[]':{required:"Please Enter Research Papers published in Non-Refereed Whether you are the main author"},
			'reputed_jour_apiscore[]':{required:"Please Enter Research Papers published in Non-Refereed API Score"},
			
			
			'confer_proce_title_page[]':{required:"Please Enter Conference Proceedings as full papers Title with page no."},
			'confer_proce_details[]':{required:"Please Enter Conference Proceedings as full papers Details of conference publications"},
			'confer_proce_issn_no[]':{required:"Please Enter Conference Proceedings as full papers ISSN/ISBN No."},
			'confer_proce_no_coauth[]':{required:"Please Enter Conference Proceedings as full papers No. of Co- authors"},
			'confer_proce_main_auth[]':{required:"Please Enter Conference Proceedings as full papers Whether you are the main author"},
			'confer_proce_apiscore[]':{required:"Please Enter Conference Proceedings as full papers API Score"},
			
			'chap_pub_title_page[]':{required:"Please Enter Articles/Chapters published in books Title with page no."},
			'chap_pub_book_title[]':{required:"Please Enter Articles/Chapters published in books Book title editor & publisher"},
			'chap_pub_issn_no[]':{required:"Please Enter Articles/Chapters published in books ISSN/ISBN No."},
			'chap_pub_co_auth[]':{required:"Please Enter Articles/Chapters published in books No. of Co- authors"},
			'chap_pub_main_auth[]':{required:"Please Enter Articles/Chapters published in books Whether you are the main author"},
			'chap_pub_apiscore[]':{required:"Please Enter Articles/Chapters published in books API Score"},
			
			
			'books_pub_single_title_page[]':{required:"Please Enter Books published as single author or as editor Title with page no."},
			'books_pub_single_type[]':{required:"Please Enter Books published as single author or as editor Type of book and authorship"},
			'books_pub_single_issn_no[]':{required:"Please Enter Books published as single author or as editor ISSN/ISBN No."},
			'books_pub_single_co_auth[]':{required:"Please Enter Books published as single author or as editor No. of Co- authors"},
			'books_pub_single_main_auth[]':{required:"Please Enter Books published as single author or as editor Whether you are the main author"},
			'books_pub_single_apiscore[]':{required:"Please Enter Books published as single author or as editor API Score"},
			
			'ongoing_res_title[]':{required:"'Please Enter Ongoing Research Projects Title"},
			'ongoing_res_agency[]':{required:"Please Enter Ongoing Research Projects Agency"},
			'ongoing_res_period[]':{required:"Please Enter Ongoing Research Projects Period"},
			'ongoing_res_amount[]':{required:"Please Enter Ongoing Research Projects Grant /Amount Mobilized (Rs Lakhs)"},
			'ongoing_res_apiscore[]':{required:"Please Enter Ongoing Research Projects API Score"},
			
			
			'projects_com_title[]':{required:"Please Enter Projects Completed and Project Outcome Title"},
			'projects_com_agency[]':{required:"Please Enter Projects Completed and Project Outcome Agency"},
			'projects_com_period[]':{required:"Please Enter Projects Completed and Project Outcome Period"},
			'projects_com_amount[]':{required:"Please Enter Projects Completed and Project Outcome Grant /Amount Mobilized"},
			'projects_com_doc_status[]':{required:"Please Enter Projects Completed and Project Outcome Whether Policy Documents/Patent as outcome"},
			'projects_com_apiscore[]':{required:"Please Enter Projects Completed and Project Outcome API Score"},
			
			'research_guide_course[]':{required:"Please Enter Research Guidance Course"},
			'research_guide_enrol[]':{required:"Please Enter Research Guidance Number Enrolled"},
			'research_guide_thesis[]':{required:"Please Enter Research Guidance Thesis submitted"},
			'research_guide_degre_aw[]':{required:"Please Enter Research Guidance Degree awarded"},
			'research_guide_apiscore[]':{required:"Please Enter Research Guidance API Score"},
			
			'faculty_deve_program[]':{required:"Please Enter Refresher Courses Programme"},
			'faculty_deve_duration[]':{required:"Please Enter Refresher Courses  Duration"},
			'faculty_deve_orgnizer[]':{required:"Please Enter Refresher Courses Organized by"},
			'faculty_deve_apiscore[]':{required:"Please Enter Refresher Courses API Score"},
			
			'workshops_present_papertitle[]':{required:"Please Enter Papers presented in conferences Title of paper presented"},
			'workshops_present_seminar_title[]':{required:"Please Enter Papers presented in conferences  Title of conference/seminar"},
			'workshops_present_date[]':{required:"Please Enter Papers presented in conferences Date of event"},
			'workshops_present_organize[]':{required:"Please Enter Papers presented in conferences Organized by"},
			'workshops_present_level[]':{required:"Please Enter Papers presented in conferences Whether International/National/State/ Regional/University or College level"},
			'workshops_present_apiscore[]':{required:"Please Enter Papers presented in conferences API Score"},
			
			'invited_lectures_papertitle[]':{required:"Please Enter Invited Lectures and Chairmanships Title of paper presented"},
			'invited_lectures_seminar_title[]':{required:"Please Enter Invited Lectures and Chairmanships  Title of conference/seminar"},
			'invited_lectures_date[]':{required:"Please Enter Invited Lectures and Chairmanships Date of event"},
			'invited_lectures_organize[]':{required:"Please Enter Invited Lectures and Chairmanships Organized by"},
			'invited_lectures_level[]':{required:"Please Enter Invited Lectures and Chairmanships Whether International/National/State/ Regional/University or College level"},
			'invited_lectures_apiscore[]':{required:"Please Enter Invited Lectures and Chairmanships API Score"},
			
			nature_activity_student:{required:"Student related co-curricular, extension and field based activities (such as extension work through NSS/NCC and other channels, cultural activities, subject related events, advisement and counseling) can not be 0 and Blank"},
			nature_activity_administrative:{required:"Contribution to Corporate life and management of the department and institution through participation in academic and administrative committees and responsibilities can not be 0 and Blank"},
			nature_activity_deve_act:{required:"Professional Development activities (such as participation in seminars, conferences, short term, training courses, talks, lectures, membership of associations, dissemination and general articles, not covered in Category III) can not be 0 and Blank"},
			acti_total_score:{required:"Total Score can not be 0 and Blank"},
			nature_activity1_tutorial:{required:"Lectures, seminars, tutorials, Practicals, contact hours undertaken taken as percentage of lectures allocated can not be 0 and Blank"},
			nature_activity1_teaching:{required:"Lectures or other teaching duties in excess of the UGC Norms can not be 0 and Blank"},
			nature_activity1_knowledge:{required:"Preparation and Imparting of knowledge / instruction as per curriculum; syllabus enrichment by providing additional resources to students can not be 0 and Blank"},
			nature_activity1_participatory:{required:"Use of participatory and innovative teaching-learning methodologies; updating of subject content, course improvement etc can not be 0 and Blank"},
			nature_activity1_exam_duties:{required:"Examination duties (Invigilation; question paper setting, evaluation/assessment of answer scripts) as per allotment can not be 0 and Blank"},
			acti_total_score1:{required:"Total Score can not be 0 and Blank"},
			
			}

	});

});
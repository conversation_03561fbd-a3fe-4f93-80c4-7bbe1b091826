<title></title>

<style>
        /* General Styles */
body {
  font-family: Arial, sans-serif;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  background-color: #f7f7f7;
}

/* Workstation Section */
.workstation-section {
  padding: 40px 20px;
  background-color: #ffffff;
}

.workstation-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  background-color: #f9fafb;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  overflow: hidden;
}

.workstation-image img {
  width: 100%;
  height: auto;
  max-width: 500px;
  border-radius: 12px 0 0 12px;
}

.workstation-content {
  padding: 20px;
  flex: 1;
  text-align: left;
}

.workstation-content h2 {
  font-size: 24px;
  color: #007bff;
  margin-bottom: 10px;
}

.workstation-content .description {
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 10px;
  color: #333;
}

.info-box {
  margin-top: 20px;
  padding: 15px;
  background-color: #eef6ff;
  border-left: 4px solid #007bff;
  border-radius: 8px;
}

.info-box p {
  font-size: 16px;
  color: #0056b3;
}

.info-box strong {
  color: #004080;
}

/* Responsive Design */
@media (max-width: 768px) {
  .workstation-container {
    flex-direction: column;
  }

  .workstation-image img {
    border-radius: 12px 12px 0 0;
  }

  .workstation-content {
    padding: 20px;
    text-align: center;
  }

  .workstation-content h2 {
    font-size: 22px;
  }
}

@media (max-width: 480px) {
  .workstation-content h2 {
    font-size: 20px;
  }

  .workstation-content .description {
    font-size: 14px;
  }

  .info-box p {
    font-size: 14px;
  }
}

</style>

<div class="workstation-section">
    <div class="workstation-container">
      <div class="workstation-image">
        <img src="https://example.com/census-workstation.jpg" alt="Census Workstation Image" />
      </div>
      <div class="workstation-content">
        <h2>Workstation for Research on Sample Micro-data from Census</h2>
        <p class="description">
          A Workstation for Research on Sample Micro-data from Census has been set up at the Institute with support from the office of the Registrar General of India, Ministry of Home Affairs, Government of India, New Delhi and the ICSSR New Delhi.
        </p>
        <p class="description">
          Dr. Biplab Dhak is In-charge and provides the required services pertaining to Census Micro-data and analysis for researchers on requisition.
        </p>
        <p class="description">
          Sri Krishna Nadan Prasad Verma, Hon’ble Minister, Department of Education, Government of Bihar-cum-chairman A N Sinha Institute of Social Studies, Patna, inaugurated the Census Workstation on November 8, 2017. It is a unique and very important facility that will greatly benefit researchers in this region.
        </p>
        <div class="info-box">
          <p><strong>Inaugurated by:</strong> Sri Krishna Nadan Prasad Verma</p>
          <p><strong>Date of Inauguration:</strong> November 8, 2017</p>
        </div>
      </div>
    </div>
  </div>
  
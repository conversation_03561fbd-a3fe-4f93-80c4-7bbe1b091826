<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>ANSISS - A.N. Sinha Institute of Social Studies</title>
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css"
      integrity="sha512-SfTiTlX6kk+qitfevl/7LibUOeJWlt9rbyDn92a1DqWOw9vWG2MFoays0sgObmWazO5BQPiFucnnEAjpAB+/Sw=="
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link href="/css/bootstrap.min.css" rel="stylesheet" />
    <link href="/css/owl.carousel.min.css" rel="stylesheet" />
    <link href="/css/owl.theme.default.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="/css/style.css" />
    <link rel="icon" type="image/png" href="/img/LOGO1.png" />
    <!-- Include SPA Router -->
    <script src="/routes/simple-router.js" defer></script>
    <style>
      body{
          background: #fff!important;
      }
         html body{font-family: "Poppins", sans-serif!important;}
         .dropdown:hover,
         .navigation li:hover{
             color:#f96126;
         }
         .navigation li{padding: 6px 0;}
         .dropdown:hover .dropdown-menu{
             display: block!important;
         }
         .sub-dropdown-menu {
             display: none;
             position: absolute;
             right: 0;
             top: 0!important;
             background: rgba(0, 0, 0, 0.7);
             width: 100%;
             padding: 6px 0;
             list-style: none;
             margin: 0;
             border-radius: 5px;
         }
         .sub-dropdown:hover .sub-dropdown-menu{
             display: block;
         }
         .sub-dropdown-menu li {
             padding: 10px 0;
         }
         .sub-dropdown-menu li a {
             color: #ffffff;
             text-decoration: none;
             padding: 0px 20px;
         }
         .sub-dropdown a {
             display: flex!important;
             justify-content: space-between;
             align-items: center;
         }
         .logo-section {
             padding: 10px 0;
             background-color: #f8f9fa;
         }
         .logo-area {
             display: flex;
             align-items: center;
             justify-content: space-between;
         }
         .logo img, .logo-right img {
             max-width: 100%;
             height: auto;
         }
         .logo-text {
             text-align: center;
         }
         .logo-text h3 {
             font-size: 1.5rem;
             margin: 0;
         }
         .logo-text h2 {
             font-size: 1.2rem;
             margin: 0;
         }
         .top-header {
             background-color: #f8f9fa;
             padding: 10px 0;
         }
         .top-menus ul.top-menu-list {
             display: flex;
             justify-content: space-between;
             align-items: center;
             list-style: none;
             padding: 0;
             margin: 0;
         }
         .top-menu-item {
             margin: 0 10px;
         }
         .top-menu-item a {
             color: #333;
             text-decoration: none;
             font-size: 14px;
         }
         .social-icons-list {
             display: flex;
             list-style: none;
             padding: 0;
             margin: 0;
         }
         .social-icons-list li {
             margin: 0 5px;
         }
         .social-button {
             color: #fff;
             background-color: #007bff;
             padding: 5px 10px;
             border-radius: 50%;
             display: flex;
             align-items: center;
             justify-content: center;
             width: 30px;
             height: 30px;
             text-decoration: none;
         }
         .social-button:hover {
             background-color: #0056b3;
         }
         .dropdown-menu li a{
             color:black!important;
         }
         .dropdown-menu li a:hover{
             color:#f96126!important;
         }
         /* Responsive Styles */
         @media (max-width: 768px) {
             .logo-area {
                 flex-direction: column;
                 align-items: center;
             }
             .logo, .logo-right {
                 text-align: center;
                 margin-bottom: 10px;
             }
             .logo-text h3 {
                 font-size: 1.2rem;
             }
             .logo-text h2 {
                 font-size: 1rem;
             }
             .logo-right {
                 order: -1;
             }
             .top-menus ul.top-menu-list {
                 flex-direction: column;
                 align-items: flex-start;
             }
             .top-menu-item {
                 margin: 5px 0;
             }
             .social-icons {
                 margin-top: 10px;
             }
             .social-icons-list {
                 justify-content: flex-start;
             }
         }
    </style>
  </head>
  
  <body>
    <section class="top-header">
      <div class="container-fluid">
          <div class="row">
              <div class="col-md-12 col-lg-12 col-sm-12 col-12">
                  <div class="top-menus">
                      <ul class="top-menu-list">
                          <li class="top-menu-item">
                              <a href="#"><i class="fa fa-phone" aria-hidden="true"></i> +91-0612-2219395, 2219226</a>
                          </li>
                          <li class="top-menu-item">
                              <a href="#"><i class="fa fa-envelope" aria-hidden="true"></i><EMAIL></a>
                          </li>
                          <li class="top-menu-item"><a href="#">Skip to main content</a></li>
                          <li class="top-menu-item"><a href="#">Screen Reader Access</a></li>
                          <li class="social-icons">
                              <ul class="social-icons-list">
                                  <li>
                                  <i class="fab fa-twitter"></i>
                                  </li>
                                  <li>
                                  <i class="fab fa-linkedin-in"></i>
                                  </li>
                                  <li>
                                  <i class="fab fa-facebook-f"></i>
                                  </li>
                                  <li>
                                      <a href="https://webmail.ansiss.res.in" class="social-button social-button--webmail"
                                          aria-label="Webmail">
                                          <i class="fas fa-envelope"></i>
                                      </a>
                                  </li>
                                  <li class="nav-item" id="google_translate_element"></li>
                              </ul>
                          </li>
                      </ul>
                  </div>
              </div>
          </div>
      </div>
  </section>
    <header class="logo-section">
      <div class="container-fluid">
          <div class="row">
              <div class="logo-area">
                  <div class="col-md-2 col-lg-2 col-sm-2 col-12">
                      <div class="logo">
                          <a href="/">
                              <img src="/img/LOGO1.png" alt="img-logo" class="img-fluid"
                                  style="filter: drop-shadow(0 0 0.5rem rgb(253, 253, 253));" />
                          </a>
                      </div>
                  </div>
                  <div class="col-md-8 col-lg-8 col-sm-8 col-12">
                      <div class="logo-text">
                          <h3>अनुग्रह नारायण सिंह समाज अध्ययन संस्थान</h3>
                          <h2 id="name-eng">A N Sinha Institute of Social Studies</h2>
                      </div>
                  </div>
                  <div class="col-md-2 col-lg-2 col-sm-2 col-6">
                      <div class="logo-right">
                          <img src="/img/directorhd.png" alt="img-logo" class="img-fluid" />
                      </div>
                  </div>
              </div>
          </div>
      </div>
  </header>
		
    <section class="navMenu">
        <div class="container">
          <div class="row">
            <nav class="navbar navbar-expand-lg">
               <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarTogglerDemo01" aria-controls="navbarTogglerDemo01" aria-expanded="false" aria-label="Toggle navigation">
                <i class="fas fa-bars"></i>
              </button>
             <div class="collapse navbar-collapse" id="bs-example-navbar-collapse-1">
              <div class="menu-primary-menu-container"><ul class="nav navbar-nav navigation main-nav"><li class=''><a class="dropdown-item" href="/">Home</a></li>
                <li class='dropdown'><a class="dropdown-toggle all-toggle" href="javascript:void(0)">About Us</a>
                <ul class="dropdown-menu">
                <li class=''><a class="dropdown-item" href="/about">About Us</a></li>
                <li class=''><a class="dropdown-item" href="/bocmembers">BOC Members</a></li>
                <li class=''><a class="dropdown-item" href="/facultymember">Faculty Members</a></li>
                <li class=''><a class="dropdown-item" href="/doctoral_fellow">PHD Scholar</a></li>
                <li class=''><a class="dropdown-item" href="/director_tenior">Director Tenure</a></li>
                <li class=''><a class="dropdown-item" href="/Director_desk">Director Desk</a></li>
                <li class=''><a class="dropdown-item" href="/administration">Administration</a></li>
                </ul>
                </li>
                <li class='dropdown'><a class="dropdown-toggle all-toggle" href="javascript:void(0)">Infrastructure</a>
                <ul class="dropdown-menu">
                <li class=''><a class="dropdown-item" href="/campus">Building & Campus</a></li>
                <li class=''><a class="dropdown-item" href="/ansissLibrary">ANSISS Library</a></li>
                <li class=''><a class="dropdown-item" href="http://eg4.nic.in/CGOVLIB1/OPAC/Default.aspx?LIB_CODE=ANSISS">ANSISS e-Library</a></li>
                <li class=''><a class="dropdown-item" href="/computerlab">Computer Centre</a></li>
                <li class=''><a class="dropdown-item" href="/facilites">ANSISS Facilities</a></li>
                </ul>
                </li>
                <li class='dropdown'><a class="dropdown-toggle all-toggle" href="javascript:void(0)">Activities</a>
                <ul class="dropdown-menu">
                <li class=''><a class="dropdown-item" href="/Phd_Program">Ph. D Programme</a></li>
                <li class=''><a class="dropdown-item" href="/e_Content">e-Content</a></li>
                <li class=''><a class="dropdown-item" href="/completed_project">Completed Projects</a></li>
                <li class=''><a class="dropdown-item" href="/OnGoingProject">On Going Projects</a></li>
                <li class=''><a class="dropdown-item" href="/Workshop">Workshop/Training</a></li>
                <li class=''><a class="dropdown-item" href="/Dialogue_series">Dialogue Series</a></li>
                <li class=''><a class="dropdown-item" href="/internal_seminar">Internal Seminars</a></li>
                <li class=''><a class="dropdown-item" href="/workstation">Work Station</a></li>
                </ul>
                </li>
                <li class='dropdown'><a class="dropdown-toggle all-toggle" href="javascript:void(0)">Publications</a>
                <ul class="dropdown-menu">
                <li class=''><a class="dropdown-item" href="/annualReport">Annual Report</a></li>
                <li class=''><a class="dropdown-item" href="/workingPaper">Working Paper</a></li>
                <li class=''><a class="dropdown-item" href="/newsLetter">Newsletters</a></li>
                </ul>
                </li>
                <li class=''><a class="dropdown-item" href="/journal_about">Journal</a></li>
                <li class=''><a class="dropdown-item" href="/carrier-view">Career</a></li>
                <li class=''><a class="dropdown-item" href="/gallary">Gallery<img style="width: 40px; display: inline-block;" src="/images/new-gif-image.gif" /></a></li>
</ul></div>                    
                 </div>
            </nav>
          </div>
        </div>
      </section>
    <main class="main-content">
        <div class="wrapperbg">
    <div class="header-top">
   
        <div class="container">
        <div class="logo"><a href="../"><img src="img/logo.png" /></a></div>
      </div>
   
  </div>
    
    <!--====================header over============================-->
    
    <div class="container">
    <div class="form-heading">
        <h2>APPLICATION FORM FOR FACULTY POSITIONS( Registrar  AND Administrator)</h2>
        <span style="background-color:#14855a; padding:10px 20px; color:#000; font-weight:bold; border-radius:5px;">(FILL UP SEPARATE FORM FOR EACH POST)</span><br /><br />

        <span>(Note:- Please go through instructions given on the website <a href="http://ansiss.res.in/">http://ansiss.res.in/</a> carefully before filling –up the Application Form)</span><br /><br />
        <span style="font-size: large ; color: green;">(Note: Kindly Contact On this Number &nbsp;<span style="color: #F00;"> 6201594413</span> &nbsp; Email:<EMAIL> &nbsp;If you Have Any Technical Problem )</span><br /><br />
        <span style="font-size: large ; color: rgb(76, 0, 255);">(Note:-Please Re-upload All Document Files When You Search Details By application number Then go For Final Submit) </span><br /><br />
    </div>
     
      <div class="form-top-content" style="width:100% !important;"></div>
      <div class="form-top">

      <!-- Question: Do you have an application number? -->
      <div class="form-group">
        
          <label >Do you have any application number ? <em>*</em></label>
          <input type="radio" name="haveApplicationNo" value="Yes" id="haveApplicationNoYes"  > Yes
          <input type="radio" name="haveApplicationNo" value="no" id="haveApplicationNoNo"  > No
      </div>
  
      <!-- Search Form by Application No -->
      <div id="searchForm" style="display: none; margin-top: 20px;">
        <div class="form-top">
            <div class="form-top-content" style="display: flex; ">
                <!-- Added mobile number and email id fields -->
                <div class=" col-md-6 input-container" style="flex: 2; margin-right: 10px;">
                    <label for="applicationnosearch" style="font-size: large;">Application Number <em>*</em></label>
                    <input type="text" class="form-control" id="applicationnosearch" style="width: 100%; height: 25px;" required>
                </div>

                <div class=" col-md-6 input-container" style="flex: 2; margin-left: 20px;">
                    <label for="mobilesearch" style="font-size: large;">Mobile No <em>*</em></label>
                    <input type="text" class="form-control" id="mobilesearch" style="width: 100%; height: 25px;" required>
                </div>

                <div style="flex: 2; margin-left: 60px;">
                    <button type="submit" id="applicationbuttonsearch" style="padding: 10px 20px 20px; margin-top: 17px; background-color: #14855a; color: white; border: none; border-radius: 5px; cursor: pointer; height: 40px;">Search Deatails</button>
                </div>
            </div>
        </div>
      </div>
  
      <!-- Detailed Form -->
      <div id="detailedForm" style="display: none; margin-top: 20px;">
          <div class="form-top">
              <div class="form-top-content" style="display: flex; ">
                  <!-- Added mobile number and email id fields -->
                  <div class=" col-md-6 input-container" style="flex: 2; margin-right: 10px;">
                      <label for="mobile" style="font-size: large;">Mobile Number <em>*</em></label>
                      <input type="text" class="form-control" id="mobile" style="width: 100%; height: 25px;" required>
                  </div>
  
                  <div class=" col-md-6 input-container" style="flex: 2; margin-left: 20px;">
                      <label for="name" style="font-size: large;">Full Name <em>*</em></label>
                      <input type="text" class="form-control" id="name" style="width: 100%; height: 25px;" required>
                  </div>
  
                  <div style="flex: 2; margin-left: 60px;">
                      <button type="submit" name="applicationbutton" id="applicationbutton" style="padding: 10px 20px 20px; margin-top: 17px; background-color: #14855a; color: white; border: none; border-radius: 5px; cursor: pointer; height: 40px;">Generate Application No.</button>
                  </div>
              </div>
          </div>
      </div>
    
         <div class="form-top-content" style="width:100% !important;">
			            </div>
        <div class="form-top" style="margin-bottom:10px;">        
        <div class="form-top-content">
            <label for="name" style="font-size: larger; margin-left: 7px; padding: 20px;" > Genearted Application Number: <span id="applicationnogenearted"> </span></label>
            <ul>
            <li class="left"><span style="color:#f00;font-size: 15px;"> * </span> Advertisement Number</li>
            <li>
                <input name="adv_number" id="adv_number" class="check" type="text" placeholder="Advertisement Number" value="Advt No NEW" readonly="readonly" >
              </li>

              <form method="post" enctype="multipart/form-data"  name="rec_registeration_form" id="rec_registeration_form" >
            <li style="width:100%;clear:both;"></li>
            <li class="left"><span style="color:#f00;font-size: 15px;"> * </span> Post Applied for </li>
            <li>
                <select name="postAppliedFor" id="postAppliedFor" style="width: 103%;padding: 8px 5px;border-radius: 5px; background: #fff;   border: 1px solid #aaa;font-size: 13px;" >
                	<option value="">Select Post</option>
                    <option  value="Registrar">Registrar</option>
                    <option  value="Administrator">Administrator</option>
                  
                </select>
              </li>
             
            <li class="left"><span style="color:#f00;font-size: 15px;"> * </span> Specific  Subject Area's Description</li>
            <li>
                <textarea style=" resize:none;width: 100%;float: left;padding: 8px 5px;border-radius: 5px;background: #fff;border: 1px solid #ccc;font-size: 13px; min-height:100px;" name="specificSubject" id="specificSubject" placeholder="Specific Subject Area's Description"  ></textarea>
              </li>
            <li style="width:100%;clear:both;"></li>
          </ul>
          </div>
        <div class="profile-pic" style="position:relative; right:110px;"> <img src="" alt="" /> <p style="position: relative;top: 20px;left: 158px;">Upload a Recent Passport size Photograph</p><input type="hidden" id="imageDocPath" name="imageDocPath"></div>
        <br />
        <p style="float: right;margin-left: 20px; width:250px;"><input type="file" name="imageDoc" id="imageDoc"  /><span style="color:#F00;">(Passport Size Image Upto 20kb)<br />(Image Must be in .jpg format)</span></p>
      </div>
        <div class="form-top" style="padding-top:10px;">        
          <div class="form-top-content">
            <h3 style="margin-left: 20px;">SECTION - A : GENERAL</h3>
            <ul>
                <li class="left"><span style="color:#f00;font-size: 15px;"> * </span> Full Name (In Block Latters)</li>
                <li>
                   
                    <input name="fullName" id="fullName" type="text" placeholder="Full Name" value="" style="width:75%; margin-left:5px; padding-bottom:10px;" >
                </li>
                <li style="width:100%;clear:both;"></li>
                <li class="left"><span style="color:#f00;font-size: 15px;"> * </span> Date Of Birth</li>
                <li>
                    <input name="dateOfBirth" type="text" id="dateOfBirth"  placeholder="DD/MM/YYYY" value="" >
                </li>
                <li style="width:100%;clear:both;"></li>
                <li class="left"><span style="color:#f00;font-size: 15px;"> * </span> Father's / Spouse  Name</li>
                <li>
                    <input name="fatherOrSpouseName" id="fatherOrSpouseName" placeholder="Father's Name" type="text" value="" >
                </li>
                <li style="width:100%;clear:both;"></li>
            </ul>
        </div>
        
        <!--    =============================form top over===================-->
        
        <div class="form-part1">
            <h3>Address for Mailing</h3>
            <ul>
                <li class="left"><span style="color:#f00;font-size: 15px;"> * </span> Address Line1: </li>
                <li>
                    <input name="mailingAddressLine1" id="mailingAddressLine1" placeholder="Address Line1" type="text" value="" >
                </li>
                <li style="width:100%;clear:both;"></li>
                <li class="left">Address Line2: </li>
                <li>
                    <input type="text" name="mailingAddressLine2" id="mailingAddressLine2" placeholder="Address Line2" value="" >
                </li>
                <li style="width:100%;clear:both;"></li>
                <li class="left"> <span style="color:#f00;font-size: 15px;"> * </span>Pin Code: </li>
                <li>
                    <input type="number" name="mailingPinCode" id="mailingPinCode" placeholder="Pin Code" value="" >
                </li>
                <li style="width:100%;clear:both;"></li>
                <li class="left">Telephone No.(With STD Code) </li>
                <li>
                  
                    <input name="mailingTelephoneNumber" id="mailingTelephoneNumber" class="std-no" placeholder="Phone No" type="number" value="" >
                </li>
                <li style="width:100%;clear:both;"></li>
            </ul>
            <div style="clear: both;"></div>
            <div class="form-check" style="margin-left: 5px; width: 99%;">
                <input name="form_check" id="form_check" type="checkbox" />
                If your permanent address is same as correspondence address, than please click here.</div>
            <div style="clear: both;"></div>
        </div>
        <div class="form-part1 form-right">
            <h3>Permanent Address</h3>
            <ul>
                <li class="left"> <span style="color:#f00;font-size: 15px;"> * </span>Address Line1: </li>
                <li>
                    <input name="permanentAddressLine1" id="permanentAddressLine1" placeholder="Address Line1" type="text" value="" >
                </li>
                <li style="width:100%;clear:both;"></li>
                <li class="left">Address Line2: </li>
                <li>
                    <input type="text" name="permanentAddressLine2" id="permanentAddressLine2" placeholder="Address Line2" value="" >
                </li>
                <li style="width:100%;clear:both;"></li>
                <li class="left"><span style="color:#f00;font-size: 15px;"> * </span> Pin Code: </li>
                <li>
                    <input name="permanentPinCode" id="permanentPinCode" type="number" placeholder="Pin Code" value="" />
                </li>
                <li style="width:100%;clear:both;"></li>
                <li class="left">Telephone No.(With STD Code) </li>
                <li>
                    <!-- <input name="permanentTelephoneNumber_stdCode" id="permanentTelephoneNumber_stdCode" class="std-code" type="number" placeholder="Std Code" /> -->
                    <input name="permanentTelephoneNumber" id="permanentTelephoneNumber" class="std-no" placeholder="Phone No" type="number" value="" />
                </li>
            </ul>
        </div>
        <table class="form-tb1" cellpadding="10" cellspacing="0" width="100%;">
            <tbody>
                <tr>
                    <td colspan="4"><h3>Contact Deatils</h3></td>
                </tr>
                <tr>
                    <td width="15.5%" align="left;"><span style="color:#f00;font-size: 15px;"> * </span>Mobile No. </td>
                    <td width="32.4%" align="right;"><input type="text" value="+91" readonly="readonly" style="width:30px; float:left;" /><input name="mobileNo" id="mobileNo" type="number" maxlength="10" placeholder="Mobile No" value="" style="float:left; width:80%;" ></td>
                    <td width="20%" align="left;"><span style="color:#f00;font-size: 15px;"> * </span> Email Address</td>
                    <td align="right;"><input name="emailAddress" id="emailAddress" placeholder="Email Address" type="text" value="" ></td>
                </tr>
            </tbody>
        </table>
        <table class="form-tb4" cellpadding="10" cellspacing="0" width="100%;">
            <tbody>
                <tr>
                    <td width="15.5%" align="left"><span style="color:#f00;font-size: 15px;"> * </span> Gender </td>
                    <td width="32.4%" align="left"><input name="gender" id="gender_male" value="Male" type="radio" />
                        Male
                        <input name="gender" id="gender_female" value="Female" type="radio" />
                        Female </td>
                    <td width="20%" align="left"><span style="color:#f00;font-size: 15px;"> * </span>Marital Status </td>
                    <td align="left;"><select name="maritalStatus" id="maritalStatus">
                        <option value="">---Select Marital Status ----</option>
                        <option value="married">Married</option>
                        <option value="unmarried">Unmarried</option>
                    </select></td>
                </tr>
                <tr>
                    <td align="left"><span style="color:#f00;font-size: 15px;"> * </span> Nationality </td>
                    <td align="left"><input name="nationality" id="nationality" type="text" placeholder="Nationality" value="" style="width: 90%; padding: 8px 5px; border-radius: 5px; background: #fff; border: 1px solid #aaa;font-size: 13px;" ></td>
                    <td align="left">State of Domicile <span style="color:#f00;font-size: 15px;"> * </span></td>
                    <td align="left;"><select name="stateOfDomicile" id="stateOfDomicile">
                        <option value=""> Select State</option>
                        <option value="Andaman and Nicobar Islands">Andaman and Nicobar Islands</option>
                        <option value="Andhra Pradesh">Andhra Pradesh</option>
                        <option value="Arunachal Pradesh">Arunachal Pradesh</option>
                        <option value="Assam">Assam</option>
                        <option value="Bihar">Bihar</option>
                        <option value="Chandigarh">Chandigarh</option>
                        <option value="Chhattisgarh">Chhattisgarh</option>
                        <option value="Dadra and Nagar Haveli">Dadra and Nagar Haveli</option>
                        <option value="Daman and Diu">Daman and Diu</option>
                        <option value="Delhi">Delhi</option>
                        <option value="Goa">Goa</option>
                        <option value="Gujarat">Gujarat</option>
                        <option value="Haryana">Haryana</option>
                        <option value="Himachal Pradesh">Himachal Pradesh</option>
                        <option value="Jammu and Kashmir">Jammu and Kashmir</option>
                        <option value="Jharkhand">Jharkhand</option>
                        <option value="Karnataka">Karnataka</option>
                        <option value="Kerala">Kerala</option>
                        <option value="Lakshadweep">Lakshadweep</option>
                        <option value="Madhya Pradesh">Madhya Pradesh</option>
                        <option value="Maharashtra">Maharashtra</option>
                        <option value="Manipur">Manipur</option>
                        <option value="Meghalaya">Meghalaya</option>
                        <option value="Mizoram">Mizoram</option>
                        <option value="Nagaland">Nagaland</option>
                        <option value="Odisha">Odisha</option>
                        <option value="Pondicherry">Pondicherry</option>
                        <option value="Punjab">Punjab</option>
                        <option value="Rajasthan">Rajasthan</option>
                        <option value="Sikkim">Sikkim</option>
                        <option value="Tamil Nadu">Tamil Nadu</option>
                        <option value="Telangana">Telangana</option>
                        <option value="Tripura">Tripura</option>
                        <option value="Uttar Pradesh">Uttar Pradesh</option>
                        <option value="Uttarakhand">Uttarakhand</option>
                        <option value="West Bengal">West Bengal</option>
                    </select></td>
                </tr>
                <tr>
                    <td align="left">Category <span style="color:#f00;font-size: 15px;"> * </span></td>
                    <td align="left" colspan="3"><select name="category" id="category" style="float:left; width:30%;" >
                        <option value="">---Select Category----</option>
                        <option value="SC">SC</option>
                        <option value="ST">ST</option>
                        <option value="OBC">OBC</option>
                        <option value="General">General</option>
                        <option value="PWD">PWD</option>
                    </select>
                 
                    </td>
                </tr>
                <tr>
                    <td align="left"><span style="color:#f00;font-size: 15px;"> * </span> Present Employer </td>
                    <td align="left" colspan="3"><input type="text" name="presentEmployer" id="presentEmployer" placeholder="Present Employer" style="width: 96%; padding: 8px 5px; border-radius: 5px; background: #fff; border: 1px solid #aaa;font-size: 13px;" value="" ></td>
                </tr>
            </tbody>
        </table>
        
        <table class="form-tb1" cellpadding="10" cellspacing="0"  width="100%;">
        <tbody>
        	<tr>
            	<td colspan="7"><h3 style="text-align:left; margin-bottom:5px;">Section B :</h3><span style="font-weight:bold;">Category  I : Teaching, Learning And  Evaluation  Relation Activities</span></td>
            </tr>
            <tr>
            <td colspan="7"><h3>Educational Qualifications<span style="color:#f00;font-size: 15px;"> * </span></h3></td>
          </tr>
            <tr>
            <td align="center"><b>Examination / Degree</b></td>
            <td align="center" width="50"><b>Name of Board/College/University</b></td>
            <td align="center"><b>% of Marks</b></td>
            <td align="center"><b>Subject (s)</b></td>
            <td align="center"><b>Year of Passing/award</b></td>
            <td align="center"><b>Supporting Documents</b></td>
          </tr>
         <tr>
          <td align="center">PhD</td>
          <td align="center"><input type="text" name="phdBoardCollegeUniversity" id="phdBoardCollegeUniversity" class="form-tb-input1" placeholder="Name of Board/College/University"  value="" ></td>
          <td align="center"><input type="number" name="phdPercentageOfMarks" id="phdPercentageOfMarks" class="form-tb-input2" placeholder="% of Marks"   value="" ></td>
          <td align="center"><textarea style="resize: none;" type="text" name="phdSubjects" id="phdSubjects" class="form-tb-input2" placeholder="Subjects" ></textarea></td>
          <td align="center"><select name="phdYearOfPassingAward" id="phdYearOfPassingAward" class="form-tb-input1">
                <option value="">Select Year</option>
                                <option value="1865"  >1865</option>
                                <option value="1866"  >1866</option>
                                <option value="1867"  >1867</option>
                                <option value="1868"  >1868</option>
                                <option value="1869"  >1869</option>
                                <option value="1870"  >1870</option>
                                <option value="1871"  >1871</option>
                                <option value="1872"  >1872</option>
                                <option value="1873"  >1873</option>
                                <option value="1874"  >1874</option>
                                <option value="1875"  >1875</option>
                                <option value="1876"  >1876</option>
                                <option value="1877"  >1877</option>
                                <option value="1878"  >1878</option>
                                <option value="1879"  >1879</option>
                                <option value="1880"  >1880</option>
                                <option value="1881"  >1881</option>
                                <option value="1882"  >1882</option>
                                <option value="1883"  >1883</option>
                                <option value="1884"  >1884</option>
                                <option value="1885"  >1885</option>
                                <option value="1886"  >1886</option>
                                <option value="1887"  >1887</option>
                                <option value="1888"  >1888</option>
                                <option value="1889"  >1889</option>
                                <option value="1890"  >1890</option>
                                <option value="1891"  >1891</option>
                                <option value="1892"  >1892</option>
                                <option value="1893"  >1893</option>
                                <option value="1894"  >1894</option>
                                <option value="1895"  >1895</option>
                                <option value="1896"  >1896</option>
                                <option value="1897"  >1897</option>
                                <option value="1898"  >1898</option>
                                <option value="1899"  >1899</option>
                                <option value="1900"  >1900</option>
                                <option value="1901"  >1901</option>
                                <option value="1902"  >1902</option>
                                <option value="1903"  >1903</option>
                                <option value="1904"  >1904</option>
                                <option value="1905"  >1905</option>
                                <option value="1906"  >1906</option>
                                <option value="1907"  >1907</option>
                                <option value="1908"  >1908</option>
                                <option value="1909"  >1909</option>
                                <option value="1910"  >1910</option>
                                <option value="1911"  >1911</option>
                                <option value="1912"  >1912</option>
                                <option value="1913"  >1913</option>
                                <option value="1914"  >1914</option>
                                <option value="1915"  >1915</option>
                                <option value="1916"  >1916</option>
                                <option value="1917"  >1917</option>
                                <option value="1918"  >1918</option>
                                <option value="1919"  >1919</option>
                                <option value="1920"  >1920</option>
                                <option value="1921"  >1921</option>
                                <option value="1922"  >1922</option>
                                <option value="1923"  >1923</option>
                                <option value="1924"  >1924</option>
                                <option value="1925"  >1925</option>
                                <option value="1926"  >1926</option>
                                <option value="1927"  >1927</option>
                                <option value="1928"  >1928</option>
                                <option value="1929"  >1929</option>
                                <option value="1930"  >1930</option>
                                <option value="1931"  >1931</option>
                                <option value="1932"  >1932</option>
                                <option value="1933"  >1933</option>
                                <option value="1934"  >1934</option>
                                <option value="1935"  >1935</option>
                                <option value="1936"  >1936</option>
                                <option value="1937"  >1937</option>
                                <option value="1938"  >1938</option>
                                <option value="1939"  >1939</option>
                                <option value="1940"  >1940</option>
                                <option value="1941"  >1941</option>
                                <option value="1942"  >1942</option>
                                <option value="1943"  >1943</option>
                                <option value="1944"  >1944</option>
                                <option value="1945"  >1945</option>
                                <option value="1946"  >1946</option>
                                <option value="1947"  >1947</option>
                                <option value="1948"  >1948</option>
                                <option value="1949"  >1949</option>
                                <option value="1950"  >1950</option>
                                <option value="1951"  >1951</option>
                                <option value="1952"  >1952</option>
                                <option value="1953"  >1953</option>
                                <option value="1954"  >1954</option>
                                <option value="1955"  >1955</option>
                                <option value="1956"  >1956</option>
                                <option value="1957"  >1957</option>
                                <option value="1958"  >1958</option>
                                <option value="1959"  >1959</option>
                                <option value="1960"  >1960</option>
                                <option value="1961"  >1961</option>
                                <option value="1962"  >1962</option>
                                <option value="1963"  >1963</option>
                                <option value="1964"  >1964</option>
                                <option value="1965"  >1965</option>
                                <option value="1966"  >1966</option>
                                <option value="1967"  >1967</option>
                                <option value="1968"  >1968</option>
                                <option value="1969"  >1969</option>
                                <option value="1970"  >1970</option>
                                <option value="1971"  >1971</option>
                                <option value="1972"  >1972</option>
                                <option value="1973"  >1973</option>
                                <option value="1974"  >1974</option>
                                <option value="1975"  >1975</option>
                                <option value="1976"  >1976</option>
                                <option value="1977"  >1977</option>
                                <option value="1978"  >1978</option>
                                <option value="1979"  >1979</option>
                                <option value="1980"  >1980</option>
                                <option value="1981"  >1981</option>
                                <option value="1982"  >1982</option>
                                <option value="1983"  >1983</option>
                                <option value="1984"  >1984</option>
                                <option value="1985"  >1985</option>
                                <option value="1986"  >1986</option>
                                <option value="1987"  >1987</option>
                                <option value="1988"  >1988</option>
                                <option value="1989"  >1989</option>
                                <option value="1990"  >1990</option>
                                <option value="1991"  >1991</option>
                                <option value="1992"  >1992</option>
                                <option value="1993"  >1993</option>
                                <option value="1994"  >1994</option>
                                <option value="1995"  >1995</option>
                                <option value="1996"  >1996</option>
                                <option value="1997"  >1997</option>
                                <option value="1998"  >1998</option>
                                <option value="1999"  >1999</option>
                                <option value="2000"  >2000</option>
                                <option value="2001"  >2001</option>
                                <option value="2002"  >2002</option>
                                <option value="2003"  >2003</option>
                                <option value="2004"  >2004</option>
                                <option value="2005"  >2005</option>
                                <option value="2006"  >2006</option>
                                <option value="2007"  >2007</option>
                                <option value="2008"  >2008</option>
                                <option value="2009"  >2009</option>
                                <option value="2010"  >2010</option>
                                <option value="2011"  >2011</option>
                                <option value="2012"  >2012</option>
                                <option value="2013"  >2013</option>
                                <option value="2014"  >2014</option>
                                <option value="2015"  >2015</option>
                                <option value="2016"  >2016</option>
                                <option value="2017"  >2017</option>
                                <option value="2018"  >2018</option>
                                <option value="2019"  >2019</option>
                                <option value="2020"  >2020</option>
                                <option value="2021"  >2021</option>
                                <option value="2022"  >2022</option>
                                <option value="2023"  >2023</option>
                                <option value="2024"  >2024</option>
                              </select></td>
                              <td align="center"> <input type="file" name="phdSupportingDocuments" id="phdSupportingDocuments" class="form-tb-input2"></td>
            
          </tr>
          <tr>
            <td align="center">Post Graduation</td>
            <td align="center"><input type="text" name="postGraduationBoardCollegeUniversity" id="postGraduationBoardCollegeUniversity" class="form-tb-input1" placeholder="Name of Board/College/University" value="" ></td>
            <td align="center"><input type="number" name="postGraduationPercentageOfMarks" id="postGraduationPercentageOfMarks" class="form-tb-input2" placeholder="% of Marks" value="" ></td>
            <td align="center"><textarea style="resize: none;" type="text" name="postGraduationSubjects" id="postGraduationSubjects" class="form-tb-input2" placeholder="Subjects" ></textarea></td>
            <td align="center"><select name="postGraduationYearOfPassingAward" id="postGraduationYearOfPassingAward" class="form-tb-input1">
                <option value="">Select Year</option>
                                <option value="1865"  >1865</option>
                                <option value="1866"  >1866</option>
                                <option value="1867"  >1867</option>
                                <option value="1868"  >1868</option>
                                <option value="1869"  >1869</option>
                                <option value="1870"  >1870</option>
                                <option value="1871"  >1871</option>
                                <option value="1872"  >1872</option>
                                <option value="1873"  >1873</option>
                                <option value="1874"  >1874</option>
                                <option value="1875"  >1875</option>
                                <option value="1876"  >1876</option>
                                <option value="1877"  >1877</option>
                                <option value="1878"  >1878</option>
                                <option value="1879"  >1879</option>
                                <option value="1880"  >1880</option>
                                <option value="1881"  >1881</option>
                                <option value="1882"  >1882</option>
                                <option value="1883"  >1883</option>
                                <option value="1884"  >1884</option>
                                <option value="1885"  >1885</option>
                                <option value="1886"  >1886</option>
                                <option value="1887"  >1887</option>
                                <option value="1888"  >1888</option>
                                <option value="1889"  >1889</option>
                                <option value="1890"  >1890</option>
                                <option value="1891"  >1891</option>
                                <option value="1892"  >1892</option>
                                <option value="1893"  >1893</option>
                                <option value="1894"  >1894</option>
                                <option value="1895"  >1895</option>
                                <option value="1896"  >1896</option>
                                <option value="1897"  >1897</option>
                                <option value="1898"  >1898</option>
                                <option value="1899"  >1899</option>
                                <option value="1900"  >1900</option>
                                <option value="1901"  >1901</option>
                                <option value="1902"  >1902</option>
                                <option value="1903"  >1903</option>
                                <option value="1904"  >1904</option>
                                <option value="1905"  >1905</option>
                                <option value="1906"  >1906</option>
                                <option value="1907"  >1907</option>
                                <option value="1908"  >1908</option>
                                <option value="1909"  >1909</option>
                                <option value="1910"  >1910</option>
                                <option value="1911"  >1911</option>
                                <option value="1912"  >1912</option>
                                <option value="1913"  >1913</option>
                                <option value="1914"  >1914</option>
                                <option value="1915"  >1915</option>
                                <option value="1916"  >1916</option>
                                <option value="1917"  >1917</option>
                                <option value="1918"  >1918</option>
                                <option value="1919"  >1919</option>
                                <option value="1920"  >1920</option>
                                <option value="1921"  >1921</option>
                                <option value="1922"  >1922</option>
                                <option value="1923"  >1923</option>
                                <option value="1924"  >1924</option>
                                <option value="1925"  >1925</option>
                                <option value="1926"  >1926</option>
                                <option value="1927"  >1927</option>
                                <option value="1928"  >1928</option>
                                <option value="1929"  >1929</option>
                                <option value="1930"  >1930</option>
                                <option value="1931"  >1931</option>
                                <option value="1932"  >1932</option>
                                <option value="1933"  >1933</option>
                                <option value="1934"  >1934</option>
                                <option value="1935"  >1935</option>
                                <option value="1936"  >1936</option>
                                <option value="1937"  >1937</option>
                                <option value="1938"  >1938</option>
                                <option value="1939"  >1939</option>
                                <option value="1940"  >1940</option>
                                <option value="1941"  >1941</option>
                                <option value="1942"  >1942</option>
                                <option value="1943"  >1943</option>
                                <option value="1944"  >1944</option>
                                <option value="1945"  >1945</option>
                                <option value="1946"  >1946</option>
                                <option value="1947"  >1947</option>
                                <option value="1948"  >1948</option>
                                <option value="1949"  >1949</option>
                                <option value="1950"  >1950</option>
                                <option value="1951"  >1951</option>
                                <option value="1952"  >1952</option>
                                <option value="1953"  >1953</option>
                                <option value="1954"  >1954</option>
                                <option value="1955"  >1955</option>
                                <option value="1956"  >1956</option>
                                <option value="1957"  >1957</option>
                                <option value="1958"  >1958</option>
                                <option value="1959"  >1959</option>
                                <option value="1960"  >1960</option>
                                <option value="1961"  >1961</option>
                                <option value="1962"  >1962</option>
                                <option value="1963"  >1963</option>
                                <option value="1964"  >1964</option>
                                <option value="1965"  >1965</option>
                                <option value="1966"  >1966</option>
                                <option value="1967"  >1967</option>
                                <option value="1968"  >1968</option>
                                <option value="1969"  >1969</option>
                                <option value="1970"  >1970</option>
                                <option value="1971"  >1971</option>
                                <option value="1972"  >1972</option>
                                <option value="1973"  >1973</option>
                                <option value="1974"  >1974</option>
                                <option value="1975"  >1975</option>
                                <option value="1976"  >1976</option>
                                <option value="1977"  >1977</option>
                                <option value="1978"  >1978</option>
                                <option value="1979"  >1979</option>
                                <option value="1980"  >1980</option>
                                <option value="1981"  >1981</option>
                                <option value="1982"  >1982</option>
                                <option value="1983"  >1983</option>
                                <option value="1984"  >1984</option>
                                <option value="1985"  >1985</option>
                                <option value="1986"  >1986</option>
                                <option value="1987"  >1987</option>
                                <option value="1988"  >1988</option>
                                <option value="1989"  >1989</option>
                                <option value="1990"  >1990</option>
                                <option value="1991"  >1991</option>
                                <option value="1992"  >1992</option>
                                <option value="1993"  >1993</option>
                                <option value="1994"  >1994</option>
                                <option value="1995"  >1995</option>
                                <option value="1996"  >1996</option>
                                <option value="1997"  >1997</option>
                                <option value="1998"  >1998</option>
                                <option value="1999"  >1999</option>
                                <option value="2000"  >2000</option>
                                <option value="2001"  >2001</option>
                                <option value="2002"  >2002</option>
                                <option value="2003"  >2003</option>
                                <option value="2004"  >2004</option>
                                <option value="2005"  >2005</option>
                                <option value="2006"  >2006</option>
                                <option value="2007"  >2007</option>
                                <option value="2008"  >2008</option>
                                <option value="2009"  >2009</option>
                                <option value="2010"  >2010</option>
                                <option value="2011"  >2011</option>
                                <option value="2012"  >2012</option>
                                <option value="2013"  >2013</option>
                                <option value="2014"  >2014</option>
                                <option value="2015"  >2015</option>
                                <option value="2016"  >2016</option>
                                <option value="2017"  >2017</option>
                                <option value="2018"  >2018</option>
                                <option value="2019"  >2019</option>
                                <option value="2020"  >2020</option>
                                <option value="2021"  >2021</option>
                                <option value="2022"  >2022</option>
                                <option value="2023"  >2023</option>
                                <option value="2024"  >2024</option>
                              </select></td>
                              <td align="center"><input type="file" name="postGraduationSupportingDocuments" id="postGraduationSupportingDocuments" class="form-tb-input2"></td>
          </tr>
          <tr>
            <td align="center">Graduation<span style="color:#f00;font-size: 15px;"> * </span></td>
            <td align="center"><input type="text" name="graduationBoardCollegeUniversity" id="graduationBoardCollegeUniversity" class="form-tb-input1" placeholder="Name of Board/College/University" value="" ></td>
            <td align="center"><input type="number" name="graduationPercentageOfMarks" id="graduationPercentageOfMarks" class="form-tb-input2" placeholder="% of Marks" value="" ></td>
            <td align="center"><textarea style="resize: none;" type="text" name="graduationSubjects" id="graduationSubjects" class="form-tb-input2" placeholder="Subjects" ></textarea></td>
            <td align="center"><select name="graduationYearOfPassingAward" id="graduationYearOfPassingAward" class="form-tb-input1">
                <option value="">Select Year</option>
                                <option value="1865"  >1865</option>
                                <option value="1866"  >1866</option>
                                <option value="1867"  >1867</option>
                                <option value="1868"  >1868</option>
                                <option value="1869"  >1869</option>
                                <option value="1870"  >1870</option>
                                <option value="1871"  >1871</option>
                                <option value="1872"  >1872</option>
                                <option value="1873"  >1873</option>
                                <option value="1874"  >1874</option>
                                <option value="1875"  >1875</option>
                                <option value="1876"  >1876</option>
                                <option value="1877"  >1877</option>
                                <option value="1878"  >1878</option>
                                <option value="1879"  >1879</option>
                                <option value="1880"  >1880</option>
                                <option value="1881"  >1881</option>
                                <option value="1882"  >1882</option>
                                <option value="1883"  >1883</option>
                                <option value="1884"  >1884</option>
                                <option value="1885"  >1885</option>
                                <option value="1886"  >1886</option>
                                <option value="1887"  >1887</option>
                                <option value="1888"  >1888</option>
                                <option value="1889"  >1889</option>
                                <option value="1890"  >1890</option>
                                <option value="1891"  >1891</option>
                                <option value="1892"  >1892</option>
                                <option value="1893"  >1893</option>
                                <option value="1894"  >1894</option>
                                <option value="1895"  >1895</option>
                                <option value="1896"  >1896</option>
                                <option value="1897"  >1897</option>
                                <option value="1898"  >1898</option>
                                <option value="1899"  >1899</option>
                                <option value="1900"  >1900</option>
                                <option value="1901"  >1901</option>
                                <option value="1902"  >1902</option>
                                <option value="1903"  >1903</option>
                                <option value="1904"  >1904</option>
                                <option value="1905"  >1905</option>
                                <option value="1906"  >1906</option>
                                <option value="1907"  >1907</option>
                                <option value="1908"  >1908</option>
                                <option value="1909"  >1909</option>
                                <option value="1910"  >1910</option>
                                <option value="1911"  >1911</option>
                                <option value="1912"  >1912</option>
                                <option value="1913"  >1913</option>
                                <option value="1914"  >1914</option>
                                <option value="1915"  >1915</option>
                                <option value="1916"  >1916</option>
                                <option value="1917"  >1917</option>
                                <option value="1918"  >1918</option>
                                <option value="1919"  >1919</option>
                                <option value="1920"  >1920</option>
                                <option value="1921"  >1921</option>
                                <option value="1922"  >1922</option>
                                <option value="1923"  >1923</option>
                                <option value="1924"  >1924</option>
                                <option value="1925"  >1925</option>
                                <option value="1926"  >1926</option>
                                <option value="1927"  >1927</option>
                                <option value="1928"  >1928</option>
                                <option value="1929"  >1929</option>
                                <option value="1930"  >1930</option>
                                <option value="1931"  >1931</option>
                                <option value="1932"  >1932</option>
                                <option value="1933"  >1933</option>
                                <option value="1934"  >1934</option>
                                <option value="1935"  >1935</option>
                                <option value="1936"  >1936</option>
                                <option value="1937"  >1937</option>
                                <option value="1938"  >1938</option>
                                <option value="1939"  >1939</option>
                                <option value="1940"  >1940</option>
                                <option value="1941"  >1941</option>
                                <option value="1942"  >1942</option>
                                <option value="1943"  >1943</option>
                                <option value="1944"  >1944</option>
                                <option value="1945"  >1945</option>
                                <option value="1946"  >1946</option>
                                <option value="1947"  >1947</option>
                                <option value="1948"  >1948</option>
                                <option value="1949"  >1949</option>
                                <option value="1950"  >1950</option>
                                <option value="1951"  >1951</option>
                                <option value="1952"  >1952</option>
                                <option value="1953"  >1953</option>
                                <option value="1954"  >1954</option>
                                <option value="1955"  >1955</option>
                                <option value="1956"  >1956</option>
                                <option value="1957"  >1957</option>
                                <option value="1958"  >1958</option>
                                <option value="1959"  >1959</option>
                                <option value="1960"  >1960</option>
                                <option value="1961"  >1961</option>
                                <option value="1962"  >1962</option>
                                <option value="1963"  >1963</option>
                                <option value="1964"  >1964</option>
                                <option value="1965"  >1965</option>
                                <option value="1966"  >1966</option>
                                <option value="1967"  >1967</option>
                                <option value="1968"  >1968</option>
                                <option value="1969"  >1969</option>
                                <option value="1970"  >1970</option>
                                <option value="1971"  >1971</option>
                                <option value="1972"  >1972</option>
                                <option value="1973"  >1973</option>
                                <option value="1974"  >1974</option>
                                <option value="1975"  >1975</option>
                                <option value="1976"  >1976</option>
                                <option value="1977"  >1977</option>
                                <option value="1978"  >1978</option>
                                <option value="1979"  >1979</option>
                                <option value="1980"  >1980</option>
                                <option value="1981"  >1981</option>
                                <option value="1982"  >1982</option>
                                <option value="1983"  >1983</option>
                                <option value="1984"  >1984</option>
                                <option value="1985"  >1985</option>
                                <option value="1986"  >1986</option>
                                <option value="1987"  >1987</option>
                                <option value="1988"  >1988</option>
                                <option value="1989"  >1989</option>
                                <option value="1990"  >1990</option>
                                <option value="1991"  >1991</option>
                                <option value="1992"  >1992</option>
                                <option value="1993"  >1993</option>
                                <option value="1994"  >1994</option>
                                <option value="1995"  >1995</option>
                                <option value="1996"  >1996</option>
                                <option value="1997"  >1997</option>
                                <option value="1998"  >1998</option>
                                <option value="1999"  >1999</option>
                                <option value="2000"  >2000</option>
                                <option value="2001"  >2001</option>
                                <option value="2002"  >2002</option>
                                <option value="2003"  >2003</option>
                                <option value="2004"  >2004</option>
                                <option value="2005"  >2005</option>
                                <option value="2006"  >2006</option>
                                <option value="2007"  >2007</option>
                                <option value="2008"  >2008</option>
                                <option value="2009"  >2009</option>
                                <option value="2010"  >2010</option>
                                <option value="2011"  >2011</option>
                                <option value="2012"  >2012</option>
                                <option value="2013"  >2013</option>
                                <option value="2014"  >2014</option>
                                <option value="2015"  >2015</option>
                                <option value="2016"  >2016</option>
                                <option value="2017"  >2017</option>
                                <option value="2018"  >2018</option>
                                <option value="2019"  >2019</option>
                                <option value="2020"  >2020</option>
                                <option value="2021"  >2021</option>
                                <option value="2022"  >2022</option>
                                <option value="2023"  >2023</option>
                                <option value="2024"  >2024</option>
                              </select></td>
                              <td align="center"><input type="file" name="graduationSupportingDocuments" id="graduationSupportingDocuments" class="form-tb-input2"></td>
          </tr>
            <tr>
              <td align="center">XII<span style="color:#f00;font-size: 15px;"> * </span></td>
              <td align="center"><input type="text" name="xiiBoardCollegeUniversity" id="xiiBoardCollegeUniversity" class="form-tb-input1" placeholder="Name of Board/College/University" value="" ></td>
              <td align="center"><input type="number" name="xiiPercentageOfMarks" id="xiiPercentageOfMarks" class="form-tb-input2" placeholder="% of Marks" value="" ></td>
              <td align="center"><textarea style="resize: none;" type="text" name="xiiSubjects" id="xiiSubjects" class="form-tb-input2" placeholder="Subjects" ></textarea></td>
              <td align="center"><select name="xiiYearOfPassingAward" id="xiiYearOfPassingAward" class="form-tb-input1">
                  <option value="">Select Year</option>
                                <option value="1865"  >1865</option>
                                <option value="1866"  >1866</option>
                                <option value="1867"  >1867</option>
                                <option value="1868"  >1868</option>
                                <option value="1869"  >1869</option>
                                <option value="1870"  >1870</option>
                                <option value="1871"  >1871</option>
                                <option value="1872"  >1872</option>
                                <option value="1873"  >1873</option>
                                <option value="1874"  >1874</option>
                                <option value="1875"  >1875</option>
                                <option value="1876"  >1876</option>
                                <option value="1877"  >1877</option>
                                <option value="1878"  >1878</option>
                                <option value="1879"  >1879</option>
                                <option value="1880"  >1880</option>
                                <option value="1881"  >1881</option>
                                <option value="1882"  >1882</option>
                                <option value="1883"  >1883</option>
                                <option value="1884"  >1884</option>
                                <option value="1885"  >1885</option>
                                <option value="1886"  >1886</option>
                                <option value="1887"  >1887</option>
                                <option value="1888"  >1888</option>
                                <option value="1889"  >1889</option>
                                <option value="1890"  >1890</option>
                                <option value="1891"  >1891</option>
                                <option value="1892"  >1892</option>
                                <option value="1893"  >1893</option>
                                <option value="1894"  >1894</option>
                                <option value="1895"  >1895</option>
                                <option value="1896"  >1896</option>
                                <option value="1897"  >1897</option>
                                <option value="1898"  >1898</option>
                                <option value="1899"  >1899</option>
                                <option value="1900"  >1900</option>
                                <option value="1901"  >1901</option>
                                <option value="1902"  >1902</option>
                                <option value="1903"  >1903</option>
                                <option value="1904"  >1904</option>
                                <option value="1905"  >1905</option>
                                <option value="1906"  >1906</option>
                                <option value="1907"  >1907</option>
                                <option value="1908"  >1908</option>
                                <option value="1909"  >1909</option>
                                <option value="1910"  >1910</option>
                                <option value="1911"  >1911</option>
                                <option value="1912"  >1912</option>
                                <option value="1913"  >1913</option>
                                <option value="1914"  >1914</option>
                                <option value="1915"  >1915</option>
                                <option value="1916"  >1916</option>
                                <option value="1917"  >1917</option>
                                <option value="1918"  >1918</option>
                                <option value="1919"  >1919</option>
                                <option value="1920"  >1920</option>
                                <option value="1921"  >1921</option>
                                <option value="1922"  >1922</option>
                                <option value="1923"  >1923</option>
                                <option value="1924"  >1924</option>
                                <option value="1925"  >1925</option>
                                <option value="1926"  >1926</option>
                                <option value="1927"  >1927</option>
                                <option value="1928"  >1928</option>
                                <option value="1929"  >1929</option>
                                <option value="1930"  >1930</option>
                                <option value="1931"  >1931</option>
                                <option value="1932"  >1932</option>
                                <option value="1933"  >1933</option>
                                <option value="1934"  >1934</option>
                                <option value="1935"  >1935</option>
                                <option value="1936"  >1936</option>
                                <option value="1937"  >1937</option>
                                <option value="1938"  >1938</option>
                                <option value="1939"  >1939</option>
                                <option value="1940"  >1940</option>
                                <option value="1941"  >1941</option>
                                <option value="1942"  >1942</option>
                                <option value="1943"  >1943</option>
                                <option value="1944"  >1944</option>
                                <option value="1945"  >1945</option>
                                <option value="1946"  >1946</option>
                                <option value="1947"  >1947</option>
                                <option value="1948"  >1948</option>
                                <option value="1949"  >1949</option>
                                <option value="1950"  >1950</option>
                                <option value="1951"  >1951</option>
                                <option value="1952"  >1952</option>
                                <option value="1953"  >1953</option>
                                <option value="1954"  >1954</option>
                                <option value="1955"  >1955</option>
                                <option value="1956"  >1956</option>
                                <option value="1957"  >1957</option>
                                <option value="1958"  >1958</option>
                                <option value="1959"  >1959</option>
                                <option value="1960"  >1960</option>
                                <option value="1961"  >1961</option>
                                <option value="1962"  >1962</option>
                                <option value="1963"  >1963</option>
                                <option value="1964"  >1964</option>
                                <option value="1965"  >1965</option>
                                <option value="1966"  >1966</option>
                                <option value="1967"  >1967</option>
                                <option value="1968"  >1968</option>
                                <option value="1969"  >1969</option>
                                <option value="1970"  >1970</option>
                                <option value="1971"  >1971</option>
                                <option value="1972"  >1972</option>
                                <option value="1973"  >1973</option>
                                <option value="1974"  >1974</option>
                                <option value="1975"  >1975</option>
                                <option value="1976"  >1976</option>
                                <option value="1977"  >1977</option>
                                <option value="1978"  >1978</option>
                                <option value="1979"  >1979</option>
                                <option value="1980"  >1980</option>
                                <option value="1981"  >1981</option>
                                <option value="1982"  >1982</option>
                                <option value="1983"  >1983</option>
                                <option value="1984"  >1984</option>
                                <option value="1985"  >1985</option>
                                <option value="1986"  >1986</option>
                                <option value="1987"  >1987</option>
                                <option value="1988"  >1988</option>
                                <option value="1989"  >1989</option>
                                <option value="1990"  >1990</option>
                                <option value="1991"  >1991</option>
                                <option value="1992"  >1992</option>
                                <option value="1993"  >1993</option>
                                <option value="1994"  >1994</option>
                                <option value="1995"  >1995</option>
                                <option value="1996"  >1996</option>
                                <option value="1997"  >1997</option>
                                <option value="1998"  >1998</option>
                                <option value="1999"  >1999</option>
                                <option value="2000"  >2000</option>
                                <option value="2001"  >2001</option>
                                <option value="2002"  >2002</option>
                                <option value="2003"  >2003</option>
                                <option value="2004"  >2004</option>
                                <option value="2005"  >2005</option>
                                <option value="2006"  >2006</option>
                                <option value="2007"  >2007</option>
                                <option value="2008"  >2008</option>
                                <option value="2009"  >2009</option>
                                <option value="2010"  >2010</option>
                                <option value="2011"  >2011</option>
                                <option value="2012"  >2012</option>
                                <option value="2013"  >2013</option>
                                <option value="2014"  >2014</option>
                                <option value="2015"  >2015</option>
                                <option value="2016"  >2016</option>
                                <option value="2017"  >2017</option>
                                <option value="2018"  >2018</option>
                                <option value="2019"  >2019</option>
                                <option value="2020"  >2020</option>
                                <option value="2021"  >2021</option>
                                <option value="2022"  >2022</option>
                                <option value="2023"  >2023</option>
                                <option value="2024"  >2024</option>
                              </select></td>
                              <td align="center"><input type="file" name="xiiSupportingDocuments" id="xiiSupportingDocuments" class="form-tb-input2"></td>
          </tr>
            <tr>
              <td align="center">X<span style="color:#f00;font-size: 15px;"> * </span></td>
              <td align="center"><input type="text" name="xBoardCollegeUniversity" id="xBoardCollegeUniversity" class="form-tb-input1" placeholder="Name of Board/College/University" value="" ></td>
              <td align="center"><input type="number" name="xPercentageOfMarks" id="xPercentageOfMarks" class="form-tb-input2" placeholder="% of Marks" value="" ></td>
              <td align="center"><textarea type="text" name="xSubjects" id="xSubjects" class="form-tb-input2" placeholder="Subjects" ></textarea></td>
              <td align="center"><select name="xYearOfPassingAward" id="xYearOfPassingAward" class="form-tb-input1">
                  <option value="">Select Year</option>
                                <option value="1865"  >1865</option>
                                <option value="1866"  >1866</option>
                                <option value="1867"  >1867</option>
                                <option value="1868"  >1868</option>
                                <option value="1869"  >1869</option>
                                <option value="1870"  >1870</option>
                                <option value="1871"  >1871</option>
                                <option value="1872"  >1872</option>
                                <option value="1873"  >1873</option>
                                <option value="1874"  >1874</option>
                                <option value="1875"  >1875</option>
                                <option value="1876"  >1876</option>
                                <option value="1877"  >1877</option>
                                <option value="1878"  >1878</option>
                                <option value="1879"  >1879</option>
                                <option value="1880"  >1880</option>
                                <option value="1881"  >1881</option>
                                <option value="1882"  >1882</option>
                                <option value="1883"  >1883</option>
                                <option value="1884"  >1884</option>
                                <option value="1885"  >1885</option>
                                <option value="1886"  >1886</option>
                                <option value="1887"  >1887</option>
                                <option value="1888"  >1888</option>
                                <option value="1889"  >1889</option>
                                <option value="1890"  >1890</option>
                                <option value="1891"  >1891</option>
                                <option value="1892"  >1892</option>
                                <option value="1893"  >1893</option>
                                <option value="1894"  >1894</option>
                                <option value="1895"  >1895</option>
                                <option value="1896"  >1896</option>
                                <option value="1897"  >1897</option>
                                <option value="1898"  >1898</option>
                                <option value="1899"  >1899</option>
                                <option value="1900"  >1900</option>
                                <option value="1901"  >1901</option>
                                <option value="1902"  >1902</option>
                                <option value="1903"  >1903</option>
                                <option value="1904"  >1904</option>
                                <option value="1905"  >1905</option>
                                <option value="1906"  >1906</option>
                                <option value="1907"  >1907</option>
                                <option value="1908"  >1908</option>
                                <option value="1909"  >1909</option>
                                <option value="1910"  >1910</option>
                                <option value="1911"  >1911</option>
                                <option value="1912"  >1912</option>
                                <option value="1913"  >1913</option>
                                <option value="1914"  >1914</option>
                                <option value="1915"  >1915</option>
                                <option value="1916"  >1916</option>
                                <option value="1917"  >1917</option>
                                <option value="1918"  >1918</option>
                                <option value="1919"  >1919</option>
                                <option value="1920"  >1920</option>
                                <option value="1921"  >1921</option>
                                <option value="1922"  >1922</option>
                                <option value="1923"  >1923</option>
                                <option value="1924"  >1924</option>
                                <option value="1925"  >1925</option>
                                <option value="1926"  >1926</option>
                                <option value="1927"  >1927</option>
                                <option value="1928"  >1928</option>
                                <option value="1929"  >1929</option>
                                <option value="1930"  >1930</option>
                                <option value="1931"  >1931</option>
                                <option value="1932"  >1932</option>
                                <option value="1933"  >1933</option>
                                <option value="1934"  >1934</option>
                                <option value="1935"  >1935</option>
                                <option value="1936"  >1936</option>
                                <option value="1937"  >1937</option>
                                <option value="1938"  >1938</option>
                                <option value="1939"  >1939</option>
                                <option value="1940"  >1940</option>
                                <option value="1941"  >1941</option>
                                <option value="1942"  >1942</option>
                                <option value="1943"  >1943</option>
                                <option value="1944"  >1944</option>
                                <option value="1945"  >1945</option>
                                <option value="1946"  >1946</option>
                                <option value="1947"  >1947</option>
                                <option value="1948"  >1948</option>
                                <option value="1949"  >1949</option>
                                <option value="1950"  >1950</option>
                                <option value="1951"  >1951</option>
                                <option value="1952"  >1952</option>
                                <option value="1953"  >1953</option>
                                <option value="1954"  >1954</option>
                                <option value="1955"  >1955</option>
                                <option value="1956"  >1956</option>
                                <option value="1957"  >1957</option>
                                <option value="1958"  >1958</option>
                                <option value="1959"  >1959</option>
                                <option value="1960"  >1960</option>
                                <option value="1961"  >1961</option>
                                <option value="1962"  >1962</option>
                                <option value="1963"  >1963</option>
                                <option value="1964"  >1964</option>
                                <option value="1965"  >1965</option>
                                <option value="1966"  >1966</option>
                                <option value="1967"  >1967</option>
                                <option value="1968"  >1968</option>
                                <option value="1969"  >1969</option>
                                <option value="1970"  >1970</option>
                                <option value="1971"  >1971</option>
                                <option value="1972"  >1972</option>
                                <option value="1973"  >1973</option>
                                <option value="1974"  >1974</option>
                                <option value="1975"  >1975</option>
                                <option value="1976"  >1976</option>
                                <option value="1977"  >1977</option>
                                <option value="1978"  >1978</option>
                                <option value="1979"  >1979</option>
                                <option value="1980"  >1980</option>
                                <option value="1981"  >1981</option>
                                <option value="1982"  >1982</option>
                                <option value="1983"  >1983</option>
                                <option value="1984"  >1984</option>
                                <option value="1985"  >1985</option>
                                <option value="1986"  >1986</option>
                                <option value="1987"  >1987</option>
                                <option value="1988"  >1988</option>
                                <option value="1989"  >1989</option>
                                <option value="1990"  >1990</option>
                                <option value="1991"  >1991</option>
                                <option value="1992"  >1992</option>
                                <option value="1993"  >1993</option>
                                <option value="1994"  >1994</option>
                                <option value="1995"  >1995</option>
                                <option value="1996"  >1996</option>
                                <option value="1997"  >1997</option>
                                <option value="1998"  >1998</option>
                                <option value="1999"  >1999</option>
                                <option value="2000"  >2000</option>
                                <option value="2001"  >2001</option>
                                <option value="2002"  >2002</option>
                                <option value="2003"  >2003</option>
                                <option value="2004"  >2004</option>
                                <option value="2005"  >2005</option>
                                <option value="2006"  >2006</option>
                                <option value="2007"  >2007</option>
                                <option value="2008"  >2008</option>
                                <option value="2009"  >2009</option>
                                <option value="2010"  >2010</option>
                                <option value="2011"  >2011</option>
                                <option value="2012"  >2012</option>
                                <option value="2013"  >2013</option>
                                <option value="2014"  >2014</option>
                                <option value="2015"  >2015</option>
                                <option value="2016"  >2016</option>
                                <option value="2017"  >2017</option>
                                <option value="2018"  >2018</option>
                                <option value="2019"  >2019</option>
                                <option value="2020"  >2020</option>
                                <option value="2021"  >2021</option>
                                <option value="2022"  >2022</option>
                                <option value="2023"  >2023</option>
                                <option value="2024"  >2024</option>
                              </select></td>
                              <td align="center"><input type="file" name="xSupportingDocuments" id="xSupportingDocuments" class="form-tb-input2"></td>
          </tr>
          <tr>
            <td align="center">Others</td>
            <td align="center"><input type="text" name="othersBoardCollegeUniversity" id="othersBoardCollegeUniversity" class="form-tb-input1" placeholder="Name of Board/College/University" value="" ></td>
            <td align="center"><input type="number" name="othersPercentageOfMarks" id="othersPercentageOfMarks" class="form-tb-input2" placeholder="% of Marks" value="" ></td>
            <td align="center"><textarea style="resize: none;" type="text" name="othersSubjects" id="othersSubjects" class="form-tb-input2" placeholder="Subjects" ></textarea></td>
            <td align="center"><select name="othersYearOfPassingAward" id="othersYearOfPassingAward" class="form-tb-input1">
                <option value="">Select Year</option>
                                <option value="1865"  >1865</option>
                                <option value="1866"  >1866</option>
                                <option value="1867"  >1867</option>
                                <option value="1868"  >1868</option>
                                <option value="1869"  >1869</option>
                                <option value="1870"  >1870</option>
                                <option value="1871"  >1871</option>
                                <option value="1872"  >1872</option>
                                <option value="1873"  >1873</option>
                                <option value="1874"  >1874</option>
                                <option value="1875"  >1875</option>
                                <option value="1876"  >1876</option>
                                <option value="1877"  >1877</option>
                                <option value="1878"  >1878</option>
                                <option value="1879"  >1879</option>
                                <option value="1880"  >1880</option>
                                <option value="1881"  >1881</option>
                                <option value="1882"  >1882</option>
                                <option value="1883"  >1883</option>
                                <option value="1884"  >1884</option>
                                <option value="1885"  >1885</option>
                                <option value="1886"  >1886</option>
                                <option value="1887"  >1887</option>
                                <option value="1888"  >1888</option>
                                <option value="1889"  >1889</option>
                                <option value="1890"  >1890</option>
                                <option value="1891"  >1891</option>
                                <option value="1892"  >1892</option>
                                <option value="1893"  >1893</option>
                                <option value="1894"  >1894</option>
                                <option value="1895"  >1895</option>
                                <option value="1896"  >1896</option>
                                <option value="1897"  >1897</option>
                                <option value="1898"  >1898</option>
                                <option value="1899"  >1899</option>
                                <option value="1900"  >1900</option>
                                <option value="1901"  >1901</option>
                                <option value="1902"  >1902</option>
                                <option value="1903"  >1903</option>
                                <option value="1904"  >1904</option>
                                <option value="1905"  >1905</option>
                                <option value="1906"  >1906</option>
                                <option value="1907"  >1907</option>
                                <option value="1908"  >1908</option>
                                <option value="1909"  >1909</option>
                                <option value="1910"  >1910</option>
                                <option value="1911"  >1911</option>
                                <option value="1912"  >1912</option>
                                <option value="1913"  >1913</option>
                                <option value="1914"  >1914</option>
                                <option value="1915"  >1915</option>
                                <option value="1916"  >1916</option>
                                <option value="1917"  >1917</option>
                                <option value="1918"  >1918</option>
                                <option value="1919"  >1919</option>
                                <option value="1920"  >1920</option>
                                <option value="1921"  >1921</option>
                                <option value="1922"  >1922</option>
                                <option value="1923"  >1923</option>
                                <option value="1924"  >1924</option>
                                <option value="1925"  >1925</option>
                                <option value="1926"  >1926</option>
                                <option value="1927"  >1927</option>
                                <option value="1928"  >1928</option>
                                <option value="1929"  >1929</option>
                                <option value="1930"  >1930</option>
                                <option value="1931"  >1931</option>
                                <option value="1932"  >1932</option>
                                <option value="1933"  >1933</option>
                                <option value="1934"  >1934</option>
                                <option value="1935"  >1935</option>
                                <option value="1936"  >1936</option>
                                <option value="1937"  >1937</option>
                                <option value="1938"  >1938</option>
                                <option value="1939"  >1939</option>
                                <option value="1940"  >1940</option>
                                <option value="1941"  >1941</option>
                                <option value="1942"  >1942</option>
                                <option value="1943"  >1943</option>
                                <option value="1944"  >1944</option>
                                <option value="1945"  >1945</option>
                                <option value="1946"  >1946</option>
                                <option value="1947"  >1947</option>
                                <option value="1948"  >1948</option>
                                <option value="1949"  >1949</option>
                                <option value="1950"  >1950</option>
                                <option value="1951"  >1951</option>
                                <option value="1952"  >1952</option>
                                <option value="1953"  >1953</option>
                                <option value="1954"  >1954</option>
                                <option value="1955"  >1955</option>
                                <option value="1956"  >1956</option>
                                <option value="1957"  >1957</option>
                                <option value="1958"  >1958</option>
                                <option value="1959"  >1959</option>
                                <option value="1960"  >1960</option>
                                <option value="1961"  >1961</option>
                                <option value="1962"  >1962</option>
                                <option value="1963"  >1963</option>
                                <option value="1964"  >1964</option>
                                <option value="1965"  >1965</option>
                                <option value="1966"  >1966</option>
                                <option value="1967"  >1967</option>
                                <option value="1968"  >1968</option>
                                <option value="1969"  >1969</option>
                                <option value="1970"  >1970</option>
                                <option value="1971"  >1971</option>
                                <option value="1972"  >1972</option>
                                <option value="1973"  >1973</option>
                                <option value="1974"  >1974</option>
                                <option value="1975"  >1975</option>
                                <option value="1976"  >1976</option>
                                <option value="1977"  >1977</option>
                                <option value="1978"  >1978</option>
                                <option value="1979"  >1979</option>
                                <option value="1980"  >1980</option>
                                <option value="1981"  >1981</option>
                                <option value="1982"  >1982</option>
                                <option value="1983"  >1983</option>
                                <option value="1984"  >1984</option>
                                <option value="1985"  >1985</option>
                                <option value="1986"  >1986</option>
                                <option value="1987"  >1987</option>
                                <option value="1988"  >1988</option>
                                <option value="1989"  >1989</option>
                                <option value="1990"  >1990</option>
                                <option value="1991"  >1991</option>
                                <option value="1992"  >1992</option>
                                <option value="1993"  >1993</option>
                                <option value="1994"  >1994</option>
                                <option value="1995"  >1995</option>
                                <option value="1996"  >1996</option>
                                <option value="1997"  >1997</option>
                                <option value="1998"  >1998</option>
                                <option value="1999"  >1999</option>
                                <option value="2000"  >2000</option>
                                <option value="2001"  >2001</option>
                                <option value="2002"  >2002</option>
                                <option value="2003"  >2003</option>
                                <option value="2004"  >2004</option>
                                <option value="2005"  >2005</option>
                                <option value="2006"  >2006</option>
                                <option value="2007"  >2007</option>
                                <option value="2008"  >2008</option>
                                <option value="2009"  >2009</option>
                                <option value="2010"  >2010</option>
                                <option value="2011"  >2011</option>
                                <option value="2012"  >2012</option>
                                <option value="2013"  >2013</option>
                                <option value="2014"  >2014</option>
                                <option value="2015"  >2015</option>
                                <option value="2016"  >2016</option>
                                <option value="2017"  >2017</option>
                                <option value="2018"  >2018</option>
                                <option value="2019"  >2019</option>
                                <option value="2020"  >2020</option>
                                <option value="2021"  >2021</option>
                                <option value="2022"  >2022</option>
                                <option value="2023"  >2023</option>
                                <option value="2024"  >2024</option>
                              </select></td>
          <td align="center"><input type="file" name="othersSupportingDocuments" id="othersSupportingDocuments" class="form-tb-input2"></td>
          </tr>
          
          </tbody>
      </table>
      <table class="form-tb1" id="form-phd" cellpadding="10" cellspacing="0"  width="100%;">
      	<tbody>
        	<tr>
            <td align="left" colspan="2" style="width: 20px;">Whether Ph.D. awarded <span style="color:#f00;font-size: 15px;"> * </span></td>
            <td align="left;" colspan="3">
              <input  class="phd_awared_statusget" type="radio" value="yes" style="width: 20px;float: left;" /> Yes
              <input type="radio"  class="phd_awared_statusget" value="no"  style="width: 20px;"/> No
              <div style="width:100%; margin-left:10px;" id="cagegory_change_lisdfdt"></div>
              </td>
              <script>
				$(".phd_awared_statusget" ).click(function() {
					var catsdfas_val	=	this.value;
					var pwd_choosesdfasd_html='';
					if(catsdfas_val=='yes'){
						pwd_choosesdfasd_html	=	'<select name="phdAwarded" id="phdAwarded" class="form-tb-input1" style="margin-top:5px;"><option value="">Select Year</option> <option value="1865"  >1865</option>  <option value="1866"  >1866</option>  <option value="1867"  >1867</option>  <option value="1868"  >1868</option>  <option value="1869"  >1869</option>  <option value="1870"  >1870</option>  <option value="1871"  >1871</option>  <option value="1872"  >1872</option>  <option value="1873"  >1873</option>  <option value="1874"  >1874</option>  <option value="1875"  >1875</option>  <option value="1876"  >1876</option>  <option value="1877"  >1877</option>  <option value="1878"  >1878</option>  <option value="1879"  >1879</option>  <option value="1880"  >1880</option>  <option value="1881"  >1881</option>  <option value="1882"  >1882</option>  <option value="1883"  >1883</option>  <option value="1884"  >1884</option>  <option value="1885"  >1885</option>  <option value="1886"  >1886</option>  <option value="1887"  >1887</option>  <option value="1888"  >1888</option>  <option value="1889"  >1889</option>  <option value="1890"  >1890</option>  <option value="1891"  >1891</option>  <option value="1892"  >1892</option>  <option value="1893"  >1893</option>  <option value="1894"  >1894</option>  <option value="1895"  >1895</option>  <option value="1896"  >1896</option>  <option value="1897"  >1897</option>  <option value="1898"  >1898</option>  <option value="1899"  >1899</option>  <option value="1900"  >1900</option>  <option value="1901"  >1901</option>  <option value="1902"  >1902</option>  <option value="1903"  >1903</option>  <option value="1904"  >1904</option>  <option value="1905"  >1905</option>  <option value="1906"  >1906</option>  <option value="1907"  >1907</option>  <option value="1908"  >1908</option>  <option value="1909"  >1909</option>  <option value="1910"  >1910</option>  <option value="1911"  >1911</option>  <option value="1912"  >1912</option>  <option value="1913"  >1913</option>  <option value="1914"  >1914</option>  <option value="1915"  >1915</option>  <option value="1916"  >1916</option>  <option value="1917"  >1917</option>  <option value="1918"  >1918</option>  <option value="1919"  >1919</option>  <option value="1920"  >1920</option>  <option value="1921"  >1921</option>  <option value="1922"  >1922</option>  <option value="1923"  >1923</option>  <option value="1924"  >1924</option>  <option value="1925"  >1925</option>  <option value="1926"  >1926</option>  <option value="1927"  >1927</option>  <option value="1928"  >1928</option>  <option value="1929"  >1929</option>  <option value="1930"  >1930</option>  <option value="1931"  >1931</option>  <option value="1932"  >1932</option>  <option value="1933"  >1933</option>  <option value="1934"  >1934</option>  <option value="1935"  >1935</option>  <option value="1936"  >1936</option>  <option value="1937"  >1937</option>  <option value="1938"  >1938</option>  <option value="1939"  >1939</option>  <option value="1940"  >1940</option>  <option value="1941"  >1941</option>  <option value="1942"  >1942</option>  <option value="1943"  >1943</option>  <option value="1944"  >1944</option>  <option value="1945"  >1945</option>  <option value="1946"  >1946</option>  <option value="1947"  >1947</option>  <option value="1948"  >1948</option>  <option value="1949"  >1949</option>  <option value="1950"  >1950</option>  <option value="1951"  >1951</option>  <option value="1952"  >1952</option>  <option value="1953"  >1953</option>  <option value="1954"  >1954</option>  <option value="1955"  >1955</option>  <option value="1956"  >1956</option>  <option value="1957"  >1957</option>  <option value="1958"  >1958</option>  <option value="1959"  >1959</option>  <option value="1960"  >1960</option>  <option value="1961"  >1961</option>  <option value="1962"  >1962</option>  <option value="1963"  >1963</option>  <option value="1964"  >1964</option>  <option value="1965"  >1965</option>  <option value="1966"  >1966</option>  <option value="1967"  >1967</option>  <option value="1968"  >1968</option>  <option value="1969"  >1969</option>  <option value="1970"  >1970</option>  <option value="1971"  >1971</option>  <option value="1972"  >1972</option>  <option value="1973"  >1973</option>  <option value="1974"  >1974</option>  <option value="1975"  >1975</option>  <option value="1976"  >1976</option>  <option value="1977"  >1977</option>  <option value="1978"  >1978</option>  <option value="1979"  >1979</option>  <option value="1980"  >1980</option>  <option value="1981"  >1981</option>  <option value="1982"  >1982</option>  <option value="1983"  >1983</option>  <option value="1984"  >1984</option>  <option value="1985"  >1985</option>  <option value="1986"  >1986</option>  <option value="1987"  >1987</option>  <option value="1988"  >1988</option>  <option value="1989"  >1989</option>  <option value="1990"  >1990</option>  <option value="1991"  >1991</option>  <option value="1992"  >1992</option>  <option value="1993"  >1993</option>  <option value="1994"  >1994</option>  <option value="1995"  >1995</option>  <option value="1996"  >1996</option>  <option value="1997"  >1997</option>  <option value="1998"  >1998</option>  <option value="1999"  >1999</option>  <option value="2000"  >2000</option>  <option value="2001"  >2001</option>  <option value="2002"  >2002</option>  <option value="2003"  >2003</option>  <option value="2004"  >2004</option>  <option value="2005"  >2005</option>  <option value="2006"  >2006</option>  <option value="2007"  >2007</option>  <option value="2008"  >2008</option>  <option value="2009"  >2009</option>  <option value="2010"  >2010</option>  <option value="2011"  >2011</option>  <option value="2012"  >2012</option>  <option value="2013"  >2013</option>  <option value="2014"  >2014</option>  <option value="2015"  >2015</option>  <option value="2016"  >2016</option>  <option value="2017"  >2017</option>  <option value="2018"  >2018</option>  <option value="2019"  >2019</option>  <option value="2020"  >2020</option>  <option value="2021"  >2021</option>  <option value="2022"  >2022</option>  <option value="2023"  >2023</option>  <option value="2024"  >2024</option> </select>';
						$('#cagegory_change_lisdfdt').html(pwd_choosesdfasd_html);
					}else if(catsdfas_val=='no'){
						$('#cagegory_change_lisdfdt').html('');
					}
				});
			</script>
          </tr>
          <tr>
            <td align="left" colspan="2" style="width: 20px;">Title of Ph.D. thesis awarded <span style="color:#f00;font-size: 15px;"> * </span></td>
            <td align="left;" colspan="3"><textarea name="phdThesisAwarded" id="phdThesisAwarded" style="width: 98%;border: 1px solid #ccc;  resize: none;border-radius: 5px;padding: 8px 5px;font-size: 14px; height:80px;"></textarea></td>
          </tr>
          <tr>
            <td align="left" colspan="2" style="width: 20px;">Whether qualified UGC/CSIR NET/SLET/SET/ARS <span style="color:#f00;font-size: 15px;"> * </span> </td>
            <td align="left;" colspan="3"><input  class="qualified_awared_statusget" type="radio" value="yes" style="width: 20px;float: left;" /> Yes
            <input type="radio"  class="qualified_awared_statusget" value="no"  style="width: 20px;"/> No
              <div style="width:100%; margin-left:10px;" id="qualified_change_lisdfdt"></div>
              </td>
              <script>
				$(".qualified_awared_statusget" ).click(function() {
					var qualified_val	=	this.value;
					var qualified_choosesdfasd_html='';
					if(qualified_val=='yes'){
						qualified_choosesdfasd_html	=	'<select class="form-tb-input1" style="margin-top:5px;width: 25%;float:left;" name="ugcCSIR" id="ugcCSIR"><option value="UGC">UGC</option><option value="CSIR NET">CSIR NET</option><option value="SLET">SLET</option><option value="SET">SET</option><option value="ARS">ARS</option></select><select name="ugcYear" id="ugcYear" class="form-tb-input1" style="margin-top:5px;width: 35%;float:left;"><option value="">Select Year</option> <option value="1865"  >1865</option>  <option value="1866"  >1866</option>  <option value="1867"  >1867</option>  <option value="1868"  >1868</option>  <option value="1869"  >1869</option>  <option value="1870"  >1870</option>  <option value="1871"  >1871</option>  <option value="1872"  >1872</option>  <option value="1873"  >1873</option>  <option value="1874"  >1874</option>  <option value="1875"  >1875</option>  <option value="1876"  >1876</option>  <option value="1877"  >1877</option>  <option value="1878"  >1878</option>  <option value="1879"  >1879</option>  <option value="1880"  >1880</option>  <option value="1881"  >1881</option>  <option value="1882"  >1882</option>  <option value="1883"  >1883</option>  <option value="1884"  >1884</option>  <option value="1885"  >1885</option>  <option value="1886"  >1886</option>  <option value="1887"  >1887</option>  <option value="1888"  >1888</option>  <option value="1889"  >1889</option>  <option value="1890"  >1890</option>  <option value="1891"  >1891</option>  <option value="1892"  >1892</option>  <option value="1893"  >1893</option>  <option value="1894"  >1894</option>  <option value="1895"  >1895</option>  <option value="1896"  >1896</option>  <option value="1897"  >1897</option>  <option value="1898"  >1898</option>  <option value="1899"  >1899</option>  <option value="1900"  >1900</option>  <option value="1901"  >1901</option>  <option value="1902"  >1902</option>  <option value="1903"  >1903</option>  <option value="1904"  >1904</option>  <option value="1905"  >1905</option>  <option value="1906"  >1906</option>  <option value="1907"  >1907</option>  <option value="1908"  >1908</option>  <option value="1909"  >1909</option>  <option value="1910"  >1910</option>  <option value="1911"  >1911</option>  <option value="1912"  >1912</option>  <option value="1913"  >1913</option>  <option value="1914"  >1914</option>  <option value="1915"  >1915</option>  <option value="1916"  >1916</option>  <option value="1917"  >1917</option>  <option value="1918"  >1918</option>  <option value="1919"  >1919</option>  <option value="1920"  >1920</option>  <option value="1921"  >1921</option>  <option value="1922"  >1922</option>  <option value="1923"  >1923</option>  <option value="1924"  >1924</option>  <option value="1925"  >1925</option>  <option value="1926"  >1926</option>  <option value="1927"  >1927</option>  <option value="1928"  >1928</option>  <option value="1929"  >1929</option>  <option value="1930"  >1930</option>  <option value="1931"  >1931</option>  <option value="1932"  >1932</option>  <option value="1933"  >1933</option>  <option value="1934"  >1934</option>  <option value="1935"  >1935</option>  <option value="1936"  >1936</option>  <option value="1937"  >1937</option>  <option value="1938"  >1938</option>  <option value="1939"  >1939</option>  <option value="1940"  >1940</option>  <option value="1941"  >1941</option>  <option value="1942"  >1942</option>  <option value="1943"  >1943</option>  <option value="1944"  >1944</option>  <option value="1945"  >1945</option>  <option value="1946"  >1946</option>  <option value="1947"  >1947</option>  <option value="1948"  >1948</option>  <option value="1949"  >1949</option>  <option value="1950"  >1950</option>  <option value="1951"  >1951</option>  <option value="1952"  >1952</option>  <option value="1953"  >1953</option>  <option value="1954"  >1954</option>  <option value="1955"  >1955</option>  <option value="1956"  >1956</option>  <option value="1957"  >1957</option>  <option value="1958"  >1958</option>  <option value="1959"  >1959</option>  <option value="1960"  >1960</option>  <option value="1961"  >1961</option>  <option value="1962"  >1962</option>  <option value="1963"  >1963</option>  <option value="1964"  >1964</option>  <option value="1965"  >1965</option>  <option value="1966"  >1966</option>  <option value="1967"  >1967</option>  <option value="1968"  >1968</option>  <option value="1969"  >1969</option>  <option value="1970"  >1970</option>  <option value="1971"  >1971</option>  <option value="1972"  >1972</option>  <option value="1973"  >1973</option>  <option value="1974"  >1974</option>  <option value="1975"  >1975</option>  <option value="1976"  >1976</option>  <option value="1977"  >1977</option>  <option value="1978"  >1978</option>  <option value="1979"  >1979</option>  <option value="1980"  >1980</option>  <option value="1981"  >1981</option>  <option value="1982"  >1982</option>  <option value="1983"  >1983</option>  <option value="1984"  >1984</option>  <option value="1985"  >1985</option>  <option value="1986"  >1986</option>  <option value="1987"  >1987</option>  <option value="1988"  >1988</option>  <option value="1989"  >1989</option>  <option value="1990"  >1990</option>  <option value="1991"  >1991</option>  <option value="1992"  >1992</option>  <option value="1993"  >1993</option>  <option value="1994"  >1994</option>  <option value="1995"  >1995</option>  <option value="1996"  >1996</option>  <option value="1997"  >1997</option>  <option value="1998"  >1998</option>  <option value="1999"  >1999</option>  <option value="2000"  >2000</option>  <option value="2001"  >2001</option>  <option value="2002"  >2002</option>  <option value="2003"  >2003</option>  <option value="2004"  >2004</option>  <option value="2005"  >2005</option>  <option value="2006"  >2006</option>  <option value="2007"  >2007</option>  <option value="2008"  >2008</option>  <option value="2009"  >2009</option>  <option value="2010"  >2010</option>  <option value="2011"  >2011</option>  <option value="2012"  >2012</option>  <option value="2013"  >2013</option>  <option value="2014"  >2014</option>  <option value="2015"  >2015</option>  <option value="2016"  >2016</option>  <option value="2017"  >2017</option>  <option value="2018"  >2018</option>  <option value="2019"  >2019</option>  <option value="2020"  >2020</option>  <option value="2021"  >2021</option>  <option value="2022"  >2022</option>  <option value="2023"  >2023</option>  <option value="2024"  >2024</option> </select><input type="file" name="ugcDoc" id="ugcDoc" style="width: 25%; float:left;position: relative; top: 3px;margin-left: 5px;" />';
						$('#qualified_change_lisdfdt').html(qualified_choosesdfasd_html);
					}else if(qualified_val=='no'){
						$('#qualified_change_lisdfdt').html('');
					}
				});
			</script>
          </tr>
        </tbody>
      </table>
      <table class="form-tb1" cellpadding="10" cellspacing="0" width="100%;" id="form_table">
        <tbody>
            <tr>
                <td colspan="7" align="center">
                    <h3 style="margin-bottom:5px;">Details of Employment Experience <span style="color:#f00;font-size: 15px; font-size: larger;"> * </span></h3>
                    <span style="font-weight:bold;">(In chronological order starting with the most recent)</span>
                </td>
            </tr>
            <tr>
                <td rowspan="2" align="center"><b>Name of Employer/Status of Institute/University (Govt. /Quasi Govt./Autonomous etc.)<span style="color:#f00;font-size: 15px;"> * </span></b></td>
                <td rowspan="2" align="center"><b>Post held/Designation<span style="color:#f00;font-size: 15px;"> * </span></b></td>
                <td colspan="2" align="center"><b>Period of Employment<span style="color:#f00;font-size: 15px;"> * </span></b></td>
                <td rowspan="2" align="center"><b>Basic salary last drawn, pay scale and Grade Pay<span style="color:#f00;font-size: 15px;"> * </span></b></td>
                <td rowspan="2" align="center"><b>Nature of duties</b><span style="color:#f00;font-size: 15px;"> * </span></td>
                <td rowspan="2" align="center"><b>Supporting Documents</b><span style="color:#f00;font-size: 15px;"> * </span></td>
            </tr>
            <tr>
                <td align="center" style="height: 20px;"><b>From</b></td>
                <td align="center" style="height: 20px;"><b>To</b></td>
            </tr>
            <tr id="tr_row1">
                <td align="center"><input type="text" name="nameOfEmployer1" id="nameOfEmployer1" class="form-tb-input2" placeholder="Name of Employer/Status of Institute/University (Govt. /Quasi Govt./Autonomous etc.)" value="" ></td>
                <td align="center"><input type="text" name="postHeldDesignation1" id="postHeldDesignation1" class="form-tb-input2" placeholder="Post held/Designation" value="" ></td>
                <td align="center" style="width: 190px;">
                    <select name="periodEmploymentFromMonth1" id="periodEmploymentFromMonth1" class="form-tb-input1" style="width: 90px;float: left; margin-right: 5px;">
                        <option value=""> Month</option> 
                        <option value="Jan" >Jan</option>
                    <option value="Feb" >Feb</option>
                    <option value="March" >March</option>
                    <option value="April" >April</option>
                    <option value="May" >May</option>
                    <option value="June" >June</option>
                    <option value="July" >July</option>
                    <option value="Aug" >Aug</option>
                    <option value="Sept" >Sept</option>
                    <option value="Oct" >Oct</option>
                    <option value="Nov" >Nov</option>
                    <option value="Dec" >Dec</option>
                    </select>
                    <select name="periodEmploymentFromYear1" id="periodEmploymentFromYear1" class="form-tb-input1" style="width: 90px;float: left; margin-right: 5px;">
                        <option value="">Year</option>
                       <option value="1950">1950</option>
                        <option value="1951">1951</option>
                        <option value="1952">1952</option>
                        <option value="1953">1953</option>
                        <option value="1954">1954</option>
                        <option value="1955">1955</option>
                        <option value="1956">1956</option>
                        <option value="1957">1957</option>
                        <option value="1958">1958</option>
                        <option value="1959">1959</option>
                        <option value="1960">1960</option>
                        <option value="1961">1961</option>
                        <option value="1962">1962</option>
                        <option value="1963">1963</option>
                        <option value="1964">1964</option>
                        <option value="1965">1965</option>
                        <option value="1966">1966</option>
                        <option value="1967">1967</option>
                        <option value="1968">1968</option>
                        <option value="1969">1969</option>
                        <option value="1970">1970</option>
                        <option value="1971">1971</option>
                        <option value="1972">1972</option>
                        <option value="1973">1973</option>
                        <option value="1974">1974</option>
                        <option value="1975">1975</option>
                        <option value="1976">1976</option>
                        <option value="1977">1977</option>
                        <option value="1978">1978</option>
                        <option value="1979">1979</option>
                        <option value="1980">1980</option>
                        <option value="1981">1981</option>
                        <option value="1982">1982</option>
                        <option value="1983">1983</option>
                        <option value="1984">1984</option>
                        <option value="1985">1985</option>
                        <option value="1986">1986</option>
                        <option value="1987">1987</option>
                        <option value="1988">1988</option>
                        <option value="1989">1989</option>
                        <option value="1990">1990</option>
                        <option value="1991">1991</option>
                        <option value="1992">1992</option>
                        <option value="1993">1993</option>
                        <option value="1994">1994</option>
                        <option value="1995">1995</option>
                        <option value="1996">1996</option>
                        <option value="1997">1997</option>
                        <option value="1998">1998</option>
                        <option value="1999">1999</option>
                        <option value="2000">2000</option>
                        <option value="2001">2001</option>
                        <option value="2002">2002</option>
                        <option value="2003">2003</option>
                        <option value="2004">2004</option>
                        <option value="2005">2005</option>
                        <option value="2006">2006</option>
                        <option value="2007">2007</option>
                        <option value="2008">2008</option>
                        <option value="2009">2009</option>
                        <option value="2010">2010</option>
                        <option value="2011">2011</option>
                        <option value="2012">2012</option>
                        <option value="2013">2013</option>
                        <option value="2014">2014</option>
                        <option value="2015">2015</option>
                        <option value="2016">2016</option>
                        <option value="2017">2017</option>
                        <option value="2018">2018</option>
                        <option value="2019">2019</option>
                        <option value="2020">2020</option>
                        <option value="2021">2021</option>
                        <option value="2022">2022</option>
                        <option value="2023">2023</option>
                        <option value="2024">2024</option>
                    </select>
                </td>
                <td align="center" style="width: 190px;">
                    <select name="periodEmploymentToMonth1" id="periodEmploymentToMonth1" class="form-tb-input1" style="width: 90px;float: left; margin-right: 5px;">
                                  <option value="">Month</option>
                                   <option value="Jan" >Jan</option>
                                    <option value="Feb" >Feb</option>
                                    <option value="March" >March</option>
                                    <option value="April" >April</option>
                                    <option value="May" >May</option>
                                    <option value="June" >June</option>
                                    <option value="July" >July</option>
                                    <option value="Aug" >Aug</option>
                                    <option value="Sept" >Sept</option>
                                    <option value="Oct" >Oct</option>
                                    <option value="Nov" >Nov</option>
                                    <option value="Dec" >Dec</option>
                        <!-- More months -->
                    </select>
                    <select name="periodEmploymentToYear1" id="periodEmploymentToYear1" class="form-tb-input1" style="width: 90px;float: left; margin-right: 5px;">
                        <option value="">Year</option>
                        <option value="1950">1950</option>
                        <option value="1951">1951</option>
                        <option value="1952">1952</option>
                        <option value="1953">1953</option>
                        <option value="1954">1954</option>
                        <option value="1955">1955</option>
                        <option value="1956">1956</option>
                        <option value="1957">1957</option>
                        <option value="1958">1958</option>
                        <option value="1959">1959</option>
                        <option value="1960">1960</option>
                        <option value="1961">1961</option>
                        <option value="1962">1962</option>
                        <option value="1963">1963</option>
                        <option value="1964">1964</option>
                        <option value="1965">1965</option>
                        <option value="1966">1966</option>
                        <option value="1967">1967</option>
                        <option value="1968">1968</option>
                        <option value="1969">1969</option>
                        <option value="1970">1970</option>
                        <option value="1971">1971</option>
                        <option value="1972">1972</option>
                        <option value="1973">1973</option>
                        <option value="1974">1974</option>
                        <option value="1975">1975</option>
                        <option value="1976">1976</option>
                        <option value="1977">1977</option>
                        <option value="1978">1978</option>
                        <option value="1979">1979</option>
                        <option value="1980">1980</option>
                        <option value="1981">1981</option>
                        <option value="1982">1982</option>
                        <option value="1983">1983</option>
                        <option value="1984">1984</option>
                        <option value="1985">1985</option>
                        <option value="1986">1986</option>
                        <option value="1987">1987</option>
                        <option value="1988">1988</option>
                        <option value="1989">1989</option>
                        <option value="1990">1990</option>
                        <option value="1991">1991</option>
                        <option value="1992">1992</option>
                        <option value="1993">1993</option>
                        <option value="1994">1994</option>
                        <option value="1995">1995</option>
                        <option value="1996">1996</option>
                        <option value="1997">1997</option>
                        <option value="1998">1998</option>
                        <option value="1999">1999</option>
                        <option value="2000">2000</option>
                        <option value="2001">2001</option>
                        <option value="2002">2002</option>
                        <option value="2003">2003</option>
                        <option value="2004">2004</option>
                        <option value="2005">2005</option>
                        <option value="2006">2006</option>
                        <option value="2007">2007</option>
                        <option value="2008">2008</option>
                        <option value="2009">2009</option>
                        <option value="2010">2010</option>
                        <option value="2011">2011</option>
                        <option value="2012">2012</option>
                        <option value="2013">2013</option>
                        <option value="2014">2014</option>
                        <option value="2015">2015</option>
                        <option value="2016">2016</option>
                        <option value="2017">2017</option>
                        <option value="2018">2018</option>
                        <option value="2019">2019</option>
                        <option value="2020">2020</option>
                        <option value="2021">2021</option>
                        <option value="2022">2022</option>
                        <option value="2023">2023</option>
                        <option value="2024">2024</option>
                    </select>
                </td>
                <td align="center"><input type="text" name="basicSalaryLastDrawnPayScaleAndGradePay1" id="basicSalaryLastDrawnPayScaleAndGradePay1" class="form-tb-input2" placeholder="Salary" value=""></td>
                <td align="center"><input type="text" name="natureOfDuties1" id="natureOfDuties1" class="form-tb-input2" placeholder="Nature of duties" value=""></td>
                <td align="center"><input type="file" style="border:none; width:100% !important; padding-left: 0px;" name="filePaths1" id="filePaths1" > &nbsp;<span style="cursor:pointer; background-color: #148534;color: #fff; padding: 5px 6px;border-radius: 3px;position: relative;top: -27px;left: 95px;" onclick="addFamilyMembersRows();" class="smallgreenbtn"><i class="fa fa-plus-circle"></i> Add More</span></td>
            </tr>
            <input type="hidden" name="tr_count" id="tr_count" value="2">
        </tbody>

          <script>
		function addFamilyMembersRows() {
    var rowCount = parseInt(document.getElementById("tr_count").value);

    var newRow = `
    <tr id="tr_row${rowCount}">
        <td align="center"><input type="text" name="nameOfEmployer${rowCount}" id="nameOfEmployer${rowCount}" class="form-tb-input2" placeholder="Name of Employer/Status of Institute/University (Govt. /Quasi Govt./Autonomous etc.)" value=""></td>
        <td align="center"><input type="text" name="postHeldDesignation${rowCount}" id="postHeldDesignation${rowCount}" class="form-tb-input2" placeholder="Post held/Designation" value=""></td>
        <td align="center" style="width: 190px;">
            <select name="periodEmploymentFromMonth${rowCount}" id="periodEmploymentFromMonth${rowCount}" class="form-tb-input1" style="width: 90px;float: left; margin-right: 5px;">
               <option value=""> Month</option> 
					 <option value="Jan" >Jan</option>
                 <option value="Feb" >Feb</option>
                 <option value="March" >March</option>
                 <option value="April" >April</option>
                 <option value="May" >May</option>
                 <option value="June" >June</option>
                 <option value="July" >July</option>
                 <option value="Aug" >Aug</option>
                 <option value="Sept" >Sept</option>
                 <option value="Oct" >Oct</option>
                 <option value="Nov" >Nov</option>
                 <option value="Dec" >Dec</option>
            </select>
            <select name="periodEmploymentFromYear${rowCount}" id="periodEmploymentFromYear${rowCount}" class="form-tb-input1" style="width: 90px;float: left; margin-right: 5px;">
               <option value=""> Year</option>
                                <option value="1950">1950</option>
                                <option value="1951">1951</option>
                                <option value="1952">1952</option>
                                <option value="1953">1953</option>
                                <option value="1954">1954</option>
                                <option value="1955">1955</option>
                                <option value="1956">1956</option>
                                <option value="1957">1957</option>
                                <option value="1958">1958</option>
                                <option value="1959">1959</option>
                                <option value="1960">1960</option>
                                <option value="1961">1961</option>
                                <option value="1962">1962</option>
                                <option value="1963">1963</option>
                                <option value="1964">1964</option>
                                <option value="1965">1965</option>
                                <option value="1966">1966</option>
                                <option value="1967">1967</option>
                                <option value="1968">1968</option>
                                <option value="1969">1969</option>
                                <option value="1970">1970</option>
                                <option value="1971">1971</option>
                                <option value="1972">1972</option>
                                <option value="1973">1973</option>
                                <option value="1974">1974</option>
                                <option value="1975">1975</option>
                                <option value="1976">1976</option>
                                <option value="1977">1977</option>
                                <option value="1978">1978</option>
                                <option value="1979">1979</option>
                                <option value="1980">1980</option>
                                <option value="1981">1981</option>
                                <option value="1982">1982</option>
                                <option value="1983">1983</option>
                                <option value="1984">1984</option>
                                <option value="1985">1985</option>
                                <option value="1986">1986</option>
                                <option value="1987">1987</option>
                                <option value="1988">1988</option>
                                <option value="1989">1989</option>
                                <option value="1990">1990</option>
                                <option value="1991">1991</option>
                                <option value="1992">1992</option>
                                <option value="1993">1993</option>
                                <option value="1994">1994</option>
                                <option value="1995">1995</option>
                                <option value="1996">1996</option>
                                <option value="1997">1997</option>
                                <option value="1998">1998</option>
                                <option value="1999">1999</option>
                                <option value="2000">2000</option>
                                <option value="2001">2001</option>
                                <option value="2002">2002</option>
                                <option value="2003">2003</option>
                                <option value="2004">2004</option>
                                <option value="2005">2005</option>
                                <option value="2006">2006</option>
                                <option value="2007">2007</option>
                                <option value="2008">2008</option>
                                <option value="2009">2009</option>
                                <option value="2010">2010</option>
                                <option value="2011">2011</option>
                                <option value="2012">2012</option>
                                <option value="2013">2013</option>
                                <option value="2014">2014</option>
                                <option value="2015">2015</option>
                                <option value="2016">2016</option>
                                <option value="2017">2017</option>
                                <option value="2018">2018</option>
                                <option value="2019">2019</option>
                                <option value="2020">2020</option>
                                <option value="2021">2021</option>
                                <option value="2022">2022</option>
                                <option value="2023">2023</option>
                                <option value="2024">2024</option>
            </select>
        </td>
        <td align="center" style="width: 190px;">
            <select name="periodEmploymentToMonth${rowCount}" id="periodEmploymentToMonth${rowCount}" class="form-tb-input1" style="width: 90px;float: left; margin-right: 5px;">
                <option value=""> Month</option> 
					 <option value="Jan" >Jan</option>
                 <option value="Feb" >Feb</option>
                 <option value="March" >March</option>
                 <option value="April" >April</option>
                 <option value="May" >May</option>
                 <option value="June" >June</option>
                 <option value="July" >July</option>
                 <option value="Aug" >Aug</option>
                 <option value="Sept" >Sept</option>
                 <option value="Oct" >Oct</option>
                 <option value="Nov" >Nov</option>
                 <option value="Dec" >Dec</option>
            </select>
            <select name="periodEmploymentToYear${rowCount}" id="periodEmploymentToYear${rowCount}" class="form-tb-input1" style="width: 90px;float: left; margin-right: 5px;">
               <option value=""> Year</option>
                                <option value="1950">1950</option>
                                <option value="1951">1951</option>
                                <option value="1952">1952</option>
                                <option value="1953">1953</option>
                                <option value="1954">1954</option>
                                <option value="1955">1955</option>
                                <option value="1956">1956</option>
                                <option value="1957">1957</option>
                                <option value="1958">1958</option>
                                <option value="1959">1959</option>
                                <option value="1960">1960</option>
                                <option value="1961">1961</option>
                                <option value="1962">1962</option>
                                <option value="1963">1963</option>
                                <option value="1964">1964</option>
                                <option value="1965">1965</option>
                                <option value="1966">1966</option>
                                <option value="1967">1967</option>
                                <option value="1968">1968</option>
                                <option value="1969">1969</option>
                                <option value="1970">1970</option>
                                <option value="1971">1971</option>
                                <option value="1972">1972</option>
                                <option value="1973">1973</option>
                                <option value="1974">1974</option>
                                <option value="1975">1975</option>
                                <option value="1976">1976</option>
                                <option value="1977">1977</option>
                                <option value="1978">1978</option>
                                <option value="1979">1979</option>
                                <option value="1980">1980</option>
                                <option value="1981">1981</option>
                                <option value="1982">1982</option>
                                <option value="1983">1983</option>
                                <option value="1984">1984</option>
                                <option value="1985">1985</option>
                                <option value="1986">1986</option>
                                <option value="1987">1987</option>
                                <option value="1988">1988</option>
                                <option value="1989">1989</option>
                                <option value="1990">1990</option>
                                <option value="1991">1991</option>
                                <option value="1992">1992</option>
                                <option value="1993">1993</option>
                                <option value="1994">1994</option>
                                <option value="1995">1995</option>
                                <option value="1996">1996</option>
                                <option value="1997">1997</option>
                                <option value="1998">1998</option>
                                <option value="1999">1999</option>
                                <option value="2000">2000</option>
                                <option value="2001">2001</option>
                                <option value="2002">2002</option>
                                <option value="2003">2003</option>
                                <option value="2004">2004</option>
                                <option value="2005">2005</option>
                                <option value="2006">2006</option>
                                <option value="2007">2007</option>
                                <option value="2008">2008</option>
                                <option value="2009">2009</option>
                                <option value="2010">2010</option>
                                <option value="2011">2011</option>
                                <option value="2012">2012</option>
                                <option value="2013">2013</option>
                                <option value="2014">2014</option>
                                <option value="2015">2015</option>
                                <option value="2016">2016</option>
                                <option value="2017">2017</option>
                                <option value="2018">2018</option>
                                <option value="2019">2019</option>
                                <option value="2020">2020</option>
                                <option value="2021">2021</option>
                                <option value="2022">2022</option>
                                <option value="2023">2023</option>
                                <option value="2024">2024</option>
            </select>
        </td>
        <td align="center"><input type="text" name="basicSalaryLastDrawnPayScaleAndGradePay${rowCount}" id="basicSalaryLastDrawnPayScaleAndGradePay${rowCount}" class="form-tb-input2" placeholder="Salary" value=""></td>
        <td align="center"><input type="text" name="natureOfDuties${rowCount}" id="natureOfDuties${rowCount}" class="form-tb-input2" placeholder="Nature of duties" value=""></td>
        <td align="center"><input type="file" style="border:none; width:100% !important; padding-left: 0px;" name="filePaths${rowCount}" id="filePaths${rowCount}"> &nbsp;<a href="javascript:void(0);" style="background-color: #cd0a0a;text-decoration:none;color: #fff; padding: 5px 6px;border-radius: 3px;position: relative;top: -27px;left: 95px;" onclick="removeFamilyMembersRows(${rowCount});" class="smallgreybtn"><i class="fa fa-trash"></i> Remove</a></td>
    </tr>`;

    $('#form_table').append(newRow);
    document.getElementById("tr_count").value = rowCount + 1;
}

function removeFamilyMembersRows(rowNum) {
    $('#tr_row' + rowNum).remove();
}

</script>
      </table>

      <table class="form-tb1" id="form-teaching" cellpadding="10" cellspacing="0"  width="100%;">
        <tbody>
            <tr>
            <td colspan="7" align="center"><h3 style="margin-bottom:5px;">Summary Of Teaching Experience/Performance </h3></td>
          </tr>
            <tr>
            <td rowspan="2" align="center"><b>Teaching Experience</b></td>
            <td rowspan="2" align="center"><b>From</b></td>
            <td rowspan="2" align="center"><b>To</b></td>
            <td colspan="2" align="center"><b>Total</b></td>
            <td rowspan="2" align="center"><b>Supporting Documents</b></td>
          </tr>
          <tr>
            <td align="center" style="height: 20px;"><b>Years</b></td>
            <td align="center" style="height: 20px;"><b>Months</b></td>
          </tr>
            <tr>
            <td align="center">Under Graduate</td>
            <td align="center"><input type="text" value="" name="undergraduateFrom" id="undergraduateFrom" class="date_pickere_byclass"  placeholder="From(DD/MM/YYYY)" ></td>
            <td align="center"><input type="text" value="" name="undergraduateTo" id="undergraduateTo" class="date_pickere_byclass"  placeholder="To (DD/MM/YYYY)" ></td>
            <td style="width: 190px;" align="center">
            <select name="undergraduateTotalYears" id="undergraduateTotalYears" class="form-tb-input1">
                <option value="">Years</option>
                                <option value="0"  >0</option>
                                <option value="1"  >1</option>
                                <option value="2"  >2</option>
                                <option value="3"  >3</option>
                                <option value="4"  >4</option>
                                <option value="5"  >5</option>
                                <option value="6"  >6</option>
                                <option value="7"  >7</option>
                                <option value="8"  >8</option>
                                <option value="9"  >9</option>
                                <option value="10"  >10</option>
                                <option value="11"  >11</option>
                                <option value="12"  >12</option>
                                <option value="13"  >13</option>
                                <option value="14"  >14</option>
                                <option value="15"  >15</option>
                                <option value="16"  >16</option>
                                <option value="17"  >17</option>
                                <option value="18"  >18</option>
                                <option value="19"  >19</option>
                                <option value="20"  >20</option>
                                <option value="21"  >21</option>
                                <option value="22"  >22</option>
                                <option value="23"  >23</option>
                                <option value="24"  >24</option>
                                <option value="25"  >25</option>
                                <option value="26"  >26</option>
                                <option value="27"  >27</option>
                                <option value="28"  >28</option>
                                <option value="29"  >29</option>
                                <option value="30"  >30</option>
                                <option value="31"  >31</option>
                                <option value="32"  >32</option>
                                <option value="33"  >33</option>
                                <option value="34"  >34</option>
                                <option value="35"  >35</option>
                                <option value="36"  >36</option>
                                <option value="37"  >37</option>
                                <option value="38"  >38</option>
                                <option value="39"  >39</option>
                                <option value="40"  >40</option>
                                <option value="41"  >41</option>
                                <option value="42"  >42</option>
                                <option value="43"  >43</option>
                                <option value="44"  >44</option>
                                <option value="45"  >45</option>
                                <option value="46"  >46</option>
                                <option value="47"  >47</option>
                                <option value="48"  >48</option>
                                <option value="49"  >49</option>
                                <option value="50"  >50</option>
                                <option value="51"  >51</option>
                                <option value="52"  >52</option>
                                <option value="53"  >53</option>
                                <option value="54"  >54</option>
                                <option value="55"  >55</option>
                                <option value="56"  >56</option>
                                <option value="57"  >57</option>
                                <option value="58"  >58</option>
                                <option value="59"  >59</option>
                                <option value="60"  >60</option>
                              </select>
            </td>
            <td style="width: 190px;" align="center">
            <select name="undergraduateTotalMonths" id="undergraduateTotalMonths" class="form-tb-input1">
                <option value="">Months</option>
                                <option value="0"  >0</option>
                                <option value="1"  >1</option>
                                <option value="2"  >2</option>
                                <option value="3"  >3</option>
                                <option value="4"  >4</option>
                                <option value="5"  >5</option>
                                <option value="6"  >6</option>
                                <option value="7"  >7</option>
                                <option value="8"  >8</option>
                                <option value="9"  >9</option>
                                <option value="10"  >10</option>
                                <option value="11"  >11</option>
                                <option value="12"  >12</option>
                                <option value="13"  >13</option>
                                <option value="14"  >14</option>
                                <option value="15"  >15</option>
                                <option value="16"  >16</option>
                                <option value="17"  >17</option>
                                <option value="18"  >18</option>
                                <option value="19"  >19</option>
                                <option value="20"  >20</option>
                                <option value="21"  >21</option>
                                <option value="22"  >22</option>
                                <option value="23"  >23</option>
                                <option value="24"  >24</option>
                                <option value="25"  >25</option>
                                <option value="26"  >26</option>
                                <option value="27"  >27</option>
                                <option value="28"  >28</option>
                                <option value="29"  >29</option>
                                <option value="30"  >30</option>
                                <option value="31"  >31</option>
                                <option value="32"  >32</option>
                                <option value="33"  >33</option>
                                <option value="34"  >34</option>
                                <option value="35"  >35</option>
                                <option value="36"  >36</option>
                                <option value="37"  >37</option>
                                <option value="38"  >38</option>
                                <option value="39"  >39</option>
                                <option value="40"  >40</option>
                                <option value="41"  >41</option>
                                <option value="42"  >42</option>
                                <option value="43"  >43</option>
                                <option value="44"  >44</option>
                                <option value="45"  >45</option>
                                <option value="46"  >46</option>
                                <option value="47"  >47</option>
                                <option value="48"  >48</option>
                                <option value="49"  >49</option>
                                <option value="50"  >50</option>
                                <option value="51"  >51</option>
                                <option value="52"  >52</option>
                                <option value="53"  >53</option>
                                <option value="54"  >54</option>
                                <option value="55"  >55</option>
                                <option value="56"  >56</option>
                                <option value="57"  >57</option>
                                <option value="58"  >58</option>
                                <option value="59"  >59</option>
                                <option value="60"  >60</option>
                              </select>
            </td>
            <td align="center"><input type="file" style="border:none; width:96% !important; padding-left: 0px;" name="undergraduateSupportingDocuments"  id="undergraduateSupportingDocuments" > </td>
          </tr>
          <tr>
            <td align="center">Post Graduate</td>
            <td align="center"><input type="text" value="" name="postGraduateFrom" id="postGraduateFrom" class="date_pickere_byclass" placeholder="From(DD/MM/YYYY)" ></td>
            <td align="center"><input type="text" value="" name="postGraduateTo" id="postGraduateTo" class="date_pickere_byclass"  placeholder="To(DD/MM/YYYY)" ></td>
            <td align="center" style="width: 190px;">
            <select name="postGraduateTotalYears" id="postGraduateTotalYears" class="form-tb-input1">
                <option value="">Years</option>
                                <option value="0"  >0</option>
                                <option value="1"  >1</option>
                                <option value="2"  >2</option>
                                <option value="3"  >3</option>
                                <option value="4"  >4</option>
                                <option value="5"  >5</option>
                                <option value="6"  >6</option>
                                <option value="7"  >7</option>
                                <option value="8"  >8</option>
                                <option value="9"  >9</option>
                                <option value="10"  >10</option>
                                <option value="11"  >11</option>
                                <option value="12"  >12</option>
                                <option value="13"  >13</option>
                                <option value="14"  >14</option>
                                <option value="15"  >15</option>
                                <option value="16"  >16</option>
                                <option value="17"  >17</option>
                                <option value="18"  >18</option>
                                <option value="19"  >19</option>
                                <option value="20"  >20</option>
                                <option value="21"  >21</option>
                                <option value="22"  >22</option>
                                <option value="23"  >23</option>
                                <option value="24"  >24</option>
                                <option value="25"  >25</option>
                                <option value="26"  >26</option>
                                <option value="27"  >27</option>
                                <option value="28"  >28</option>
                                <option value="29"  >29</option>
                                <option value="30"  >30</option>
                                <option value="31"  >31</option>
                                <option value="32"  >32</option>
                                <option value="33"  >33</option>
                                <option value="34"  >34</option>
                                <option value="35"  >35</option>
                                <option value="36"  >36</option>
                                <option value="37"  >37</option>
                                <option value="38"  >38</option>
                                <option value="39"  >39</option>
                                <option value="40"  >40</option>
                                <option value="41"  >41</option>
                                <option value="42"  >42</option>
                                <option value="43"  >43</option>
                                <option value="44"  >44</option>
                                <option value="45"  >45</option>
                                <option value="46"  >46</option>
                                <option value="47"  >47</option>
                                <option value="48"  >48</option>
                                <option value="49"  >49</option>
                                <option value="50"  >50</option>
                                <option value="51"  >51</option>
                                <option value="52"  >52</option>
                                <option value="53"  >53</option>
                                <option value="54"  >54</option>
                                <option value="55"  >55</option>
                                <option value="56"  >56</option>
                                <option value="57"  >57</option>
                                <option value="58"  >58</option>
                                <option value="59"  >59</option>
                                <option value="60"  >60</option>
                              </select>
            </td>
            <td style="width: 190px;" align="center">
            <select name="postGraduateTotalMonths" id="postGraduateTotalMonths" class="form-tb-input1">
                <option value="">Months</option>
                                <option value="0"  >0</option>
                                <option value="1"  >1</option>
                                <option value="2"  >2</option>
                                <option value="3"  >3</option>
                                <option value="4"  >4</option>
                                <option value="5"  >5</option>
                                <option value="6"  >6</option>
                                <option value="7"  >7</option>
                                <option value="8"  >8</option>
                                <option value="9"  >9</option>
                                <option value="10"  >10</option>
                                <option value="11"  >11</option>
                                <option value="12"  >12</option>
                                <option value="13"  >13</option>
                                <option value="14"  >14</option>
                                <option value="15"  >15</option>
                                <option value="16"  >16</option>
                                <option value="17"  >17</option>
                                <option value="18"  >18</option>
                                <option value="19"  >19</option>
                                <option value="20"  >20</option>
                                <option value="21"  >21</option>
                                <option value="22"  >22</option>
                                <option value="23"  >23</option>
                                <option value="24"  >24</option>
                                <option value="25"  >25</option>
                                <option value="26"  >26</option>
                                <option value="27"  >27</option>
                                <option value="28"  >28</option>
                                <option value="29"  >29</option>
                                <option value="30"  >30</option>
                                <option value="31"  >31</option>
                                <option value="32"  >32</option>
                                <option value="33"  >33</option>
                                <option value="34"  >34</option>
                                <option value="35"  >35</option>
                                <option value="36"  >36</option>
                                <option value="37"  >37</option>
                                <option value="38"  >38</option>
                                <option value="39"  >39</option>
                                <option value="40"  >40</option>
                                <option value="41"  >41</option>
                                <option value="42"  >42</option>
                                <option value="43"  >43</option>
                                <option value="44"  >44</option>
                                <option value="45"  >45</option>
                                <option value="46"  >46</option>
                                <option value="47"  >47</option>
                                <option value="48"  >48</option>
                                <option value="49"  >49</option>
                                <option value="50"  >50</option>
                                <option value="51"  >51</option>
                                <option value="52"  >52</option>
                                <option value="53"  >53</option>
                                <option value="54"  >54</option>
                                <option value="55"  >55</option>
                                <option value="56"  >56</option>
                                <option value="57"  >57</option>
                                <option value="58"  >58</option>
                                <option value="59"  >59</option>
                                <option value="60"  >60</option>
                              </select>
            </td>
            <td align="center"><input type="file" style="border:none; width:96% !important; padding-left: 0px;" name="postGraduateSupportingDocuments"  id="postGraduateSupportingDocuments" > </td>
          </tr>
          <tr>
            <td align="center">Total Teaching Experience</td>
            <td align="center"><input type="text" value="" name="totalTeachingFrom" id="totalTeachingFrom" class="date_pickere_byclass" placeholder="From(DD/MM/YYYY)" ></td>
            <td align="center"><input type="text" value="" name="TotalTeachingTo" id="TotalTeachingTo" class="date_pickere_byclass"  placeholder="To(DD/MM/YYYY)" ></td>
            <td align="center" style="width: 190px;">
            <select name="totalTeachingExperienceYears" id="totalTeachingExperienceYears" class="form-tb-input1">
                <option value="">Years</option>
                                <option value="0"  >0</option>
                                <option value="1"  >1</option>
                                <option value="2"  >2</option>
                                <option value="3"  >3</option>
                                <option value="4"  >4</option>
                                <option value="5"  >5</option>
                                <option value="6"  >6</option>
                                <option value="7"  >7</option>
                                <option value="8"  >8</option>
                                <option value="9"  >9</option>
                                <option value="10"  >10</option>
                                <option value="11"  >11</option>
                                <option value="12"  >12</option>
                                <option value="13"  >13</option>
                                <option value="14"  >14</option>
                                <option value="15"  >15</option>
                                <option value="16"  >16</option>
                                <option value="17"  >17</option>
                                <option value="18"  >18</option>
                                <option value="19"  >19</option>
                                <option value="20"  >20</option>
                                <option value="21"  >21</option>
                                <option value="22"  >22</option>
                                <option value="23"  >23</option>
                                <option value="24"  >24</option>
                                <option value="25"  >25</option>
                                <option value="26"  >26</option>
                                <option value="27"  >27</option>
                                <option value="28"  >28</option>
                                <option value="29"  >29</option>
                                <option value="30"  >30</option>
                                <option value="31"  >31</option>
                                <option value="32"  >32</option>
                                <option value="33"  >33</option>
                                <option value="34"  >34</option>
                                <option value="35"  >35</option>
                                <option value="36"  >36</option>
                                <option value="37"  >37</option>
                                <option value="38"  >38</option>
                                <option value="39"  >39</option>
                                <option value="40"  >40</option>
                                <option value="41"  >41</option>
                                <option value="42"  >42</option>
                                <option value="43"  >43</option>
                                <option value="44"  >44</option>
                                <option value="45"  >45</option>
                                <option value="46"  >46</option>
                                <option value="47"  >47</option>
                                <option value="48"  >48</option>
                                <option value="49"  >49</option>
                                <option value="50"  >50</option>
                                <option value="51"  >51</option>
                                <option value="52"  >52</option>
                                <option value="53"  >53</option>
                                <option value="54"  >54</option>
                                <option value="55"  >55</option>
                                <option value="56"  >56</option>
                                <option value="57"  >57</option>
                                <option value="58"  >58</option>
                                <option value="59"  >59</option>
                                <option value="60"  >60</option>
                              </select>
            </td>
            <td style="width: 190px;" align="center">
            <select name="totalTeachingExperienceMonths" id="totalTeachingExperienceMonths" class="form-tb-input1">
                <option value="">Months</option>
                                <option value="0"  >0</option>
                                <option value="1"  >1</option>
                                <option value="2"  >2</option>
                                <option value="3"  >3</option>
                                <option value="4"  >4</option>
                                <option value="5"  >5</option>
                                <option value="6"  >6</option>
                                <option value="7"  >7</option>
                                <option value="8"  >8</option>
                                <option value="9"  >9</option>
                                <option value="10"  >10</option>
                                <option value="11"  >11</option>
                                <option value="12"  >12</option>
                                <option value="13"  >13</option>
                                <option value="14"  >14</option>
                                <option value="15"  >15</option>
                                <option value="16"  >16</option>
                                <option value="17"  >17</option>
                                <option value="18"  >18</option>
                                <option value="19"  >19</option>
                                <option value="20"  >20</option>
                                <option value="21"  >21</option>
                                <option value="22"  >22</option>
                                <option value="23"  >23</option>
                                <option value="24"  >24</option>
                                <option value="25"  >25</option>
                                <option value="26"  >26</option>
                                <option value="27"  >27</option>
                                <option value="28"  >28</option>
                                <option value="29"  >29</option>
                                <option value="30"  >30</option>
                                <option value="31"  >31</option>
                                <option value="32"  >32</option>
                                <option value="33"  >33</option>
                                <option value="34"  >34</option>
                                <option value="35"  >35</option>
                                <option value="36"  >36</option>
                                <option value="37"  >37</option>
                                <option value="38"  >38</option>
                                <option value="39"  >39</option>
                                <option value="40"  >40</option>
                                <option value="41"  >41</option>
                                <option value="42"  >42</option>
                                <option value="43"  >43</option>
                                <option value="44"  >44</option>
                                <option value="45"  >45</option>
                                <option value="46"  >46</option>
                                <option value="47"  >47</option>
                                <option value="48"  >48</option>
                                <option value="49"  >49</option>
                                <option value="50"  >50</option>
                                <option value="51"  >51</option>
                                <option value="52"  >52</option>
                                <option value="53"  >53</option>
                                <option value="54"  >54</option>
                                <option value="55"  >55</option>
                                <option value="56"  >56</option>
                                <option value="57"  >57</option>
                                <option value="58"  >58</option>
                                <option value="59"  >59</option>
                                <option value="60"  >60</option>
                              </select>
            </td>
            <td align="center"><input type="file" style="border:none; width:96% !important; padding-left: 0px;" name="totalTeachingExperienceSupportingDocuments"  id="totalTeachingExperienceSupportingDocuments" > </td>
          </tr>
          <tr>
            <td align="center">Short term/Continuing Education/Specialist Courses conducted</td>
            <td align="center"><input type="text" value="" name="shortTermFrom" id="shortTermFrom" class="date_pickere_byclass" placeholder="From(DD/MM/YYYY)" ></td>
            <td align="center"><input type="text" value="" name="shortTermTo" id="shortTermTo" class="date_pickere_byclass" placeholder="To(DD/MM/YYYY)" ></td>
            <td align="center" style="width: 190px;">
            <select name="shortTermYears" id="shortTermYears" class="form-tb-input1">
                <option value="">Years</option>
                                <option value="0"  >0</option>
                                <option value="1"  >1</option>
                                <option value="2"  >2</option>
                                <option value="3"  >3</option>
                                <option value="4"  >4</option>
                                <option value="5"  >5</option>
                                <option value="6"  >6</option>
                                <option value="7"  >7</option>
                                <option value="8"  >8</option>
                                <option value="9"  >9</option>
                                <option value="10"  >10</option>
                                <option value="11"  >11</option>
                                <option value="12"  >12</option>
                                <option value="13"  >13</option>
                                <option value="14"  >14</option>
                                <option value="15"  >15</option>
                                <option value="16"  >16</option>
                                <option value="17"  >17</option>
                                <option value="18"  >18</option>
                                <option value="19"  >19</option>
                                <option value="20"  >20</option>
                                <option value="21"  >21</option>
                                <option value="22"  >22</option>
                                <option value="23"  >23</option>
                                <option value="24"  >24</option>
                                <option value="25"  >25</option>
                                <option value="26"  >26</option>
                                <option value="27"  >27</option>
                                <option value="28"  >28</option>
                                <option value="29"  >29</option>
                                <option value="30"  >30</option>
                                <option value="31"  >31</option>
                                <option value="32"  >32</option>
                                <option value="33"  >33</option>
                                <option value="34"  >34</option>
                                <option value="35"  >35</option>
                                <option value="36"  >36</option>
                                <option value="37"  >37</option>
                                <option value="38"  >38</option>
                                <option value="39"  >39</option>
                                <option value="40"  >40</option>
                                <option value="41"  >41</option>
                                <option value="42"  >42</option>
                                <option value="43"  >43</option>
                                <option value="44"  >44</option>
                                <option value="45"  >45</option>
                                <option value="46"  >46</option>
                                <option value="47"  >47</option>
                                <option value="48"  >48</option>
                                <option value="49"  >49</option>
                                <option value="50"  >50</option>
                                <option value="51"  >51</option>
                                <option value="52"  >52</option>
                                <option value="53"  >53</option>
                                <option value="54"  >54</option>
                                <option value="55"  >55</option>
                                <option value="56"  >56</option>
                                <option value="57"  >57</option>
                                <option value="58"  >58</option>
                                <option value="59"  >59</option>
                                <option value="60"  >60</option>
                              </select>
            </td>
            <td style="width: 190px;" align="center">
            <select name="shortTermMonths" id="shortTermMonths" class="form-tb-input1">
                <option value="">Months</option>
                                <option value="0"  >0</option>
                                <option value="1"  >1</option>
                                <option value="2"  >2</option>
                                <option value="3"  >3</option>
                                <option value="4"  >4</option>
                                <option value="5"  >5</option>
                                <option value="6"  >6</option>
                                <option value="7"  >7</option>
                                <option value="8"  >8</option>
                                <option value="9"  >9</option>
                                <option value="10"  >10</option>
                                <option value="11"  >11</option>
                                <option value="12"  >12</option>
                                <option value="13"  >13</option>
                                <option value="14"  >14</option>
                                <option value="15"  >15</option>
                                <option value="16"  >16</option>
                                <option value="17"  >17</option>
                                <option value="18"  >18</option>
                                <option value="19"  >19</option>
                                <option value="20"  >20</option>
                                <option value="21"  >21</option>
                                <option value="22"  >22</option>
                                <option value="23"  >23</option>
                                <option value="24"  >24</option>
                                <option value="25"  >25</option>
                                <option value="26"  >26</option>
                                <option value="27"  >27</option>
                                <option value="28"  >28</option>
                                <option value="29"  >29</option>
                                <option value="30"  >30</option>
                                <option value="31"  >31</option>
                                <option value="32"  >32</option>
                                <option value="33"  >33</option>
                                <option value="34"  >34</option>
                                <option value="35"  >35</option>
                                <option value="36"  >36</option>
                                <option value="37"  >37</option>
                                <option value="38"  >38</option>
                                <option value="39"  >39</option>
                                <option value="40"  >40</option>
                                <option value="41"  >41</option>
                                <option value="42"  >42</option>
                                <option value="43"  >43</option>
                                <option value="44"  >44</option>
                                <option value="45"  >45</option>
                                <option value="46"  >46</option>
                                <option value="47"  >47</option>
                                <option value="48"  >48</option>
                                <option value="49"  >49</option>
                                <option value="50"  >50</option>
                                <option value="51"  >51</option>
                                <option value="52"  >52</option>
                                <option value="53"  >53</option>
                                <option value="54"  >54</option>
                                <option value="55"  >55</option>
                                <option value="56"  >56</option>
                                <option value="57"  >57</option>
                                <option value="58"  >58</option>
                                <option value="59"  >59</option>
                                <option value="60"  >60</option>
                              </select>
            </td>
            <td align="center"><input type="file" style="border:none; width:96% !important; padding-left: 0px;" name="shortTermSupportingDocument"  id="shortTermSupportingDocument" > </td>
          </tr>
          <tr><td colspan="7"><h3 style="margin-bottom:5px;">Research Experience</h3></td></tr>
          <tr>
            <td align="center">Research Experience other than the period spent for obtaining M.Phil./Ph.D. Research Degree</td>
            <td align="center"><input type="text" value="" name="researchExperienceFrom" id="researchExperienceFrom"   placeholder="From(DD/MM/YYYY)" ></td>
            <td align="center"><input type="text" value="" name="researchExperienceTo" id="researchExperienceTo"  placeholder="To(DD/MM/YYYY)" ></td>
            <td align="center" style="width: 190px;">
            <select name="researchExperienceTotalYears" id="researchExperienceTotalYears" class="form-tb-input1">
                <option value="">Years</option>
                                <option value="0"  >0</option>
                                <option value="1"  >1</option>
                                <option value="2"  >2</option>
                                <option value="3"  >3</option>
                                <option value="4"  >4</option>
                                <option value="5"  >5</option>
                                <option value="6"  >6</option>
                                <option value="7"  >7</option>
                                <option value="8"  >8</option>
                                <option value="9"  >9</option>
                                <option value="10"  >10</option>
                                <option value="11"  >11</option>
                                <option value="12"  >12</option>
                                <option value="13"  >13</option>
                                <option value="14"  >14</option>
                                <option value="15"  >15</option>
                                <option value="16"  >16</option>
                                <option value="17"  >17</option>
                                <option value="18"  >18</option>
                                <option value="19"  >19</option>
                                <option value="20"  >20</option>
                                <option value="21"  >21</option>
                                <option value="22"  >22</option>
                                <option value="23"  >23</option>
                                <option value="24"  >24</option>
                                <option value="25"  >25</option>
                                <option value="26"  >26</option>
                                <option value="27"  >27</option>
                                <option value="28"  >28</option>
                                <option value="29"  >29</option>
                                <option value="30"  >30</option>
                                <option value="31"  >31</option>
                                <option value="32"  >32</option>
                                <option value="33"  >33</option>
                                <option value="34"  >34</option>
                                <option value="35"  >35</option>
                                <option value="36"  >36</option>
                                <option value="37"  >37</option>
                                <option value="38"  >38</option>
                                <option value="39"  >39</option>
                                <option value="40"  >40</option>
                                <option value="41"  >41</option>
                                <option value="42"  >42</option>
                                <option value="43"  >43</option>
                                <option value="44"  >44</option>
                                <option value="45"  >45</option>
                                <option value="46"  >46</option>
                                <option value="47"  >47</option>
                                <option value="48"  >48</option>
                                <option value="49"  >49</option>
                                <option value="50"  >50</option>
                                <option value="51"  >51</option>
                                <option value="52"  >52</option>
                                <option value="53"  >53</option>
                                <option value="54"  >54</option>
                                <option value="55"  >55</option>
                                <option value="56"  >56</option>
                                <option value="57"  >57</option>
                                <option value="58"  >58</option>
                                <option value="59"  >59</option>
                                <option value="60"  >60</option>
                              </select>
            </td>
            <td style="width: 190px;" align="center">
            <select name="researchExperienceTotalMonths" id="researchExperienceTotalMonths" class="form-tb-input1">
                <option value="">Months</option>
                                <option value="0"  >0</option>
                                <option value="1"  >1</option>
                                <option value="2"  >2</option>
                                <option value="3"  >3</option>
                                <option value="4"  >4</option>
                                <option value="5"  >5</option>
                                <option value="6"  >6</option>
                                <option value="7"  >7</option>
                                <option value="8"  >8</option>
                                <option value="9"  >9</option>
                                <option value="10"  >10</option>
                                <option value="11"  >11</option>
                                <option value="12"  >12</option>
                                <option value="13"  >13</option>
                                <option value="14"  >14</option>
                                <option value="15"  >15</option>
                                <option value="16"  >16</option>
                                <option value="17"  >17</option>
                                <option value="18"  >18</option>
                                <option value="19"  >19</option>
                                <option value="20"  >20</option>
                                <option value="21"  >21</option>
                                <option value="22"  >22</option>
                                <option value="23"  >23</option>
                                <option value="24"  >24</option>
                                <option value="25"  >25</option>
                                <option value="26"  >26</option>
                                <option value="27"  >27</option>
                                <option value="28"  >28</option>
                                <option value="29"  >29</option>
                                <option value="30"  >30</option>
                                <option value="31"  >31</option>
                                <option value="32"  >32</option>
                                <option value="33"  >33</option>
                                <option value="34"  >34</option>
                                <option value="35"  >35</option>
                                <option value="36"  >36</option>
                                <option value="37"  >37</option>
                                <option value="38"  >38</option>
                                <option value="39"  >39</option>
                                <option value="40"  >40</option>
                                <option value="41"  >41</option>
                                <option value="42"  >42</option>
                                <option value="43"  >43</option>
                                <option value="44"  >44</option>
                                <option value="45"  >45</option>
                                <option value="46"  >46</option>
                                <option value="47"  >47</option>
                                <option value="48"  >48</option>
                                <option value="49"  >49</option>
                                <option value="50"  >50</option>
                                <option value="51"  >51</option>
                                <option value="52"  >52</option>
                                <option value="53"  >53</option>
                                <option value="54"  >54</option>
                                <option value="55"  >55</option>
                                <option value="56"  >56</option>
                                <option value="57"  >57</option>
                                <option value="58"  >58</option>
                                <option value="59"  >59</option>
                                <option value="60"  >60</option>
                              </select>
            </td>
            <td align="center"><input type="file" style="border:none; width:96% !important; padding-left: 0px;" name="researchExperienceSupportingDocuments"  id="researchExperienceSupportingDocuments" > </td>
          </tr>
          <tr>
          	<td colspan="7"><span style="font-weight:bold;">CATEGORY II :  CO-CURRICULAR, EXTENSION AND PROFESSIONAL DEVELOPMENT RELATED ACTIVITIES</span><br /><span style="font-weight:bold;">Co-curricular, extension and professional development related activities</span><br /><br />
            <span> 1). Student related co-curricular, extension and field based activities and counseling.</span>
            </td>
          </tr>
          <tr>
          	<td style="width:20px;">Description</td>
          	<td colspan="6" align="left"><textarea style="width: 98%;border: 1px solid #ccc;  resize: none;border-radius: 5px;padding: 8px 5px;font-size: 14px; height:80px;" name="studentRelatedCoCurricular" id="studentRelatedCoCurricular"></textarea></td>
          </tr>
          <tr>
          	<td colspan="7"><span> 2). Contribution to corporate sector-management of the department and institution through participation in academic and administrative committees and responsibilities.</span>
            </td>
          </tr>
          <tr>
          	<td style="width:20px;">Description</td>
          	<td colspan="6" align="left"><textarea style="width: 98%;border: 1px solid #ccc;  resize: none;border-radius: 5px;padding: 8px 5px;font-size: 14px; height:80px;" name="contributionToCorporate" id="contributionToCorporate"></textarea></td>
          </tr>
          <tr>
          	<td colspan="7"><span style="font-weight:bold;">CATEGORY III :  RESEARCH AND ACADEMIC CONTRIBUTION</span><br><br /><span style="font-weight:bold;">RESEARCH PUBLICATIONS</span><br>
            </td>
            </tr>
            <tr><td colspan="7"><span> 1). Books – Self authored/co-authored/edited <!--(Please attach separate sheet, if necessary)--></span></td></tr>
            <tr>
            	<td colspan="7">
                	<table class="form-tb1"   cellpadding="10" cellspacing="0"  width="100%;" id="form_row_table">
                		<tbody>
                    	<tr>
                            <td align="center"><b>Title of the Book(s)</b></td>
                            <td align="center"><b>Whether Sole Author or Co-author</b></td>
                            <td align="center"><b>Name of Publisher (with city/country)</b></td>
                            <td align="center"><b>Moth & year of publication</b></td>
                            <td align="center"><b>ISBN/ISSN No.</b></td>
                            <td align="center"><b>Supporting Documents</b></td>
                  		</tr>
                          <tr id="trs_row1">
                            <td align="center"><input type="text" name="booksTitleOfTheBook1" id="booksTitleOfTheBook1" class="form-tb-input2" placeholder="Title of the Book" value=""></td>
                            <td align="center">
                                <select name="booksSoleAuthorCoauthor1" id="booksSoleAuthorCoauthor1" class="form-tb-input2">
                                    <option value="">Select</option>
                                    <option value="Sole Author">Sole Author</option>
                                    <option value="Co-author">Co-author</option>
                                </select>
                            </td>
                            <td align="center" style="width: 190px;">
                                <textarea name="booksNamePublisherCountry1" id="booksNamePublisherCountry1" style="width:95%;border: 1px solid #ccc; resize: none;border-radius: 5px;padding: 8px 5px;font-size: 14px; height:80px;"></textarea>
                            </td>
                        <td style="width: 190px;" align="center">
                            <select name="booksMonthYearPublication1" id="booksMonthYearPublication1" class="form-tb-input1" placeholder="Month" style="width: 90px;float: left; margin-right: 5px;">
                            <option value=""> Month</option> 
                                 <option value="Jan" >Jan</option>
                             <option value="Feb" >Feb</option>
                             <option value="March" >March</option>
                             <option value="April" >April</option>
                             <option value="May" >May</option>
                             <option value="June" >June</option>
                             <option value="July" >July</option>
                             <option value="Aug" >Aug</option>
                             <option value="Sept" >Sept</option>
                             <option value="Oct" >Oct</option>
                             <option value="Nov" >Nov</option>
                             <option value="Dec" >Dec</option>
                                                    </select>
                        <select dir="1" name="booksMonthYearPublication1" id="booksMonthYearPublication1"  class="form-tb-input1" style="width: 90px;float: left; margin-right: 5px;">
                            <option value="">Year</option>
                                 
                                <option value="1950">1950</option>
                             
                                <option value="1951">1951</option>
                             
                                <option value="1952">1952</option>
                             
                                <option value="1953">1953</option>
                             
                                <option value="1954">1954</option>
                             
                                <option value="1955">1955</option>
                             
                                <option value="1956">1956</option>
                             
                                <option value="1957">1957</option>
                             
                                <option value="1958">1958</option>
                             
                                <option value="1959">1959</option>
                             
                                <option value="1960">1960</option>
                             
                                <option value="1961">1961</option>
                             
                                <option value="1962">1962</option>
                             
                                <option value="1963">1963</option>
                             
                                <option value="1964">1964</option>
                             
                                <option value="1965">1965</option>
                             
                                <option value="1966">1966</option>
                             
                                <option value="1967">1967</option>
                             
                                <option value="1968">1968</option>
                             
                                <option value="1969">1969</option>
                             
                                <option value="1970">1970</option>
                             
                                <option value="1971">1971</option>
                             
                                <option value="1972">1972</option>
                             
                                <option value="1973">1973</option>
                             
                                <option value="1974">1974</option>
                             
                                <option value="1975">1975</option>
                             
                                <option value="1976">1976</option>
                             
                                <option value="1977">1977</option>
                             
                                <option value="1978">1978</option>
                             
                                <option value="1979">1979</option>
                             
                                <option value="1980">1980</option>
                             
                                <option value="1981">1981</option>
                             
                                <option value="1982">1982</option>
                             
                                <option value="1983">1983</option>
                             
                                <option value="1984">1984</option>
                             
                                <option value="1985">1985</option>
                             
                                <option value="1986">1986</option>
                             
                                <option value="1987">1987</option>
                             
                                <option value="1988">1988</option>
                             
                                <option value="1989">1989</option>
                             
                                <option value="1990">1990</option>
                             
                                <option value="1991">1991</option>
                             
                                <option value="1992">1992</option>
                             
                                <option value="1993">1993</option>
                             
                                <option value="1994">1994</option>
                             
                                <option value="1995">1995</option>
                             
                                <option value="1996">1996</option>
                             
                                <option value="1997">1997</option>
                             
                                <option value="1998">1998</option>
                             
                                <option value="1999">1999</option>
                             
                                <option value="2000">2000</option>
                             
                                <option value="2001">2001</option>
                             
                                <option value="2002">2002</option>
                             
                                <option value="2003">2003</option>
                             
                                <option value="2004">2004</option>
                             
                                <option value="2005">2005</option>
                             
                                <option value="2006">2006</option>
                             
                                <option value="2007">2007</option>
                             
                                <option value="2008">2008</option>
                             
                                <option value="2009">2009</option>
                             
                                <option value="2010">2010</option>
                             
                                <option value="2011">2011</option>
                             
                                <option value="2012">2012</option>
                             
                                <option value="2013">2013</option>
                             
                                <option value="2014">2014</option>
                             
                                <option value="2015">2015</option>
                             
                                <option value="2016">2016</option>
                             
                                <option value="2017">2017</option>
                             
                                <option value="2018">2018</option>
                             
                                <option value="2019">2019</option>
                             
                                <option value="2020">2020</option>
                             
                                <option value="2021">2021</option>
                             
                                <option value="2022">2022</option>
                             
                                <option value="2023">2023</option>
                             
                                <option value="2024">2024</option>
                             
                        </select>
                        </td>
                        <td align="center"><input type="text" name="booksIsbnIssnNo1" id="booksIsbnIssnNo1" class="form-tb-input2" placeholder="ISBN/ISSN No" value=""></td>
                        <td align="center"><input type="file" style="border:none; width:100% !important; padding-left: 0px;" name="booksSupportingDocuments1" id="booksSupportingDocuments1" > &nbsp;<span style="cursor:pointer; background-color: #148534;color: #fff; padding: 5px 6px;border-radius: 3px;position: relative;top: -27px;left: 155px;" onclick="addFamilysMembersRows();" class="smallgreenbtn"><i class="fa fa-plus-circle"></i> Add More</span></td>
                    </tr>
                    <input type="hidden" name="tr_row_count" id="tr_row_count" value="2">
                
                      </tr>
                  </tbody>
          <script>
		  function addFamilysMembersRows() {
    var rowCount = document.getElementById("tr_row_count").value;
    var newRow = `
        <tr id="trs_row${rowCount}">
            <td align="center"><input type="text" name="booksTitleOfTheBook${rowCount}" id="booksTitleOfTheBook${rowCount}" class="form-tb-input2" placeholder="Title of the Book" value=""></td>
            <td align="center">
                <select name="booksSoleAuthorCoauthor${rowCount}" id="booksSoleAuthorCoauthor${rowCount}" class="form-tb-input2">
                    <option value="">Select</option>
                    <option value="Sole Author">Sole Author</option>
                    <option value="Co-author">Co-author</option>
                </select>
            </td>
            <td align="center" style="width: 190px;">
                <textarea name="booksNamePublisherCountry${rowCount}" id="booksNamePublisherCountry${rowCount}" style="width:95%;border: 1px solid #ccc; resize: none;border-radius: 5px;padding: 8px 5px;font-size: 14px; height:80px;"></textarea>
            </td>
            <td style="width: 190px;" align="center">
                <select name="booksMonthYearPublication${rowCount}" id="booksMonthYearPublication${rowCount}" class="form-tb-input1" placeholder="Month" style="width: 90px;float: left; margin-right: 5px;">
                    <option value=""> Month</option>
                    <option value="Jan">Jan</option>
                    <option value="Feb">Feb</option>
                    <option value="March">March</option>
                    <option value="April">April</option>
                    <option value="May">May</option>
                    <option value="June">June</option>
                    <option value="July">July</option>
                    <option value="Aug">Aug</option>
                    <option value="Sept">Sept</option>
                    <option value="Oct">Oct</option>
                    <option value="Nov">Nov</option>
                    <option value="Dec">Dec</option>
                </select>
                <select name="booksMonthYearPublication${rowCount}" id="booksMonthYearPublication${rowCount}" class="form-tb-input1" style="width: 90px;float: left; margin-right: 5px;">
                  <option value="1950">1950</option>
                             
                                <option value="1951">1951</option>
                             
                                <option value="1952">1952</option>
                             
                                <option value="1953">1953</option>
                             
                                <option value="1954">1954</option>
                             
                                <option value="1955">1955</option>
                             
                                <option value="1956">1956</option>
                             
                                <option value="1957">1957</option>
                             
                                <option value="1958">1958</option>
                             
                                <option value="1959">1959</option>
                             
                                <option value="1960">1960</option>
                             
                                <option value="1961">1961</option>
                             
                                <option value="1962">1962</option>
                             
                                <option value="1963">1963</option>
                             
                                <option value="1964">1964</option>
                             
                                <option value="1965">1965</option>
                             
                                <option value="1966">1966</option>
                             
                                <option value="1967">1967</option>
                             
                                <option value="1968">1968</option>
                             
                                <option value="1969">1969</option>
                             
                                <option value="1970">1970</option>
                             
                                <option value="1971">1971</option>
                             
                                <option value="1972">1972</option>
                             
                                <option value="1973">1973</option>
                             
                                <option value="1974">1974</option>
                             
                                <option value="1975">1975</option>
                             
                                <option value="1976">1976</option>
                             
                                <option value="1977">1977</option>
                             
                                <option value="1978">1978</option>
                             
                                <option value="1979">1979</option>
                             
                                <option value="1980">1980</option>
                             
                                <option value="1981">1981</option>
                             
                                <option value="1982">1982</option>
                             
                                <option value="1983">1983</option>
                             
                                <option value="1984">1984</option>
                             
                                <option value="1985">1985</option>
                             
                                <option value="1986">1986</option>
                             
                                <option value="1987">1987</option>
                             
                                <option value="1988">1988</option>
                             
                                <option value="1989">1989</option>
                             
                                <option value="1990">1990</option>
                             
                                <option value="1991">1991</option>
                             
                                <option value="1992">1992</option>
                             
                                <option value="1993">1993</option>
                             
                                <option value="1994">1994</option>
                             
                                <option value="1995">1995</option>
                             
                                <option value="1996">1996</option>
                             
                                <option value="1997">1997</option>
                             
                                <option value="1998">1998</option>
                             
                                <option value="1999">1999</option>
                             
                                <option value="2000">2000</option>
                             
                                <option value="2001">2001</option>
                             
                                <option value="2002">2002</option>
                             
                                <option value="2003">2003</option>
                             
                                <option value="2004">2004</option>
                             
                                <option value="2005">2005</option>
                             
                                <option value="2006">2006</option>
                             
                                <option value="2007">2007</option>
                             
                                <option value="2008">2008</option>
                             
                                <option value="2009">2009</option>
                             
                                <option value="2010">2010</option>
                             
                                <option value="2011">2011</option>
                             
                                <option value="2012">2012</option>
                             
                                <option value="2013">2013</option>
                             
                                <option value="2014">2014</option>
                             
                                <option value="2015">2015</option>
                             
                                <option value="2016">2016</option>
                             
                                <option value="2017">2017</option>
                             
                                <option value="2018">2018</option>
                             
                                <option value="2019">2019</option>
                             
                                <option value="2020">2020</option>
                             
                                <option value="2021">2021</option>
                             
                                <option value="2022">2022</option>
                             
                                <option value="2023">2023</option>
                             
                                <option value="2024">2024</option>
                </select>
            </td>
            <td align="center"><input type="text" name="booksIsbnIssnNo${rowCount}" id="booksIsbnIssnNo${rowCount}" class="form-tb-input2" placeholder="ISBN/ISSN No" value=""></td>
            <td align="center"><input type="file" style="border:none; width:100% !important; padding-left: 0px;" name="booksSupportingDocuments${rowCount}" id="booksSupportingDocuments${rowCount}"> &nbsp;<a href="javascript:void(0);" style="background-color: #cd0a0a;text-decoration:none;color: #fff; padding: 5px 6px;border-radius: 3px;position: relative;top: -27px;left: 155px;" onclick="removesFamilyMembersRows(${rowCount});" class="smallgreybtn"><i class="fa fa-trash"></i> Remove</a></td>
        </tr>`;
    $('#form_row_table').append(newRow);
    document.getElementById("tr_row_count").value = Number(rowCount) + 1;
}

function removesFamilyMembersRows(removeNum) {
    $('#trs_row' + removeNum).remove();
}

</script>
      </table>
                </td>
            </tr>
            
            <tr><td colspan="7"><span> 2). Chapters contributed in edited books <!--(Please attach separate sheet, if necessary)--></span></td></tr>
            <tr>
            	<td colspan="7">
                <table class="form-tb1"  cellpadding="10" cellspacing="0" width="100%;" id="form_row_chapter_table">
                    <tbody>
                        <tr>
                            <td align="center"><b>Title of Chapter(s)</b></td>
                            <td align="center"><b>Title of the Book(s)</b></td>
                            <td align="center"><b>Whether Sole Author or Co-author</b></td>
                            <td align="center"><b>Name of Publisher (with city/country)</b></td>
                            <td align="center"><b>Month & year of publication</b></td>
                            <td align="center"><b>ISBN/ISSN No.</b></td>
                            <td align="center"><b>Supporting Documents</b></td>
                        </tr>
                        <tr id="tr_chapter_row1">
                            <td align="center"><input type="text" name="contributedTitleOfChapter1" id="contributedTitleOfChapter1" dir="1" class="form-tb-input2" placeholder="Title of Chapter(s)" value="" ></td>
                            <td align="center"><input type="text" name="ContributedTitleOfBook1" id="ContributedTitleOfBook1" dir="1" class="form-tb-input2" placeholder="Title of the Book" value="" ></td>
                            <td align="center">
                                <select name="ContributedauthorType1" id="ContributedauthorType1" class="form-tb-input2" dir="1">
                                    <option value="">Select</option>
                                    <option value="Sole Author">Sole Author</option>
                                    <option value="Co-author">Co-author</option>
                                </select>
                            </td>
                            <td style="width: 190px;" align="center">
                                <textarea dir="1" name="ContributedPublisher1" id="ContributedPublisher1" style="width:95%;border: 1px solid #ccc; resize: none;border-radius: 5px;padding: 8px 5px;font-size: 14px; height:80px;"></textarea>
                            </td>
                            <td style="width: 190px;" align="center">
                                <select dir="1" name="contrubutedMonth1" id="contrubutedMonth1" class="form-tb-input1" placeholder="Month" style="width: 90px;float: left; margin-right: 5px;">
                                    <option value=""> Month</option> 
                                    <option value="Jan">Jan</option>
                                    <option value="Feb">Feb</option>
                                    <option value="March">March</option>
                                    <option value="April">April</option>
                                    <option value="May">May</option>
                                    <option value="June">June</option>
                                    <option value="July">July</option>
                                    <option value="Aug">Aug</option>
                                    <option value="Sept">Sept</option>
                                    <option value="Oct">Oct</option>
                                    <option value="Nov">Nov</option>
                                    <option value="Dec">Dec</option>
                                </select>
                                <select dir="1" name="contrubutedYear1" id="contrubutedYear1" class="form-tb-input1" style="width: 90px;float: left; margin-right: 5px;">
                                    <option value="">Year</option>
                                    <option value="1951">1951</option>
                             
                                    <option value="1952">1952</option>
                                 
                                    <option value="1953">1953</option>
                                 
                                    <option value="1954">1954</option>
                                 
                                    <option value="1955">1955</option>
                                 
                                    <option value="1956">1956</option>
                                 
                                    <option value="1957">1957</option>
                                 
                                    <option value="1958">1958</option>
                                 
                                    <option value="1959">1959</option>
                                 
                                    <option value="1960">1960</option>
                                 
                                    <option value="1961">1961</option>
                                 
                                    <option value="1962">1962</option>
                                 
                                    <option value="1963">1963</option>
                                 
                                    <option value="1964">1964</option>
                                 
                                    <option value="1965">1965</option>
                                 
                                    <option value="1966">1966</option>
                                 
                                    <option value="1967">1967</option>
                                 
                                    <option value="1968">1968</option>
                                 
                                    <option value="1969">1969</option>
                                 
                                    <option value="1970">1970</option>
                                 
                                    <option value="1971">1971</option>
                                 
                                    <option value="1972">1972</option>
                                 
                                    <option value="1973">1973</option>
                                 
                                    <option value="1974">1974</option>
                                 
                                    <option value="1975">1975</option>
                                 
                                    <option value="1976">1976</option>
                                 
                                    <option value="1977">1977</option>
                                 
                                    <option value="1978">1978</option>
                                 
                                    <option value="1979">1979</option>
                                 
                                    <option value="1980">1980</option>
                                 
                                    <option value="1981">1981</option>
                                 
                                    <option value="1982">1982</option>
                                 
                                    <option value="1983">1983</option>
                                 
                                    <option value="1984">1984</option>
                                 
                                    <option value="1985">1985</option>
                                 
                                    <option value="1986">1986</option>
                                 
                                    <option value="1987">1987</option>
                                 
                                    <option value="1988">1988</option>
                                 
                                    <option value="1989">1989</option>
                                 
                                    <option value="1990">1990</option>
                                 
                                    <option value="1991">1991</option>
                                 
                                    <option value="1992">1992</option>
                                 
                                    <option value="1993">1993</option>
                                 
                                    <option value="1994">1994</option>
                                 
                                    <option value="1995">1995</option>
                                 
                                    <option value="1996">1996</option>
                                 
                                    <option value="1997">1997</option>
                                 
                                    <option value="1998">1998</option>
                                 
                                    <option value="1999">1999</option>
                                 
                                    <option value="2000">2000</option>
                                 
                                    <option value="2001">2001</option>
                                 
                                    <option value="2002">2002</option>
                                 
                                    <option value="2003">2003</option>
                                 
                                    <option value="2004">2004</option>
                                 
                                    <option value="2005">2005</option>
                                 
                                    <option value="2006">2006</option>
                                 
                                    <option value="2007">2007</option>
                                 
                                    <option value="2008">2008</option>
                                 
                                    <option value="2009">2009</option>
                                 
                                    <option value="2010">2010</option>
                                 
                                    <option value="2011">2011</option>
                                 
                                    <option value="2012">2012</option>
                                 
                                    <option value="2013">2013</option>
                                 
                                    <option value="2014">2014</option>
                                 
                                    <option value="2015">2015</option>
                                 
                                    <option value="2016">2016</option>
                                 
                                    <option value="2017">2017</option>
                                 
                                    <option value="2018">2018</option>
                                 
                                    <option value="2019">2019</option>
                                 
                                    <option value="2020">2020</option>
                                 
                                    <option value="2021">2021</option>
                                 
                                    <option value="2022">2022</option>
                                 
                                    <option value="2023">2023</option>
                                 
                                    <option value="2024">2024</option>
                                </select>
                            </td>
                            <td align="center"><input type="text" name="ContrubutedIsbnIssnNo1" id="ContrubutedIsbnIssnNo1" class="form-tb-input2" dir="1" placeholder="ISBN/ISSN No" value=""></td>
                            <td align="center"><input type="file" style="border:none; width:100% !important; padding-left: 0px;" name="contributedSupportingDocuments1" id="contributedSupportingDocuments1" dir="1"> &nbsp;<span style="cursor:pointer; background-color: #148534;color: #fff; padding: 5px 6px;border-radius: 3px;position: relative;top: -27px;left: 155px;" onclick="addFamilyChapMembersRows();" class="smallgreenbtn"><i class="fa fa-plus-circle"></i>AddMore</span></td>
                            
                            <input type="hidden" name="tr_chapter_row_count" id="tr_chapter_row_count" value="2" >
                        </tr>
                    </tbody>
                    <script>
		  	function addFamilyChapMembersRows() {
    var rowCount = document.getElementById("tr_chapter_row_count").value;
    var newRow = `
        <tr id="tr_chapter_row${rowCount}">
            <td align="center"><input type="text" name="contributedTitleOfChapter${rowCount}" id="contributedTitleOfChapter${rowCount}" class="form-tb-input2" placeholder="Title of Chapter(s)" value="" ></td>
            <td align="center"><input type="text" name="ContributedTitleOfBook${rowCount}" id="ContributedTitleOfBook${rowCount}" class="form-tb-input2" placeholder="Title of the Book" value="" ></td>
            <td align="center">
                <select name="ContributedauthorType${rowCount}" id="ContributedauthorType${rowCount}" class="form-tb-input2">
                    <option value="">Select</option>
                    <option value="Sole Author">Sole Author</option>
                    <option value="Co-author">Co-author</option>
                </select>
            </td>
            <td style="width: 190px;" align="center">
                <textarea name="ContributedPublisher${rowCount}" id="ContributedPublisher${rowCount}" style="width:95%;border: 1px solid #ccc; resize: none;border-radius: 5px;padding: 8px 5px;font-size: 14px; height:80px;"></textarea>
            </td>
            <td style="width: 190px;" align="center">
                <select name="contrubutedMonth${rowCount}" id="contrubutedMonth${rowCount}" class="form-tb-input1" placeholder="Month" style="width: 90px;float: left; margin-right: 5px;">
                    <option value=""> Month</option>
                    <option value="Jan">Jan</option>
                    <option value="Feb">Feb</option>
                    <option value="March">March</option>
                    <option value="April">April</option>
                    <option value="May">May</option>
                    <option value="June">June</option>
                    <option value="July">July</option>
                    <option value="Aug">Aug</option>
                    <option value="Sept">Sept</option>
                    <option value="Oct">Oct</option>
                    <option value="Nov">Nov</option>
                    <option value="Dec">Dec</option>
                </select>
                <select name="contrubutedYear${rowCount}" id="contrubutedYear${rowCount}" class="form-tb-input1" style="width: 90px;float: left; margin-right: 5px;">
                    <option value="">Year</option>
                   <option value="1951">1951</option>
                             
                                <option value="1952">1952</option>
                             
                                <option value="1953">1953</option>
                             
                                <option value="1954">1954</option>
                             
                                <option value="1955">1955</option>
                             
                                <option value="1956">1956</option>
                             
                                <option value="1957">1957</option>
                             
                                <option value="1958">1958</option>
                             
                                <option value="1959">1959</option>
                             
                                <option value="1960">1960</option>
                             
                                <option value="1961">1961</option>
                             
                                <option value="1962">1962</option>
                             
                                <option value="1963">1963</option>
                             
                                <option value="1964">1964</option>
                             
                                <option value="1965">1965</option>
                             
                                <option value="1966">1966</option>
                             
                                <option value="1967">1967</option>
                             
                                <option value="1968">1968</option>
                             
                                <option value="1969">1969</option>
                             
                                <option value="1970">1970</option>
                             
                                <option value="1971">1971</option>
                             
                                <option value="1972">1972</option>
                             
                                <option value="1973">1973</option>
                             
                                <option value="1974">1974</option>
                             
                                <option value="1975">1975</option>
                             
                                <option value="1976">1976</option>
                             
                                <option value="1977">1977</option>
                             
                                <option value="1978">1978</option>
                             
                                <option value="1979">1979</option>
                             
                                <option value="1980">1980</option>
                             
                                <option value="1981">1981</option>
                             
                                <option value="1982">1982</option>
                             
                                <option value="1983">1983</option>
                             
                                <option value="1984">1984</option>
                             
                                <option value="1985">1985</option>
                             
                                <option value="1986">1986</option>
                             
                                <option value="1987">1987</option>
                             
                                <option value="1988">1988</option>
                             
                                <option value="1989">1989</option>
                             
                                <option value="1990">1990</option>
                             
                                <option value="1991">1991</option>
                             
                                <option value="1992">1992</option>
                             
                                <option value="1993">1993</option>
                             
                                <option value="1994">1994</option>
                             
                                <option value="1995">1995</option>
                             
                                <option value="1996">1996</option>
                             
                                <option value="1997">1997</option>
                             
                                <option value="1998">1998</option>
                             
                                <option value="1999">1999</option>
                             
                                <option value="2000">2000</option>
                             
                                <option value="2001">2001</option>
                             
                                <option value="2002">2002</option>
                             
                                <option value="2003">2003</option>
                             
                                <option value="2004">2004</option>
                             
                                <option value="2005">2005</option>
                             
                                <option value="2006">2006</option>
                             
                                <option value="2007">2007</option>
                             
                                <option value="2008">2008</option>
                             
                                <option value="2009">2009</option>
                             
                                <option value="2010">2010</option>
                             
                                <option value="2011">2011</option>
                             
                                <option value="2012">2012</option>
                             
                                <option value="2013">2013</option>
                             
                                <option value="2014">2014</option>
                             
                                <option value="2015">2015</option>
                             
                                <option value="2016">2016</option>
                             
                                <option value="2017">2017</option>
                             
                                <option value="2018">2018</option>
                             
                                <option value="2019">2019</option>
                             
                                <option value="2020">2020</option>
                             
                                <option value="2021">2021</option>
                             
                                <option value="2022">2022</option>
                             
                                <option value="2023">2023</option>
                             
                                <option value="2024">2024</option>
                </select>
            </td>
            <td align="center"><input type="text" name="ContrubutedIsbnIssnNo${rowCount}" id="ContrubutedIsbnIssnNo${rowCount}" class="form-tb-input2" placeholder="ISBN/ISSN No" value=""></td>
            <td align="center"><input type="file" style="border:none; width:100% !important; padding-left: 0px;" name="contributedSupportingDocuments${rowCount}" id="contributedSupportingDocuments${rowCount}"> &nbsp;<a href="javascript:void(0);" style="background-color: #cd0a0a;text-decoration:none;color: #fff; padding: 5px 6px;border-radius: 3px;position: relative;top: -27px;left: 155px;" onclick="removechapFamilyMembersRows(${rowCount});" class="smallgreybtn"><i class="fa fa-trash"></i> Remove</a></td>
        </tr>`;
    $('#form_row_chapter_table').append(newRow);
    document.getElementById("tr_chapter_row_count").value = Number(rowCount) + 1;
}

function removechapFamilyMembersRows(removeNum) {
    $('#tr_chapter_row' + removeNum).remove();
}

</script>
      </table>
                </td>
            </tr>
            
            <tr><td colspan="7"><span> 3). Research/Articles/Papers published in Journals/Periodicals/Conference proceedings/Newspapers <!--(Please attach separate sheet, if necessary)--></span></td></tr>
            <tr>
              <td colspan="7">
                <table class="form-tb1"  cellpadding="10" cellspacing="0" width="100%;" id="form_row_table_articles">
                    <tbody>
                        <tr>
                            <td align="center"><b>Title of research article/paper(s)</b></td>
                            <td align="center"><b>Name of journal</b></td>
                            <td align="center"><b>Whether Sole Author or Co-author</b></td>
                            <td align="center"><b> Month & year of publication, volume, no. & page nos.</b></td>
                            <td align="center"><b>Whether Referred/non-referred</b></td>
                            <td align="center"><b>ISBN/ISSN No.</b></td>
                            <td align="center"><b>Level (Int./Nat./State/Local)</b></td>
                            <td align="center"><b>NAAS Rating/Impact Factor </b></td>
                            <td align="center"><b>Supporting Documents</b></td>
                        </tr>
                        <tr id="tr_article_row1">
                            <td align="center">
                                <input type="text" name="titleOfArticle1" id="titleOfArticle1" dir="1" class="form-tb-input2" placeholder="Title of research article/paper(s)" value="">
                            </td>
                            <td align="center">
                                <input type="text" name="nameOfJournal1" id="nameOfJournal1" dir="1" class="form-tb-input2" placeholder="Name of journal" value="">
                            </td>
                            <td align="center">
                                <select name="authorType1" id="authorType1" class="form-tb-input2" dir="1">
                                    <option value="">Select</option>
                                    <option value="Sole Author">Sole Author</option>
                                    <option value="Co-author">Co-author</option>
                                </select>
                            </td>
                            <td style="width: 190px;" align="center">
                                <textarea dir="1" name="publicationDetails1" id="publicationDetails1" style="width:95%;border: 1px solid #ccc; resize: none;border-radius: 5px;padding: 8px 5px;font-size: 14px; height:80px;"></textarea>
                            </td>
                            <td style="width: 190px;" align="center">
                                <select name="referredStatus1" id="referredStatus1" class="form-tb-input2" dir="1">
                                    <option value="">Select</option>
                                    <option value="Referred">Referred</option>
                                    <option value="Non-Referred">Non-Referred</option>
                                </select>
                            </td>
                            <td align="center">
                                <input type="text" name="researchIsbnIssnNo1" id="researchIsbnIssnNo1" class="form-tb-input2" dir="1" placeholder="ISBN/ISSN No" value="">
                            </td>
                            <td align="center">
                                <input type="text" name="level1" id="level1" class="form-tb-input2" dir="1" placeholder="Level (Int./Nat./State/Local)" value="">
                            </td>
                            <td align="center">
                                <input type="text" name="impactFactor1" id="impactFactor1" class="form-tb-input2" dir="1" placeholder="NAAS Rating/Impact Factor" value="">
                            </td>
                            <td align="center">
                                <input type="file" style="border:none; width:100% !important; padding-left: 0px;" name="researchSupportingDocuments1" id="researchSupportingDocuments1" dir="1">
                                &nbsp;<span style="cursor:pointer; background-color: #148534;color: #fff; padding: 5px 6px;border-radius: 3px;position: relative;top: -27px;left: 110px;" onclick="addFamilyArticlesMembersRows();" class="smallgreenbtn"><i class="fa fa-plus-circle"></i>AddMore</span>
                            </td>
                            
                            <input type="hidden" name="tr_row_article_count" id="tr_row_article_count" value="2">
                        </tr>
                    </tbody>
                    <script>
                        function addFamilyArticlesMembersRows() {
                            var rowCount = document.getElementById("tr_row_article_count").value;
                            var recRow = `
                                <tr id="tr_article_row${rowCount}">
                                    <td align="center"><input type="text" name="titleOfArticle${rowCount}" id="titleOfArticle${rowCount}" dir="${rowCount}" class="form-tb-input2" placeholder="Title of research article/paper(s)" value=""></td>
                                    <td align="center"><input type="text" name="nameOfJournal${rowCount}" id="nameOfJournal${rowCount}" dir="${rowCount}" class="form-tb-input2" placeholder="Name of journal" value=""></td>
                                    <td align="center"><select name="authorType${rowCount}" id="authorType${rowCount}" dir="${rowCount}" class="form-tb-input2"><option value="">Select</option><option value="Sole Author">Sole Author</option><option value="Co-author">Co-author</option></select></td>
                                    <td style="width: 190px;" align="center"><textarea name="publicationDetails${rowCount}" id="publicationDetails${rowCount}" dir="${rowCount}" style="width:95%;border: 1px solid #ccc; resize: none;border-radius: 5px;padding: 8px 5px;font-size: 14px; height:80px;"></textarea></td>
                                    <td style="width: 190px;" align="center"><select name="referredStatus${rowCount}" id="referredStatus${rowCount}" dir="${rowCount}" class="form-tb-input2"><option value="">Select</option><option value="Referred">Referred</option><option value="Non-Referred">Non-Referred</option></select></td>
                                    <td align="center"><input type="text" name="researchIsbnIssnNo${rowCount}" id="researchIsbnIssnNo${rowCount}" dir="${rowCount}" class="form-tb-input2" placeholder="ISBN/ISSN No" value=""></td>
                                    <td align="center"><input type="text" name="level${rowCount}" id="level${rowCount}" dir="${rowCount}" class="form-tb-input2" placeholder="Level (Int./Nat./State/Local)" value=""></td>
                                    <td align="center"><input type="text" name="impactFactor${rowCount}" id="impactFactor${rowCount}" dir="${rowCount}" class="form-tb-input2" placeholder="NAAS Rating/Impact Factor" value=""></td>
                                    <td align="center"><input type="file" style="border:none; width:100% !important; padding-left: 0px;" name="researchSupportingDocuments${rowCount}" id="researchSupportingDocuments${rowCount}" dir="${rowCount}"> &nbsp;<span style="cursor:pointer; background-color: #e8233c;color: #fff; padding: 5px 6px;border-radius: 3px;position: relative;top: -27px;left: 110px;" onclick="removeFamilyArticlesRow(${rowCount});" class="smallredbtn"><i class="fa fa-trash"></i>Remove</span></td>
                                </tr>`;
                            $('#form_row_table_articles > tbody').append(recRow);
                            rowCount++;
                            document.getElementById("tr_row_article_count").value = rowCount;
                        }
                        
                        function removeFamilyArticlesRow(removeNum) {
                            $('#tr_article_row' + removeNum).remove();
                        }
                        </script>
                        
                </table>
            </td>
            
      </table>
                </td>
            </tr>
          </tbody>
      </table>
      <table class="form-tb1" id="form-projecttaken" cellpadding="10" cellspacing="0" width="100%;">
        <tbody>
        	<tr>
            	<td colspan="7"><h3 style="text-align:left; margin-bottom:5px;">Research Projects Undertaken :</h3><span style="font-weight:bold;">(other than that for a research degree)</span>
</span></td>
            </tr>
            
            <tr>
              <td colspan="7">
                <table class="form-tb1" cellpadding="10" cellspacing="0" width="100%;" id="form_row_table_project">
                    <tbody>
                        <tr>
                            <td align="center"><b>Title/ Subject of Research Project(s)</b></td>
                            <td align="center"><b>Project Type</b></td>
                            <td align="center"><b>Date of commencement</b></td>
                            <td align="center"><b>Date of Completion</b></td>
                            <td align="center"><b>Total Grants/Funding received (Rs.)</b></td>
                            <td align="center"><b>Name of Sponsoring/Funding Agency</b></td>
                            <td align="center"><b>Whether Outcome/Outputs sent to Sponsoring Govt. Agency</b></td>
                            <td align="center"><b>Whether final report published as monograph book</b></td>
                            <td align="center"><b>Supporting Documents</b></td>
                        </tr>
                        <tr id="tr_project_row1">
                            <td align="center"><input type="text" name="researchTitleSubject1" id="researchTitleSubject1" dir="1" class="form-tb-input2" placeholder="Title/ Subject of Research Project(s)" value=""></td>
                            <td align="center">
                                <select name="researchProjectType1" id="researchProjectType1" class="form-tb-input2" dir="1">
                                    <option value="">Select</option>
                                    <option value="Major">Major</option>
                                    <option value="Minor">Minor</option>
                                    <option value="Consultancy Projects">Consultancy Projects</option>
                                    <option value="Completed Research Projects">Completed Research Projects</option>
                                    <option value="Ongoing Research Projects">Ongoing Research Projects</option>
                                    <option value="Outcome or Output Project">Outcome or Output Project</option>
                                    <option value="Policy Document forwarded to Sponsoring">Policy Document forwarded to Sponsoring</option>
                                    <option value="Funding Government Agency">Funding Government Agency</option>
                                </select>
                            </td>
                            <td><input type="text" name="researchDateCommencement1" id="researchDateCommencement1" dir="1" class="date_pickere_byclass"></td>
                            <td><input type="text" name="researchDateCompletion1" id="researchDateCompletion1" dir="1" class="date_pickere_byclass"></td>
                            <td align="center"><input type="text" name="researchTotalGrantsReceivedRs1" id="researchTotalGrantsReceivedRs1" class="form-tb-input2" dir="1" placeholder="Total Grants/Funding received (Rs.)" value=""></td>
                            <td align="center"><input type="text" name="researchNameSponsoringAgency1" id="researchNameSponsoringAgency1" class="form-tb-input2" dir="1" placeholder="Name of Sponsoring/Funding Agency" value=""></td>
                            <td align="center">
                                <select name="researchOutcomeOutputsSentGovt1" id="researchOutcomeOutputsSentGovt1" class="form-tb-input2" dir="1">
                                    <option value="">Select</option>
                                    <option value="Outcome">Outcome</option>
                                    <option value="Outputs sent to Sponsoring Govt Agency">Outputs sent to Sponsoring Govt. Agency</option>
                                </select>
                            </td>
                            <td align="center"><input type="text" name="researchFinalReportPublished1" id="researchFinalReportPublished1" class="form-tb-input2" dir="1" placeholder="Whether final report published as monograph book" value=""></td>
                            <td align="center"><input type="file" style="border:none; width:100% !important; padding-left: 0px;" name="researchSupportingDocuments1" id="researchSupportingDocuments1" dir="1"> &nbsp;<span style="cursor:pointer; background-color: #148534;color: #fff; padding: 5px 6px;border-radius: 3px;position: relative;top: -27px;left: 110px;" onclick="addFamilyProjectMembersRows();" class="smallgreenbtn"><i class="fa fa-plus-circle"></i>AddMore</span></td>
                            <input type="hidden" name="tr_row_project_count" id="tr_row_project_count" value="2">
                        </tr>
                    </tbody>
                    <script>
	function addFamilyProjectMembersRows() {
    var rowCount = document.getElementById("tr_row_project_count").value;
    var recRow = `
        <tr id="tr_project_row${rowCount}">
            <td align="center"><input type="text" name="researchTitleSubject${rowCount}" id="researchTitleSubject${rowCount}" class="form-tb-input2" placeholder="Title/ Subject of Research Project(s)" value=""></td>
            <td align="center">
                <select name="researchProjectType${rowCount}" id="researchProjectType${rowCount}" class="form-tb-input2">
                    <option value="">Select</option>
                    <option value="Major">Major</option>
                    <option value="Minor">Minor</option>
                    <option value="Consultancy Projects">Consultancy Projects</option>
                    <option value="Completed Research Projects">Completed Research Projects</option>
                    <option value="Ongoing Research Projects">Ongoing Research Projects</option>
                    <option value="Outcome or Output Project">Outcome or Output Project</option>
                    <option value="Policy Document forwarded to Sponsoring">Policy Document forwarded to Sponsoring</option>
                    <option value="Funding Government Agency">Funding Government Agency</option>
                </select>
            </td>
            <td><input type="text" name="researchDateCommencement${rowCount}" id="researchDateCommencement${rowCount}" class="date_pickere_byclass"></td>
            <td><input type="text" name="researchDateCompletion${rowCount}" id="researchDateCompletion${rowCount}" class="date_pickere_byclass"></td>
            <td align="center"><input type="text" name="researchTotalGrantsReceivedRs${rowCount}" id="researchTotalGrantsReceivedRs${rowCount}" class="form-tb-input2" placeholder="Total Grants/Funding received (Rs.)" value=""></td>
            <td align="center"><input type="text" name="researchNameSponsoringAgency${rowCount}" id="researchNameSponsoringAgency${rowCount}" class="form-tb-input2" placeholder="Name of Sponsoring/Funding Agency" value=""></td>
            <td align="center">
                <select name="researchOutcomeOutputsSentGovt${rowCount}" id="researchOutcomeOutputsSentGovt${rowCount}" class="form-tb-input2">
                    <option value="">Select</option>
                    <option value="Outcome">Outcome</option>
                    <option value="Outputs sent to Sponsoring Govt Agency">Outputs sent to Sponsoring Govt. Agency</option>
                </select>
            </td>
            <td align="center"><input type="text" name="researchFinalReportPublished${rowCount}" id="researchFinalReportPublished${rowCount}" class="form-tb-input2" placeholder="Whether final report published as monograph book" value=""></td>
            <td align="center"><input type="file" style="border:none; width:100% !important; padding-left: 0px;" name="researchSupportingDocuments${rowCount}" id="researchSupportingDocuments${rowCount}"> &nbsp;<a href="javascript:void(0);" style="background-color: #cd0a0a;text-decoration:none;color: #fff; padding: 5px 6px;border-radius: 3px;position: relative;top: -27px;left: 110px;" onclick="removeProjectFamilyMembersRows(${rowCount});" class="smallgreybtn"><i class="fa fa-trash"></i> Remove</a></td>
        </tr>`;
    $('#form_row_table_project').append(recRow);
    document.getElementById("tr_row_project_count").value = Number(rowCount) + 1;
}

// Function to remove rows from the research project table
function removeProjectFamilyMembersRows(removeNum) {
    $('#tr_project_row' + removeNum).remove();
}
</script>
      </table>
                </td>
            </tr>
            
        </tbody>
      </table>
      
      <table class="form-tb1" id="form-paper" cellpadding="10" cellspacing="0" width="100%;">
        <tbody>
            <tr>
                <td colspan="7"><h3 style="text-align:left; margin-bottom:5px;">Papers presented in Regional/National and International Seminars/Conferences/Workshop/Symposium. Indicate whether the Conference Proceedings are published.</h3></td>
            </tr>
            <tr>
                <td colspan="7">
                    <table class="form-tb1" cellpadding="10" cellspacing="0" width="100%;" id="form_row_table_paper_presented">
                        <tbody id="paper_presented_table_body">
                            <tr>
                                <td align="center"><b>Title/Subject of paper presented</b></td>
                                <td align="center"><b>Subject of Conference/Seminar/Symposium/Workshop</b></td>
                                <td align="center"><b>Organizing Institution/and Name of City/Country</b></td>
                                <td align="center"><b>Duration From / To</b></td>
                                <td align="center"><b>Whether the proceedings published Yes/No</b></td>
                                <td align="center"><b>Supporting Documents</b></td>
                            </tr>
                            <tr id="tr_paper_presented_row1">
                                <td align="center"><input type="text" name="titleOfPaper1" id="titleOfPaper1" dir="1" class="form-tb-input2" placeholder="Title/Subject of paper presented" value=""></td>
                                <td align="center"><input type="text" name="subjectOfConference1" id="subjectOfConference1" dir="1" class="form-tb-input2" placeholder="Subject of Conference/Seminar/Symposium/Workshop" value=""></td>
                                <td><textarea name="organizingInstitutionCityCountry1" id="organizingInstitutionCityCountry1" dir="1" style="width:95%;border: 1px solid #ccc; resize: none;border-radius: 5px;padding: 8px 5px;font-size: 14px; height:80px;"></textarea></td>
                                <td align="center"><input type="text" name="durationFrom1" id="durationFrom1" class="form-tb-input2 date_pickere_byclass" dir="1" placeholder="From" value=""> &nbsp;<input type="text" name="durationTo1" id="durationTo1" class="form-tb-input2 date_pickere_byclass" dir="1" placeholder="To" value=""></td>
                                <td align="center">
                                    <select name="proceedingsPublished1" id="proceedingsPublished1" class="form-tb-input2" dir="1">
                                        <option value="">Select</option>
                                        <option value="Yes">Yes</option>
                                        <option value="No">No</option>
                                    </select>
                                </td>
                                <td align="center"><input type="file" style="border:none; width:100% !important; padding-left: 0px;" name="supportingDocuments1" id="supportingDocuments1" dir="1"> &nbsp;<span style="cursor:pointer; background-color: #148534;color: #fff; padding: 5px 6px;border-radius: 3px;position: relative;top: -27px;left: 125px;" onclick="addPaperPresentRows();" class="smallgreenbtn"><i class="fa fa-plus-circle"></i>Add More</span></td>
                            </tr>
                        </tbody>
                    </table>
                    <input type="hidden" name="tr_row_paper_presented_count" id="tr_row_paper_presented_count" value="2">
                </td>
            </tr>
        </tbody>

<script>
  function addPaperPresentRows() {
    var rowCount = document.getElementById("tr_row_paper_presented_count").value;
    var recRow = `
        <tr id="tr_paper_presented_row${rowCount}">
            <td align="center"><input type="text" name="titleOfPaper${rowCount}" id="titleOfPaper${rowCount}" dir="${rowCount}" class="form-tb-input2" placeholder="Title/Subject of paper presented" value=""></td>
            <td align="center"><input type="text" name="subjectOfConference${rowCount}" id="subjectOfConference${rowCount}" dir="${rowCount}" class="form-tb-input2" placeholder="Subject of Conference/Seminar/Symposium/Workshop" value=""></td>
            <td><textarea name="organizingInstitutionCityCountry${rowCount}" id="organizingInstitutionCityCountry${rowCount}" dir="${rowCount}" style="width:95%;border: 1px solid #ccc; resize: none;border-radius: 5px;padding: 8px 5px;font-size: 14px; height:80px;"></textarea></td>
            <td align="center"><input type="text" name="durationFrom${rowCount}" id="durationFrom${rowCount}" dir="${rowCount}" class="form-tb-input2 date_pickere_byclass" placeholder="From" value=""> &nbsp;<input type="text" name="durationTo${rowCount}" id="durationTo${rowCount}" dir="${rowCount}" class="form-tb-input2 date_pickere_byclass" placeholder="To" value=""></td>
            <td align="center">
                <select name="proceedingsPublished${rowCount}" id="proceedingsPublished${rowCount}" dir="${rowCount}" class="form-tb-input2">
                    <option value="">Select</option>
                    <option value="Yes">Yes</option>
                    <option value="No">No</option>
                </select>
            </td>
            <td align="center"><input type="file" style="border:none; width:100% !important; padding-left: 0px;" name="supportingDocuments${rowCount}" id="supportingDocuments${rowCount}" dir="${rowCount}"> &nbsp;<a href="javascript:void(0);" style="background-color: #cd0a0a;text-decoration:none;color: #fff; padding: 5px 6px;border-radius: 3px;position: relative;top: -27px;left: 125px;" onclick="removePaperPresentRows(${rowCount});" class="smallgreybtn"><i class="fa fa-trash"></i> Remove</a></td>
        </tr>`;

    // Append the new row to the table body
    $('#paper_presented_table_body').append(recRow);

    // Update the row count
    document.getElementById("tr_row_paper_presented_count").value = Number(rowCount) + 1;
}

function removePaperPresentRows(removeNum) {
    $('#tr_paper_presented_row' + removeNum).remove();
}

</script>

      </table>
                </td>
            </tr>
            
        </tbody>
      </table>
      
      <table class="form-tb1" cellpadding="10" cellspacing="0" width="100%;">
        <tbody>
        	<tr><td colspan="7"><h3 style="text-align:left; margin-bottom:5px;">Other Miscellaneous Information</h3></td></tr>
            <tr><td colspan="7"> 1). Membership/Fellowship of other institutions/professionals societies : </td></tr>
            <tr><td colspan="7"><textarea name="membershipFellowshipInstitutions" id="membershipFellowshipInstitutions" style="width:95%;border: 1px solid #ccc;  resize: none;border-radius: 5px;padding: 8px 5px;font-size: 14px; height:80px;" placeholder="Membership/Fellowship of other institutions/professionals societies"></textarea></td></tr>
            <tr><td colspan="7"> 2). Other activities/Responsibilities : </td></tr>
            <tr><td colspan="7"><textarea name="otherActivities" id="otherActivities" style="width:95%;border: 1px solid #ccc;  resize: none;border-radius: 5px;padding: 8px 5px;font-size: 14px; height:80px;" placeholder="Other activities/Responsibilities"></textarea></td></tr>
            <tr><td colspan="7"> 3). Any other relevant information, if not given above : </td></tr>
            <tr><td colspan="7"><textarea name="otherRelevantInformation" id="otherRelevantInformation" style="width:95%;border: 1px solid #ccc;  resize: none;border-radius: 5px;padding: 8px 5px;font-size: 14px; height:80px;" placeholder="Any other relevant information, if not given above"></textarea></td></tr>

            <tr><td colspan="7"> 4). Has there been any break in your academic career ? If so, give details. : </td></tr>
            <tr><td colspan="7"><textarea name="breakAcademicCareer" id="breakAcademicCareer" style="width:95%;border: 1px solid #ccc;  resize: none;border-radius: 5px;padding: 8px 5px;font-size: 14px; height:80px;" placeholder="Has there been any break in your academic career ?"></textarea></td></tr>
            <tr><td colspan="7"> 5). Have you been punished during your studies at College/University ? If so, give details : </td></tr>
            <tr><td colspan="7"><textarea name="studiesCollegeUniversity" id="studiesCollegeUniversity" style="width:95%;border: 1px solid #ccc;  resize: none;border-radius: 5px;padding: 8px 5px;font-size: 14px; height:80px;" placeholder="Have you been punished during your studies at College/University ?"></textarea></td></tr>
            <tr><td colspan="7"> 6). Have you been punished during your services or convicted by a court of law ? If so, give details. : </td></tr>
            <tr><td colspan="7"><textarea name="courtOfLaw" id="courtOfLaw" style="width:95%;border: 1px solid #ccc;  resize: none;border-radius: 5px;padding: 8px 5px;font-size: 14px; height:80px;" placeholder="Have you been punished during your services or convicted by a court of law ?"></textarea></td></tr>
            <tr><td colspan="7"> 7). Were you at any time declared medically unfit or asked to submit your resignation or discharged or dismissed ? If yes, give details in a separate sheet.  : </td></tr>
            <tr><td colspan="7"><textarea name="submitResignation" id="submitResignation" style="width:95%;border: 1px solid #ccc;  resize: none;border-radius: 5px;padding: 8px 5px;font-size: 14px; height:80px;" placeholder="Were you at any time declared medically unfit or asked to submit your resignation or discharged or dismissed ? If yes, give details in a separate sheet"></textarea></td></tr>
            
            <tr><td colspan="7"> 8). Name and Addresses of Two references</td></tr>
            <tr><td colspan="4"><textarea name="addressesFirstReferences" id="addressesFirstReferences" style="width:95%;border: 1px solid #ccc;  resize: none;border-radius: 5px;padding: 8px 5px;font-size: 14px; height:80px;" placeholder="Name and Addresses of First References"></textarea></td>
            <td colspan="3"><textarea name="addressesSecondReferences" id="addressesSecondReferences" style="width:95%;border: 1px solid #ccc;  resize: none;border-radius: 5px;padding: 8px 5px;font-size: 14px; height:80px;" placeholder="Name and Addresses of Second References"></textarea></td>
            </tr>

             <tr><td colspan="7"> Please indicate how you fulfill the essential as well as desirable qualifications prescribed in the advertisement.</span> </td></tr>
            <tr><td colspan="7"><textarea name="fulfillEssentialAdvertisement" id="fulfillEssentialAdvertisement" style="width:95%;border: 1px solid #ccc;  resize: none;border-radius: 5px;padding: 8px 5px;font-size: 14px; height:80px;" placeholder="Please indicate how you fulfill the essential as well as desirable qualifications prescribed in the advertisement."></textarea></td></tr>
        </tbody>
      </table>
        <div class="form-part4">
        <p><b>Declaration to be Signed By The Candidate:</b><br />
            I hereby declare that the information given by me in the Application is true, complete and correct to the best of my knowledge and belief and that nothing has been concealed or distorted. If at any time, I am found to have concealed/distorted any information or given any false statement, my application/appointment shall liable to be summarily rejected/terminated without notice or compensation.</p>
        <ul>
            <li>
            <div class="left1"><span style="color:#f00;font-size: 15px;"> * </span> Place</div>
            <div class="right1">
                <input name="place" id="place" placeholder="Place" type="text"  value="" >
              </div>
          </li>
            <li>
            <div class="left1"><span style="color:#f00;font-size: 15px;"> * </span> Date</div>
            <div class="right1">
                <input type="text" name="date" placeholder="dd/mm/yyyy" id="date"  value="" >
              </div>
          </li>
            <li style="width:99.7%;border-right:1px solid #aaa;">
            <div class="form-sign">Signature</div>
          </li>
          </ul>
      </div>
        <div style="clear:both"></div>
        <table class="form-tb" cellpadding="10" cellspacing="0" border="1" bordercolor="#aaa"  width="100%;">
        <tbody>
            <tr>
          
          </tr>
            <tr height="50">
            <td>Attach Document Copy of the Letter From Present Employer of the applicant: <input type="file" name="attachDocument" id="attachDocument" /></td>
          </tr>
            <tr height="50"><td style="border: none !important;"></td></tr>
            <tr>
            <td align="center">
                <button name="save-button" id="save-button"  type="button" class="btn btn-primary"><span>Save Form</span></button>
                <button name="submit-button" id="submit-button" value="Submit" type="button" class="btn btn-primary"><span>Final Submit</span></button>
               
          </tr>
        
          </tbody>
      </table>
      </form>
      <div class="form_preasdfasdf"></div>
  </div>
    <div class="clear" style="height:50px;"></div>
   
<div class="clear"></div>
</div>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<!-- Toastr -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/js/toastr.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>

<script>
	$(document).ready(function(){
		$('#form_check').click(function(){
			var candiate_corres_addr1	=	$('#candiate_corres_addr1').val();
			var candiate_corres_addr2	=	$('#candiate_corres_addr2').val();
			var candiate_corres_pincode	=	$('#candiate_corres_pincode').val();
			var candiate_corres_stdcode	=	$('#candiate_corres_stdcode').val();
			var candiate_corres_stdno	=	$('#candiate_corres_stdno').val();
			if($("#form_check").is(':checked')){
				$('#candiate_permanent_addr1').val(candiate_corres_addr1);
				$('#candiate_permanent_addr2').val(candiate_corres_addr2);
				$('#candiate_permanent_pincode').val(candiate_corres_pincode);
				$('#candiate_permanent_stdcode').val(candiate_corres_stdcode);
				$('#candiate_permanent_stdno').val(candiate_corres_stdno);
			}else{
				$('#candiate_permanent_addr1').val('');
				$('#candiate_permanent_addr2').val('');
				$('#candiate_permanent_pincode').val('');
				$('#candiate_permanent_stdcode').val('');
				$('#candiate_permanent_stdno').val('');
			}
		});
	});
</script> 
<script>
    $(document).ready(function() {
    $('#postAppliedFor').change(function() {
        var selectedValue = $(this).val();
        if (selectedValue === 'Registrar') {
            $('#form-phd').show();
            $('#form-teaching').show();
            $('#form_row_table').show();
            $('#form_row_table_articles').show();
            $('#form-projecttaken').show();
            $('#form-paper').show();
           
        } else if (selectedValue === 'Administrator') {
            // Hide the teaching experience table
            $('#form-phd').hide();
            $('#form-teaching').hide();
            $('#form_row_table').hide();
            $('#form_row_table_articles').hide();
            $('#form-projecttaken').hide();
            $('#form-paper').hide();
        } 
    })
})

</script>

<script>
$(document).ready(function() {
    // Show/Hide forms based on the selected radio button
    $('input[name="haveApplicationNo"]').change(function() {
        if ($('#haveApplicationNoYes').is(':checked')) {
            $('#searchForm').show();
            $('#detailedForm').hide();
        } else if ($('#haveApplicationNoNo').is(':checked')) {
            $('#searchForm').hide();
            $('#detailedForm').show();
        }
    });
 
 })
 // Handle button click for generating application number
 $('#applicationbutton').click(function() {
        var mobile = $('#mobile').val();
        var name = $('#name').val();
        
        $.ajax({
    url: 'https://ansiss.res.in/api/v1/stage-two/candidate-application-no',
    type: 'POST',
    contentType: 'application/json',
    data: JSON.stringify({ mobileNo: mobile, name: name }),
    success: function(response) {
        if (response.status === 'success') {
            console.log(response);
            var applicationNo = response.data.data.applicationNo;
            $('#applicationnogenearted').text(applicationNo)
            Swal.fire({
                title:  'Your application number is: ' + applicationNo,
                text: 'You can Start Your Form Filling',
                icon: 'success',
                confirmButtonText: 'OK'
            });
        } else {
            Swal.fire({
                title: 'Error!',
                text: 'The server returned a failure response.',
                icon: 'error',
                confirmButtonText: 'OK'
            });
        }
    },
    error: function(error) {
        // Handle error case
        Swal.fire({
            title: 'Error!',
            text: 'There was an error processing your request. Please try again.',
            icon: 'error',
            confirmButtonText: 'OK'
        });
    }
});
 })
</script>
<script>
    $(document).ready(function() {
        $('#applicationbuttonsearch').click(function(e) {
            e.preventDefault(); // Prevent the default form submission
    
            // Get values from input fields
            var applicationNo = $('#applicationnosearch').val();

            // Validate inputs (optional but recommended)

            // Prepare data to be sent in the AJAX request
            var requestData = {
                applicationNo: applicationNo,
               
            };
    
           // Send AJAX request
    $.ajax({
        url: 'https://ansiss.res.in/api/v1/stage-two/get-admin-details', 
        type: 'POST', 
        contentType: 'application/json',
        data: JSON.stringify(requestData),
        success: function(response) {
        if (response.status === 'success') {
            populateForm(response.data);
            Swal.fire({
                title: 'Success!',
                text: 'Data fetched successfully Please RE-UPLOAD ALL DOCUMENTS FILE Then Final SUBMIT THE FORM',
                icon: 'success',
                confirmButtonText: 'OK'
            });
        } else {
            Swal.fire({
                title: 'Failed!',
                text: 'Failed to fetch data',
                icon: 'error',
                confirmButtonText: 'OK'
            });
        }
    },
    error: function() {
        Swal.fire({
            title: 'Error!',
            text: 'Error in fetching data',
            icon: 'error',
            confirmButtonText: 'OK'
        });
    }
});
    function populateForm(data) {
       
        var generalData = data.general[0];
        var educationData = data.educationalQualifications[0];
        var researchExperienceData =data.researchexperience[0];
        var miscellaneousData = data.otherMiscellaneousInformation[0];
        var experienceData = data.summaryofexperienceperformance[0];

$('#fullName').val(generalData.fullName);

$('#applicationnogenearted').text(generalData.applicationNo),
$('#dateOfBirth').val(generalData.dateOfBirth);
$('#fatherOrSpouseName').val(generalData.fatherOrSpouseName);
$('#mailingAddressLine1').val(generalData.mailingAddressLine1);
$('#mailingAddressLine2').val(generalData.mailingAddressLine2);
$('#mailingPinCode').val(generalData.mailingPinCode);
$('#mailingTelephoneNo').val(generalData.mailingTelephoneNo);
$('#permanentAddressLine1').val(generalData.permanentAddressLine1);
$('#permanentAddressLine2').val(generalData.permanentAddressLine2);
$('#permanentPinCode').val(generalData.permanentPinCode);
$('#permanentTelephoneNo').val(generalData.permanentTelephoneNo);
$('#mobileNo').val(generalData.mobileNo);
$('#emailAddress').val(generalData.emailAddress);
$('#gender').val(generalData.gender);
$('#maritalStatus').val(generalData.maritalStatus);
$('#nationality').val(generalData.nationality);
$('#stateOfDomicile').val(generalData.stateOfDomicile);
$('#category').val(generalData.category);
$('#presentEmployer').val(generalData.presentEmployer);
$('#postAppliedFor').val(generalData.postAppliedFor);
$('#specificSubject').val(generalData.specificSubject);

     // Education Data
         $('#phDBoardCollegeUniversity').val(educationData.phDBoardCollegeUniversity);
            $('#phDPercentageOfMarks').val(educationData.phDPercentageOfMarks);
            $('#phDSubjects').val(educationData.phDSubjects);
            $('#phDYearOfPassingAward').val(educationData.phDYearOfPassingAward);
            $('#postGraduationBoardCollegeUniversity').val(educationData.postGraduationBoardCollegeUniversity);
            $('#postGraduationPercentageOfMarks').val(educationData.postGraduationPercentageOfMarks);
            $('#postGraduationSubjects').val(educationData.postGraduationSubjects);
            $('#postGraduationYearOfPassingAward').val(educationData.postGraduationYearOfPassingAward);
            $('#graduationBoardCollegeUniversity').val(educationData.graduationBoardCollegeUniversity);
            $('#graduationPercentageOfMarks').val(educationData.graduationPercentageOfMarks);
            $('#graduationSubjects').val(educationData.graduationSubjects);
            $('#graduationYearOfPassingAward').val(educationData.graduationYearOfPassingAward);
            $('#xiiBoardCollegeUniversity').val(educationData.xiiBoardCollegeUniversity);
            $('#xiiPercentageOfMarks').val(educationData.xiiPercentageOfMarks);
            $('#xiiSubjects').val(educationData.xiiSubjects);
            $('#xiiYearOfPassingAward').val(educationData.xiiYearOfPassingAward);
            $('#xBoardCollegeUniversity').val(educationData.xBoardCollegeUniversity);
            $('#xPercentageOfMarks').val(educationData.xPercentageOfMarks);
            $('#xSubjects').val(educationData.xSubjects);
            $('#xYearOfPassingAward').val(educationData.xYearOfPassingAward);
            $('#othersBoardCollegeUniversity').val(educationData.othersBoardCollegeUniversity);
            $('#othersPercentageOfMarks').val(educationData.othersPercentageOfMarks);
            $('#othersSubjects').val(educationData.othersSubjects);
            $('#othersYearOfPassingAward').val(educationData.othersYearOfPassingAward);
            $('#postGraduationSupportingDocuments').attr('href', educationData.postGraduationSupportingDocuments);
            $('#graduationSupportingDocuments').attr('href', educationData.graduationSupportingDocuments);
            $('#xiiSupportingDocuments').attr('href', educationData.xiiSupportingDocuments);
            $('#xSupportingDocuments').attr('href', educationData.xSupportingDocuments);

            // Research Experience Data
            $('#researchExperienceFrom').val(researchExperienceData.researchExperienceFrom);
            $('#researchExperienceTo').val(researchExperienceData.researchExperienceTo);
            $('#researchExperienceTotalYears').val(researchExperienceData.researchExperienceTotalYears);
            $('#researchExperienceTotalMonths').val(researchExperienceData.researchExperienceTotalMonths);
            $('#researchExperienceSupportingDocuments').attr('href', researchExperienceData.researchExperienceSupportingDocuments);
            $('#studentRelatedCoCurricular').val(researchExperienceData.studentRelatedCoCurricular);
            $('#contributionToCorporate').val(researchExperienceData.contributionToCorporate);

               // Miscellaneous Data
            $('#membershipFellowshipInstitutions').val(miscellaneousData.membershipFellowshipInstitutions);
            $('#otherActivities').val(miscellaneousData.otherActivities);
            $('#otherRelevantInformation').val(miscellaneousData.otherRelevantInformation);
            $('#breakAcademicCareer').val(miscellaneousData.breakAcademicCareer);
            $('#studiesCollegeUniversity').val(miscellaneousData.studiesCollegeUniversity);
            $('#courtOfLaw').val(miscellaneousData.courtOfLaw);
            $('#submitResignation').val(miscellaneousData.submitResignation);
            $('#addressesFirstReferences').val(miscellaneousData.addressesFirstReferences);
            $('#addressesSecondReferences').val(miscellaneousData.addressesSecondReferences);
            $('#fulfillEssentialAdvertisement').val(miscellaneousData.fulfillEssentialAdvertisement);
            $('#place').val(miscellaneousData.place);
            $('#date').val(miscellaneousData.date);

 // Research Experience Data
 $('#underGraduateFrom').val(experienceData.underGraduateFrom);
            $('#underGraduateTo').val(experienceData.underGraduateTo);
            $('#underGraduateTotalYears').val(experienceData.underGraduateTotalYears);
            $('#underGraduateTotalMonths').val(experienceData.underGraduateTotalMonths);
            $('#underGraduateSupportingDocuments').attr('href', experienceData.underGraduateSupportingDocuments);
            $('#postGraduateFrom').val(experienceData.postGraduateFrom);
            $('#postGraduateTo').val(experienceData.postGraduateTo);
            $('#postGraduateTotalYears').val(experienceData.postGraduateTotalYears);
            $('#postGraduateTotalMonths').val(experienceData.postGraduateTotalMonths);
            $('#postGraduateSupportingDocuments').attr('href', experienceData.postGraduateSupportingDocuments);
            $('#totalTeachingFrom').val(experienceData.totalTeachingFrom);
            $('#totalTeachingTo').val(experienceData.totalTeachingTo);
            $('#totalTeachingExperienceYears').val(experienceData.totalTeachingExperienceYears);
            $('#totalTeachingExperienceMonths').val(experienceData.totalTeachingExperienceMonths);
            $('#totalTeachingSupportingDocuments').attr('href', experienceData.totalTeachingSupportingDocuments);
            $('#shortTermFrom').val(experienceData.shortTermFrom);
            $('#shortTermTo').val(experienceData.shortTermTo);
            $('#shortTermTotalYears').val(experienceData.shortTermTotalYears);
            $('#shortTermTotalMonths').val(experienceData.shortTermTotalMonths);

    data.detailsEmploymentExperience.forEach(function(item, index) {
        // Only add rows if index > 0
        if (index > 0) {
            // Clone the first row and append it
            var newRow = $('#tr_row1').clone().attr('id', 'tr_row' + (index + 1));
            newRow.find('input, select').each(function() {
                var name = $(this).attr('name').replace('1', index + 1);
                var id = $(this).attr('id').replace('1', index + 1);
                $(this).attr('name', name).attr('id', id).val('');
            });
            $('#form_table tbody').append(newRow);
        }

        // Populate the values
        $('#nameOfEmployer' + (index + 1)).val(item.nameOfEmployer);
        $('#postHeldDesignation' + (index + 1)).val(item.postHeldDesignation);
        $('#periodEmploymentFromMonth' + (index + 1)).val(item.periodEmploymentFromMonth);
        $('#periodEmploymentFromYear' + (index + 1)).val(item.periodEmploymentFromYear);
        $('#periodEmploymentToMonth' + (index + 1)).val(item.periodEmploymentToMonth);
        $('#periodEmploymentToYear' + (index + 1)).val(item.periodEmploymentToYear);
        $('#basicSalaryLastDrawnPayScaleAndGradePay' + (index + 1)).val(item.basicSalaryLastDrawnPayScaleAndGradePay);
        $('#natureOfDuties' + (index + 1)).val(item.natureOfDuties);
        // Note: File inputs cannot be populated programmatically due to security reasons.
    });

    // Update the tr_count hidden input
    $('#tr_count').val(data.detailsEmploymentExperience.length + 1);

    data.researchPublicationChapterContributed.forEach(function(item, index) {
    if (index > 0) {
        var newRow = $('#tr_chapter_row1').clone().attr('id', 'tr_chapter_row' + (index + 1));
        newRow.find('input, select, textarea').each(function() {
            var name = $(this).attr('name').replace('1', index + 1);
            var id = $(this).attr('id').replace('1', index + 1);
            $(this).attr('name', name).attr('id', id).val('');
        });
        $('#form_row_chapter_table tbody').append(newRow);
    }

    $('#contributedTitleOfChapter' + (index + 1)).val(item.titleOfChapter);
    $('#ContributedTitleOfBook' + (index + 1)).val(item.titleOfBook);
    $('#ContributedauthorType' + (index + 1)).val(item.authorType);
    $('#ContributedPublisher' + (index + 1)).val(item.publisher);
    $('#contrubutedMonth' + (index + 1)).val(item.month);
    $('#contrubutedYear' + (index + 1)).val(item.year);
    $('#ContrubutedIsbnIssnNo' + (index + 1)).val(item.isbnIssnNo);
    // Note: File inputs cannot be populated programmatically due to security reasons.
});
$('#tr_chapter_row_count').val(data.researchPublicationChapterContributed.length + 1);

data.researchProjectUnderTakend.forEach(function(item, index) {
    if (index > 0) {
        var newRow = $('#tr_project_row1').clone().attr('id', 'tr_project_row' + (index + 1));
        newRow.find('input, select, textarea').each(function() {
            var name = $(this).attr('name').replace('1', index + 1);
            var id = $(this).attr('id').replace('1', index + 1);
            $(this).attr('name', name).attr('id', id).val('');
        });
        $('#form_row_table_project tbody').append(newRow);
    }

    $('#researchTitleSubject' + (index + 1)).val(item.researchTitleSubject);
    $('#researchProjectType' + (index + 1)).val(item.researchProjectType);
    $('#researchDateCommencement' + (index + 1)).val(item.researchDateCommencement);
    $('#researchDateCompletion' + (index + 1)).val(item.researchDateCompletion);
    $('#researchTotalGrantsReceivedRs' + (index + 1)).val(item.researchTotalGrantsReceivedRs);
    $('#researchNameSponsoringAgency' + (index + 1)).val(item.researchNameSponsoringAgency);
    $('#researchOutcomeOutputsSentGovt' + (index + 1)).val(item.researchOutcomeOutputsSentGovt);
    $('#researchFinalReportPublished' + (index + 1)).val(item.researchFinalReportPublished);
    // Note: File inputs cannot be populated programmatically due to security reasons.
});

// Update the tr_row_project_count hidden input
$('#tr_row_project_count').val(data.researchProjectUnderTakend.length + 1);

data.researchPublicationArticlesPublished.forEach(function(item, index) {
    if (index > 0) {
        var newRow = $('#tr_article_row1').clone().attr('id', 'tr_article_row' + (index + 1));
        newRow.find('input, select, textarea').each(function() {
            var name = $(this).attr('name').replace('1', index + 1);researchIsbnIssnNo
            var id = $(this).attr('id').replace('1', index + 1);
            $(this).attr('name', name).attr('id', id).val('');
        });
        $('#form_row_table_articles tbody').append(newRow);
    }

    $('#titleOfArticle' + (index + 1)).val(item.titleOfArticle);
    $('#nameOfJournal' + (index + 1)).val(item.nameOfJournal);
    $('#authorType' + (index + 1)).val(item.authorType);
    $('#publicationDetails' + (index + 1)).val(item.publicationDetails);
    $('#referredStatus' + (index + 1)).val(item.referredStatus);
    $('#' + (index + 1)).val(item.isbnIssnNo);
    $('#level' + (index + 1)).val(item.level);
    $('#impactFactor' + (index + 1)).val(item.impactFactor);
    // Note: File inputs cannot be populated programmatically due to security reasons.
});

// Update the tr_row_article_count hidden input
$('#tr_row_article_count').val(data.researchPublicationArticlesPublished.length + 1);

data.projectUnderTaken.forEach(function(item, index) {
            if (index > 0) {
                // Clone the first row and update its ID
                var newRow = $('#tr_paper_presented_row1').clone().attr('id', 'tr_paper_presented_row' + (index + 1));
                
                // Update input names and IDs
                newRow.find('input, select, textarea').each(function() {
                    var name = $(this).attr('name').replace('1', index + 1);
                    var id = $(this).attr('id').replace('1', index + 1);
                    $(this).attr('name', name).attr('id', id).val('');
                });
                
                // Append the new row to the table
                $('#paper_presented_table_body').append(newRow);
            }

            // Populate fields with data
            $('#titleOfPaper' + (index + 1)).val(item.titleOfPaper);
            $('#subjectOfConference' + (index + 1)).val(item.subjectOfConference);
            $('#organizingInstitutionCityCountry' + (index + 1)).val(item.organizingInstitutionCityCountry);
            $('#durationFrom' + (index + 1)).val(item.durationFrom);
            $('#durationTo' + (index + 1)).val(item.durationTo);
            $('#proceedingsPublished' + (index + 1)).val(item.proceedingsPublished);
            // Note: File inputs cannot be populated programmatically due to security reasons.
        });

        // Update the tr_row_paper_presented_count hidden input
        $('#tr_row_paper_presented_count').val(data.projectUnderTaken.length + 1);

data.researchPublication.forEach(function(item, index) {
    if (index > 0) {
        var newRow = $('#trs_row1').clone().attr('id', 'trs_row' + (index + 1));
        newRow.find('input, select, textarea').each(function() {
            var name = $(this).attr('name').replace('1', index + 1);
            var id = $(this).attr('id').replace('1', index + 1);
            $(this).attr('name', name).attr('id', id).val('');
        });
        $('#form_row_table tbody').append(newRow);
    }

           $('#booksTitleOfTheBook' + (index + 1)).val(item.booksTitleOfTheBook);
            $('#booksSoleAuthorCoauthor' + (index + 1)).val(item.booksSoleAuthorCoauthor);
            $('#booksNamePublisherCountry' + (index + 1)).val(item.booksNamePublisherCountry);
            $('#booksMonthYearPublication' + (index + 1)).val(item.booksMonthYearPublication);
            $('#booksIsbnIssnNo' + (index + 1)).val(item.booksIsbnIssnNo);
});

$('#tr_row_count').val(data.researchPublication.length + 1);
    }
        })
    })
    </script>

<script>
$(document).ready(function() {
    const uploadedFiles = {};

    // File upload handler
      $('input[type="file"]').on('change', function(event) {
        var applicationNo = $('#applicationnogenearted').text().trim();
        if ( applicationNo === '') {
             alert('Please Generate Application Number First From The Top Then Upload Any File');
             return;
         }
        const fileInput = $(this);
        const file = fileInput[0].files[0];
        const fileInputId = fileInput.attr('id');
        console.log(file);

         if (file) {
            const formData = new FormData();
            formData.append(fileInput.attr('name'), file);
   // Retrieve the application number from the HTML element
    
        formData.append('applicationNo', applicationNo);
        console.log(formData);
            $.ajax({
                url: 'https://ansiss.res.in/api/v1/stage-two/file',
                type: 'POST',
                data: formData,
                contentType: false,
                processData: false,
                success: function(response) {
                    if (response.status === "success" && response.data.success && response.data.data.filePaths.length > 0) {
                        const filePath = response.data.data.filePaths[0];
                       uploadedFiles[fileInputId] = filePath;
                        alert('Documents uploaded successfully!');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error uploading file:', error);
                    alert('Error uploading file: ' + error);
                }
            });
        }
    });
//FOR SAVE BUTTON 
    $('#save-button').on('click', function(event) {
    event.preventDefault();

    var applicationNo = $('#applicationnogenearted').text().trim();

   if ( applicationNo === '') {
                alert('Please Generate Application Number First From The Top Then Submit the Form');
                return;
            }

    var formData = {
        applicationNo: $('#applicationnogenearted').text(),
        phdBoardCollegeUniversity: $('#phdBoardCollegeUniversity').val() || '',
        phdPercentageOfMarks: $('#phdPercentageOfMarks').val() || '',
        phdSubjects: $('#phdSubjects').val() || '',
        phdYearOfPassingAward: $('#phdYearOfPassingAward').val() || '',
        phdSupportingDocuments: uploadedFiles['phdSupportingDocuments'] || '',
        postGraduationBoardCollegeUniversity: $('#postGraduationBoardCollegeUniversity').val() || '',
        postGraduationPercentageOfMarks: $('#postGraduationPercentageOfMarks').val() || '',
        postGraduationSubjects: $('#postGraduationSubjects').val() || '',
        postGraduationYearOfPassingAward: $('#postGraduationYearOfPassingAward').val() || '',
        postGraduationSupportingDocuments: uploadedFiles['postGraduationSupportingDocuments'] || '',
        graduationBoardCollegeUniversity: $('#graduationBoardCollegeUniversity').val() || '',
        graduationPercentageOfMarks: $('#graduationPercentageOfMarks').val() || '',
        graduationSubjects: $('#graduationSubjects').val() || '',
        graduationYearOfPassingAward: $('#graduationYearOfPassingAward').val() || '',
        graduationSupportingDocuments: uploadedFiles['graduationSupportingDocuments'] || '',
        xiiBoardCollegeUniversity: $('#xiiBoardCollegeUniversity').val() || '',
        xiiPercentageOfMarks: $('#xiiPercentageOfMarks').val() || '',
        xiiSubjects: $('#xiiSubjects').val() || '',
        xiiYearOfPassingAward: $('#xiiYearOfPassingAward').val() || '',
        xiiSupportingDocuments: uploadedFiles['xiiSupportingDocuments'] || '',
        xBoardCollegeUniversity: $('#xBoardCollegeUniversity').val() || '',
        xPercentageOfMarks: $('#xPercentageOfMarks').val() || '',
        xSubjects: $('#xSubjects').val() || '',
        xYearOfPassingAward: $('#xYearOfPassingAward').val() || '',
        xSupportingDocuments:  uploadedFiles['xSupportingDocuments'] || '',
        othersBoardCollegeUniversity: $('#othersBoardCollegeUniversity').val() || '',
        othersPercentageOfMarks: $('#othersPercentageOfMarks').val() || '',
        othersSubjects: $('#othersSubjects').val() || '',
        othersYearOfPassingAward: $('#othersYearOfPassingAward').val() || '',
        othersSupportingDocuments: uploadedFiles['othersSupportingDocuments'] || '',
        phdAwarded: $('#phdAwarded').val() || '',
        phdThesisAwarded: $('#phdThesisAwarded').val() || '',
        ugcCSIR: $('#ugcCSIR').val() || '',
        researchExperienceFrom: $('#researchExperienceFrom').val() || '',
        researchExperienceTo: $('#researchExperienceTo').val() || '',
        researchExperienceTotalYears: $('#researchExperienceTotalYears').val() || '',
        researchExperienceTotalMonths: $('#researchExperienceTotalMonths').val() || '',
        researchExperienceSupportingDocuments:uploadedFiles['researchExperienceSupportingDocuments'] || '',
        studentRelatedCoCurricular: $('#studentRelatedCoCurricular').val() || '',
        contributionToCorporate: $('#contributionToCorporate').val() || '',
        undergraduateFrom: $('#undergraduateFrom').val() || '',
        undergraduateTo: $('#undergraduateTo').val() || '',
        undergraduateTotalYears: $('#undergraduateTotalYears').val() || '',
        undergraduateTotalMonths: $('#undergraduateTotalMonths').val() || '',
        undergraduateSupportingDocuments: uploadedFiles['undergraduateSupportingDocuments'] || '',
        postGraduateFrom: $('#postGraduateFrom').val() || '',
        postGraduateTo: $('#postGraduateTo').val() || '',
        postGraduateTotalYears: $('#postGraduateTotalYears').val() || '',
        postGraduateTotalMonths: $('#postGraduateTotalMonths').val() || '',
        postGraduateSupportingDocuments:  uploadedFiles['postGraduateSupportingDocuments'] || '',
        totalTeachingExperienceYears: $('#totalTeachingExperienceYears').val() || '',
        totalTeachingExperienceMonths: $('#totalTeachingExperienceMonths').val() || '',
        totalTeachingExperienceSupportingDocuments: $('#totalTeachingExperienceSupportingDocuments').val() || '',
        fullName: $('#fullName').val() || '',
        dateOfBirth: $('#dateOfBirth').val() || '',
        fatherOrSpouseName: $('#fatherOrSpouseName').val() || '',
        mailingAddressLine1: $('#mailingAddressLine1').val() || '',
        mailingAddressLine2: $('#mailingAddressLine2').val() || '',
        mailingPinCode: $('#mailingPinCode').val() || '',
        mailingTelephoneNumber: $('#mailingTelephoneNumber').val() || '',
        permanentAddressLine1: $('#permanentAddressLine1').val() || '',
        permanentAddressLine2: $('#permanentAddressLine2').val() || '',
        permanentPinCode: $('#permanentPinCode').val() || '',
        permanentTelephoneNumber: $('#permanentTelephoneNumber').val() || '',
        mobileNo: $('#mobileNo').val() || '',
        emailAddress: $('#emailAddress').val() || '',
        gender: $('#gender').val() || '',
        maritalStatus: $('#maritalStatus').val() || '',
        category: $('#category').val() || '',
        presentEmployer: $('#presentEmployer').val() || '',
        title: $('#title').val() || '',
        nationality: $('#nationality').val() || '',
        stateOfDomicile: $('#stateOfDomicile').val() || '',
        postAppliedFor: $('#postAppliedFor').val() || '',
        specificSubject: $('#specificSubject').val() || '',
        imageDoc:  uploadedFiles['imageDoc'] || '',
        ugcYear: $('#ugcYear').val() || '',
        ugcDoc:  uploadedFiles['ugcDoc'] || '',
        totalTeachingFrom: $('#totalTeachingFrom').val() || '',
        TotalTeachingTo: $('#TotalTeachingTo').val() || '',
        shortTermFrom: $('#shortTermFrom').val() || '',
        shortTermTo: $('#shortTermTo').val() || '',
        shortTermYears: $('#shortTermYears').val() || '',
        shortTermMonths: $('#shortTermMonths').val() || '',
        shortTermSupportingDocument: uploadedFiles['shortTermSupportingDocument'] || '',
        membershipFellowshipInstitutions: $('#membershipFellowshipInstitutions').val() || '',
        otherActivities: $('#otherActivities').val() || '',
        otherRelevantInformation: $('#otherRelevantInformation').val() || '',
        breakAcademicCareer: $('#breakAcademicCareer').val() || '',
        studiesCollegeUniversity: $('#studiesCollegeUniversity').val() || '',
        courtOfLaw: $('#courtOfLaw').val() || '',
        submitResignation: $('#submitResignation').val() || '',
        addressesFirstReferences: $('#addressesFirstReferences').val() || '',
        addressesSecondReferences: $('#addressesSecondReferences').val() || '',
        fulfillEssentialAdvertisement: $('#fulfillEssentialAdvertisement').val() || '',
        place: $('#place').val() || '',
        date: $('#date').val() || '',
        attachDocument: uploadedFiles['attachDocument'] || '',
        finalSubmit:"NO"
  
            };
            formData.detailsEmploymentExperience = [];
    formData.reseachPublicationBook = [];
    formData.reseachContributed =[];
    formData.projectUnderTaken=[];
    formData.researchPublished=[];
    formData.projectPapers=[];

    var uniqueFieldIdCounter = 1;

    // Loop through each row in the table
    $('#form_table tr[id^="tr_row"]').each(function() {
        var rowId = $(this).attr('id').replace('tr_row', '');

        var employmentData = {
            applicationNo: $('#applicationnogenearted').text(),
            nameOfEmployer: $('#nameOfEmployer' + rowId).val() || '',
            statusInstitute: $('#statusInstitute' + rowId).val() || '',
            postHeldDesignation: $('#postHeldDesignation' + rowId).val() || '',
            periodEmploymentFrom: ($('#periodEmploymentFromMonth' + rowId).val() || '') + ' ' + ($('#periodEmploymentFromYear' + rowId).val() || ''),
            periodEmploymentTo: ($('#periodEmploymentToMonth' + rowId).val() || '') + ' ' + ($('#periodEmploymentToYear' + rowId).val() || ''),
            basicSalaryLastDrawnPayScaleAndGradePay: $('#basicSalaryLastDrawnPayScaleAndGradePay' + rowId).val() || '',
            natureOfDuties: $('#natureOfDuties' + rowId).val() || '',
          filePaths: uploadedFiles[`filePaths${rowId}`] || '',
          fieldId: uniqueFieldIdCounter 
        };

        formData.detailsEmploymentExperience.push(employmentData);
        uniqueFieldIdCounter++;
    });

    var uniqueFieldIdCounter = 1;
    $('#form_row_table tr[id^="trs_row"]').each(function() {
        var rowId = $(this).attr('id').replace('trs_row', '');

    var publicationData = {
        applicationNo: $('#applicationnogenearted').text(),
        booksTitleOfTheBook: $('#booksTitleOfTheBook' + rowId).val() || '',
        booksSoleAuthorCoauthor: $('#booksSoleAuthorCoauthor' + rowId).val() || '',
        booksNamePublisherCountry: $('#booksNamePublisherCountry' + rowId).val() || '',
        booksMonthYearPublication: ($('#booksMonthYearPublication' + rowId).val() || '') + ' ' + ($('#booksMonthYearPublication' + rowId).val() || ''),
        booksIsbnIssnNo: $('#booksIsbnIssnNo' + rowId).val() || '',
    booksSupportingDocuments: uploadedFiles['booksSupportingDocuments' + rowId] || '',
    fieldId: uniqueFieldIdCounter 
    };
    uniqueFieldIdCounter++;
    formData.reseachPublicationBook.push(publicationData);
});

var uniqueFieldIdCounter = 1;
$('#form_row_chapter_table tr[id^="tr_chapter_row"]').each(function() {
    var rowId = $(this).attr('id').replace('tr_chapter_row', '');

    var contributedData = {
        applicationNo: $('#applicationnogenearted').text(),
        contributedTitleOfChapter: $('#contributedTitleOfChapter' + rowId).val() || '',
        ContributedTitleOfBook: $('#ContributedTitleOfBook' + rowId).val() || '',
        ContributedauthorType: $('#ContributedauthorType' + rowId).val() || '',
        ContributedPublisher: $('#ContributedPublisher' + rowId).val() || '',
        contrubutedMonth: $('#contrubutedMonth' + rowId).val() || '',
        contrubutedYear: $('#contrubutedYear' + rowId).val() || '',
        ContrubutedIsbnIssnNo: $('#ContrubutedIsbnIssnNo' + rowId).val() || '',
        contributedSupportingDocuments:uploadedFiles['contributedSupportingDocuments'+ rowId] || '',
        fieldId: uniqueFieldIdCounter 
    };
    uniqueFieldIdCounter++;
    formData.reseachContributed.push(contributedData);
});
var uniqueFieldIdCounter = 1;
// Loop through each row in the research project table
$('#form_row_table_project tr[id^="tr_project_row"]').each(function() {
    var rowId = $(this).attr('id').replace('tr_project_row', '');

    var projectData = {
        applicationNo: $('#applicationnogenearted').text(),
        researchTitleSubject: $('#researchTitleSubject' + rowId).val() || '',
        researchProjectType: $('#researchProjectType' + rowId).val() || '',
        researchDateCommencement: $('#researchDateCommencement' + rowId).val() || '',
        researchDateCompletion: $('#researchDateCompletion' + rowId).val() || '',
        researchTotalGrantsReceivedRs: $('#researchTotalGrantsReceivedRs' + rowId).val() || '',
        researchNameSponsoringAgency: $('#researchNameSponsoringAgency' + rowId).val() || '',
        researchOutcomeOutputsSentGovt: $('#researchOutcomeOutputsSentGovt' + rowId).val() || '',
        researchFinalReportPublished: $('#researchFinalReportPublished' + rowId).val() || '',
        researchSupportingDocuments:uploadedFiles['researchSupportingDocuments'+ rowId] || '',
        fieldId: uniqueFieldIdCounter 
    };
    uniqueFieldIdCounter++;
    formData.projectUnderTaken.push(projectData);
});
var uniqueFieldIdCounter = 1;
$('#form_row_table_articles tr[id^="tr_article_row"]').each(function() {
    var rowId = $(this).attr('id').replace('tr_article_row', '');

    var publicationDataResearch = {
        applicationNo: $('#applicationnogenearted').text(),
        titleOfArticle: $('#titleOfArticle' + rowId).val() || '',
        nameOfJournal: $('#nameOfJournal' + rowId).val() || '',
        authorType: $('#authorType' + rowId).val() || '',
        publicationDetails: $('#publicationDetails' + rowId).val() || '',
        referredStatus: $('#referredStatus' + rowId).val() || '',
        researchIsbnIssnNo: $('#researchIsbnIssnNo' + rowId).val() || '',
        level: $('#level' + rowId).val() || '',
        impactFactor: $('#impactFactor' + rowId).val() || '',
        researchSupportingDocuments: uploadedFiles['researchSupportingDocuments'+ rowId] || '',
        fieldId: uniqueFieldIdCounter 
    };
    uniqueFieldIdCounter++;
    formData.researchPublished.push(publicationDataResearch);
});
var uniqueFieldIdCounter = 1;
$('#form_row_table_paper_presented tr[id^="tr_paper_presented_row"]').each(function() {
    var rowId = $(this).attr('id').replace('tr_paper_presented_row', '');

    var publicationprojectPapers = {
        applicationNo: $('#applicationnogenearted').text(),
        titleOfPaper: $('#titleOfPaper' + rowId).val() || '',
        subjectOfConference: $('#subjectOfConference' + rowId).val() || '',
        organizingInstitutionCityCountry: $('#organizingInstitutionCityCountry' + rowId).val() || '',
        durationFrom: $('#durationFrom' + rowId).val() || '',
        durationTo: $('#durationTo' + rowId).val() || '',
        proceedingsPublished: $('#proceedingsPublished' + rowId).val() || '',
        supportingDocuments:uploadedFiles['supportingDocuments'+ rowId] || '',
        fieldId: uniqueFieldIdCounter 
    };
    uniqueFieldIdCounter++;
    formData.projectPapers.push(publicationprojectPapers);
});

    // formData.projectPapers = [
    //     {
    //         applicationNo: $('#applicationnogenearted').text(),
    //         titleOfPaper: $('#titleOfPaper').val() || '',
    //         subjectOfConference: $('#subjectOfConference').val() || '',
    //         organizingInstitutionCityCountry: $('#organizingInstitutionCityCountry').val() || '',
    //         durationFrom: $('#durationFrom').val() || '',
    //         durationTo: $('#durationTo').val() || '',
    //         proceedingsPublished: $('#proceedingsPublished').val() || '',
    //         supportingDocuments: $('#supportingDocuments').val() || ''
    //     }
    // ];

    $.ajax({
        url: 'https://ansiss.res.in/api/v1/stage-two/insert-data',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(formData), 
        success: function(response) {
        Swal.fire({
            icon: 'success',
            title: 'Form is Partially Saved successfully',
            text: 'You Can Search Details With Application Number!'
        }).then((result) => {
                if (result.isConfirmed) {
                    generatePDF(formData);
                }
            })
    
        console.log('Form submitted successfully:', response);
    },
    error: function(xhr, status, error) {
     
        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: 'Error In Saving form: ' + error
        });
        console.error('Error submitting form:', error);
    }
    });
});

    $('#submit-button').on('click', function(event) {
    event.preventDefault();

    var applicationNo = $('#applicationnogenearted').text().trim();

   if ( applicationNo === '') {
                alert('Please Generate Application Number First From The Top Then Submit the Form');
                return;
            }
    //         if (
    //     $('#fullName').val().trim() === '' ||
    //     $('#dateOfBirth').val().trim() === '' ||
    //     $('#fatherOrSpouseName').val().trim() === '' ||
    //     $('#mailingAddressLine1').val().trim() === '' ||
    //     $('#mailingPinCode').val().trim() === '' ||
    //     $('#permanentAddressLine1').val().trim() === '' ||
    //     $('#permanentPinCode').val().trim() === '' ||
    //     $('#mobileNo').val().trim() === '' ||
    //     $('#emailAddress').val().trim() === '' ||
    //     // $('#gender').val().trim() === '' ||
    //     $('#maritalStatus').val().trim() === '' ||
    //     $('#category').val().trim() === '' ||
    //     $('#nationality').val().trim() === '' ||
    //     $('#stateOfDomicile').val().trim() === '' ||
    //     // $('#postAppliedFor').val().trim() === '' ||
    //     $('#specificSubject').val().trim() === ''
    // ) {
    //     alert('Please fill out all mandatory personal information fields.');
    //     return;
    // }
// Validate mandatory fields for other sections

    // if (
    //     $('#graduationBoardCollegeUniversity').val().trim() === '' ||
    //     $('#graduationPercentageOfMarks').val().trim() === '' ||
    //     $('#graduationSubjects').val().trim() === '' ||
    //     $('#graduationYearOfPassingAward').val().trim() === ''
    // ) {
    //     alert('Please fill out all mandatory fields in the Graduation section Of Educational Qualification .');
    //     return;
    // }

    // if (
    //     $('#xiiBoardCollegeUniversity').val().trim() === '' ||
    //     $('#xiiPercentageOfMarks').val().trim() === '' ||
    //     $('#xiiSubjects').val().trim() === '' ||
    //     $('#xiiYearOfPassingAward').val().trim() === ''
    // ) {
    //     alert('Please fill out all mandatory fields in the XII section Of Educational Qualification .');
    //     return;
    // }

    // if (
    //     $('#xBoardCollegeUniversity').val().trim() === '' ||
    //     $('#xPercentageOfMarks').val().trim() === '' ||
    //     $('#xSubjects').val().trim() === '' ||
    //     $('#xYearOfPassingAward').val().trim() === ''
    // ) {
    //     alert('Please fill out all mandatory fields in the X section Of Educational Qualification .');
    //     return;
    // }

    // Validate mandatory personal information fields

    var formData = {
        applicationNo: $('#applicationnogenearted').text(),
        phdBoardCollegeUniversity: $('#phdBoardCollegeUniversity').val() || '',
        phdPercentageOfMarks: $('#phdPercentageOfMarks').val() || '',
        phdSubjects: $('#phdSubjects').val() || '',
        phdYearOfPassingAward: $('#phdYearOfPassingAward').val() || '',
        phdSupportingDocuments: uploadedFiles['phdSupportingDocuments'] || '',
        postGraduationBoardCollegeUniversity: $('#postGraduationBoardCollegeUniversity').val() || '',
        postGraduationPercentageOfMarks: $('#postGraduationPercentageOfMarks').val() || '',
        postGraduationSubjects: $('#postGraduationSubjects').val() || '',
        postGraduationYearOfPassingAward: $('#postGraduationYearOfPassingAward').val() || '',
        postGraduationSupportingDocuments: uploadedFiles['postGraduationSupportingDocuments'] || '',
        graduationBoardCollegeUniversity: $('#graduationBoardCollegeUniversity').val() || '',
        graduationPercentageOfMarks: $('#graduationPercentageOfMarks').val() || '',
        graduationSubjects: $('#graduationSubjects').val() || '',
        graduationYearOfPassingAward: $('#graduationYearOfPassingAward').val() || '',
        graduationSupportingDocuments: uploadedFiles['graduationSupportingDocuments'] || '',
        xiiBoardCollegeUniversity: $('#xiiBoardCollegeUniversity').val() || '',
        xiiPercentageOfMarks: $('#xiiPercentageOfMarks').val() || '',
        xiiSubjects: $('#xiiSubjects').val() || '',
        xiiYearOfPassingAward: $('#xiiYearOfPassingAward').val() || '',
        xiiSupportingDocuments: uploadedFiles['xiiSupportingDocuments'] || '',
        xBoardCollegeUniversity: $('#xBoardCollegeUniversity').val() || '',
        xPercentageOfMarks: $('#xPercentageOfMarks').val() || '',
        xSubjects: $('#xSubjects').val() || '',
        xYearOfPassingAward: $('#xYearOfPassingAward').val() || '',
        xSupportingDocuments:  uploadedFiles['xSupportingDocuments'] || '',
        othersBoardCollegeUniversity: $('#othersBoardCollegeUniversity').val() || '',
        othersPercentageOfMarks: $('#othersPercentageOfMarks').val() || '',
        othersSubjects: $('#othersSubjects').val() || '',
        othersYearOfPassingAward: $('#othersYearOfPassingAward').val() || '',
        othersSupportingDocuments: uploadedFiles['othersSupportingDocuments'] || '',
        phdAwarded: $('#phdAwarded').val() || '',
        phdThesisAwarded: $('#phdThesisAwarded').val() || '',
        ugcCSIR: $('#ugcCSIR').val() || '',
        researchExperienceFrom: $('#researchExperienceFrom').val() || '',
        researchExperienceTo: $('#researchExperienceTo').val() || '',
        researchExperienceTotalYears: $('#researchExperienceTotalYears').val() || '',
        researchExperienceTotalMonths: $('#researchExperienceTotalMonths').val() || '',
        researchExperienceSupportingDocuments:uploadedFiles['researchExperienceSupportingDocuments'] || '',
        studentRelatedCoCurricular: $('#studentRelatedCoCurricular').val() || '',
        contributionToCorporate: $('#contributionToCorporate').val() || '',
        undergraduateFrom: $('#undergraduateFrom').val() || '',
        undergraduateTo: $('#undergraduateTo').val() || '',
        undergraduateTotalYears: $('#undergraduateTotalYears').val() || '',
        undergraduateTotalMonths: $('#undergraduateTotalMonths').val() || '',
        undergraduateSupportingDocuments: uploadedFiles['undergraduateSupportingDocuments'] || '',
        postGraduateFrom: $('#postGraduateFrom').val() || '',
        postGraduateTo: $('#postGraduateTo').val() || '',
        postGraduateTotalYears: $('#postGraduateTotalYears').val() || '',
        postGraduateTotalMonths: $('#postGraduateTotalMonths').val() || '',
        postGraduateSupportingDocuments:  uploadedFiles['postGraduateSupportingDocuments'] || '',
        totalTeachingExperienceYears: $('#totalTeachingExperienceYears').val() || '',
        totalTeachingExperienceMonths: $('#totalTeachingExperienceMonths').val() || '',
        totalTeachingExperienceSupportingDocuments: $('#totalTeachingExperienceSupportingDocuments').val() || '',
        fullName: $('#fullName').val() || '',
        dateOfBirth: $('#dateOfBirth').val() || '',
        fatherOrSpouseName: $('#fatherOrSpouseName').val() || '',
        mailingAddressLine1: $('#mailingAddressLine1').val() || '',
        mailingAddressLine2: $('#mailingAddressLine2').val() || '',
        mailingPinCode: $('#mailingPinCode').val() || '',
        mailingTelephoneNumber: $('#mailingTelephoneNumber').val() || '',
        permanentAddressLine1: $('#permanentAddressLine1').val() || '',
        permanentAddressLine2: $('#permanentAddressLine2').val() || '',
        permanentPinCode: $('#permanentPinCode').val() || '',
        permanentTelephoneNumber: $('#permanentTelephoneNumber').val() || '',
        mobileNo: $('#mobileNo').val() || '',
        emailAddress: $('#emailAddress').val() || '',
        gender: $('#gender').val() || '',
        maritalStatus: $('#maritalStatus').val() || '',
        category: $('#category').val() || '',
        presentEmployer: $('#presentEmployer').val() || '',
        title: $('#title').val() || '',
        nationality: $('#nationality').val() || '',
        stateOfDomicile: $('#stateOfDomicile').val() || '',
        postAppliedFor: $('#postAppliedFor').val() || '',
        specificSubject: $('#specificSubject').val() || '',
        imageDoc:  uploadedFiles['imageDoc'] || '',
        ugcYear: $('#ugcYear').val() || '',
        ugcDoc:  uploadedFiles['ugcDoc'] || '',
        totalTeachingFrom: $('#totalTeachingFrom').val() || '',
        TotalTeachingTo: $('#TotalTeachingTo').val() || '',
        shortTermFrom: $('#shortTermFrom').val() || '',
        shortTermTo: $('#shortTermTo').val() || '',
        shortTermYears: $('#shortTermYears').val() || '',
        shortTermMonths: $('#shortTermMonths').val() || '',
        shortTermSupportingDocument: uploadedFiles['shortTermSupportingDocument'] || '',
        membershipFellowshipInstitutions: $('#membershipFellowshipInstitutions').val() || '',
        otherActivities: $('#otherActivities').val() || '',
        otherRelevantInformation: $('#otherRelevantInformation').val() || '',
        breakAcademicCareer: $('#breakAcademicCareer').val() || '',
        studiesCollegeUniversity: $('#studiesCollegeUniversity').val() || '',
        courtOfLaw: $('#courtOfLaw').val() || '',
        submitResignation: $('#submitResignation').val() || '',
        addressesFirstReferences: $('#addressesFirstReferences').val() || '',
        addressesSecondReferences: $('#addressesSecondReferences').val() || '',
        fulfillEssentialAdvertisement: $('#fulfillEssentialAdvertisement').val() || '',
        place: $('#place').val() || '',
        date: $('#date').val() || '',
        attachDocument: uploadedFiles['attachDocument'] || '',
        finalSubmit:"YES"
  
            };
            formData.detailsEmploymentExperience = [];
    formData.reseachPublicationBook = [];
    formData.reseachContributed =[];
    formData.projectUnderTaken=[];
    formData.researchPublished=[];
    formData.projectPapers=[];

    var uniqueFieldIdCounter = 1;

    // Loop through each row in the table
    $('#form_table tr[id^="tr_row"]').each(function() {
        var rowId = $(this).attr('id').replace('tr_row', '');

        var employmentData = {
            applicationNo: $('#applicationnogenearted').text(),
            nameOfEmployer: $('#nameOfEmployer' + rowId).val() || '',
            statusInstitute: $('#statusInstitute' + rowId).val() || '',
            postHeldDesignation: $('#postHeldDesignation' + rowId).val() || '',
            periodEmploymentFrom: ($('#periodEmploymentFromMonth' + rowId).val() || '') + ' ' + ($('#periodEmploymentFromYear' + rowId).val() || ''),
            periodEmploymentTo: ($('#periodEmploymentToMonth' + rowId).val() || '') + ' ' + ($('#periodEmploymentToYear' + rowId).val() || ''),
            basicSalaryLastDrawnPayScaleAndGradePay: $('#basicSalaryLastDrawnPayScaleAndGradePay' + rowId).val() || '',
            natureOfDuties: $('#natureOfDuties' + rowId).val() || '',
          filePaths: uploadedFiles[`filePaths${rowId}`] || '',
          fieldId: uniqueFieldIdCounter 
        };

        formData.detailsEmploymentExperience.push(employmentData);
        uniqueFieldIdCounter++;
    });

    var uniqueFieldIdCounter = 1;
    $('#form_row_table tr[id^="trs_row"]').each(function() {
        var rowId = $(this).attr('id').replace('trs_row', '');

    var publicationData = {
        applicationNo: $('#applicationnogenearted').text(),
        booksTitleOfTheBook: $('#booksTitleOfTheBook' + rowId).val() || '',
        booksSoleAuthorCoauthor: $('#booksSoleAuthorCoauthor' + rowId).val() || '',
        booksNamePublisherCountry: $('#booksNamePublisherCountry' + rowId).val() || '',
        booksMonthYearPublication: ($('#booksMonthYearPublication' + rowId).val() || '') + ' ' + ($('#booksMonthYearPublication' + rowId).val() || ''),
        booksIsbnIssnNo: $('#booksIsbnIssnNo' + rowId).val() || '',
    booksSupportingDocuments: uploadedFiles['booksSupportingDocuments' + rowId] || '',
    fieldId: uniqueFieldIdCounter 
    };
    uniqueFieldIdCounter++;
    formData.reseachPublicationBook.push(publicationData);
});

var uniqueFieldIdCounter = 1;
$('#form_row_chapter_table tr[id^="tr_chapter_row"]').each(function() {
    var rowId = $(this).attr('id').replace('tr_chapter_row', '');

    var contributedData = {
        applicationNo: $('#applicationnogenearted').text(),
        contributedTitleOfChapter: $('#contributedTitleOfChapter' + rowId).val() || '',
        ContributedTitleOfBook: $('#ContributedTitleOfBook' + rowId).val() || '',
        ContributedauthorType: $('#ContributedauthorType' + rowId).val() || '',
        ContributedPublisher: $('#ContributedPublisher' + rowId).val() || '',
        contrubutedMonth: $('#contrubutedMonth' + rowId).val() || '',
        contrubutedYear: $('#contrubutedYear' + rowId).val() || '',
        ContrubutedIsbnIssnNo: $('#ContrubutedIsbnIssnNo' + rowId).val() || '',
        contributedSupportingDocuments:uploadedFiles['contributedSupportingDocuments'+ rowId] || '',
        fieldId: uniqueFieldIdCounter 
    };
    uniqueFieldIdCounter++;
    formData.reseachContributed.push(contributedData);
});
var uniqueFieldIdCounter = 1;
// Loop through each row in the research project table
$('#form_row_table_project tr[id^="tr_project_row"]').each(function() {
    var rowId = $(this).attr('id').replace('tr_project_row', '');

    var projectData = {
        applicationNo: $('#applicationnogenearted').text(),
        researchTitleSubject: $('#researchTitleSubject' + rowId).val() || '',
        researchProjectType: $('#researchProjectType' + rowId).val() || '',
        researchDateCommencement: $('#researchDateCommencement' + rowId).val() || '',
        researchDateCompletion: $('#researchDateCompletion' + rowId).val() || '',
        researchTotalGrantsReceivedRs: $('#researchTotalGrantsReceivedRs' + rowId).val() || '',
        researchNameSponsoringAgency: $('#researchNameSponsoringAgency' + rowId).val() || '',
        researchOutcomeOutputsSentGovt: $('#researchOutcomeOutputsSentGovt' + rowId).val() || '',
        researchFinalReportPublished: $('#researchFinalReportPublished' + rowId).val() || '',
        researchSupportingDocuments:uploadedFiles['researchSupportingDocuments'+ rowId] || '',
        fieldId: uniqueFieldIdCounter 
    };
    uniqueFieldIdCounter++;
    formData.projectUnderTaken.push(projectData);
});
var uniqueFieldIdCounter = 1;
$('#form_row_table_articles tr[id^="tr_article_row"]').each(function() {
    var rowId = $(this).attr('id').replace('tr_article_row', '');

    var publicationDataResearch = {
        applicationNo: $('#applicationnogenearted').text(),
        titleOfArticle: $('#titleOfArticle' + rowId).val() || '',
        nameOfJournal: $('#nameOfJournal' + rowId).val() || '',
        authorType: $('#authorType' + rowId).val() || '',
        publicationDetails: $('#publicationDetails' + rowId).val() || '',
        referredStatus: $('#referredStatus' + rowId).val() || '',
        researchIsbnIssnNo: $('#researchIsbnIssnNo' + rowId).val() || '',
        level: $('#level' + rowId).val() || '',
        impactFactor: $('#impactFactor' + rowId).val() || '',
        researchSupportingDocuments: uploadedFiles['researchSupportingDocuments'+ rowId] || '',
        fieldId: uniqueFieldIdCounter 
    };
    uniqueFieldIdCounter++;
    formData.researchPublished.push(publicationDataResearch);
});
var uniqueFieldIdCounter = 1;
$('#form_row_table_paper_presented tr[id^="tr_paper_presented_row"]').each(function() {
    var rowId = $(this).attr('id').replace('tr_paper_presented_row', '');

    var publicationprojectPapers = {
        applicationNo: $('#applicationnogenearted').text(),
        titleOfPaper: $('#titleOfPaper' + rowId).val() || '',
        subjectOfConference: $('#subjectOfConference' + rowId).val() || '',
        organizingInstitutionCityCountry: $('#organizingInstitutionCityCountry' + rowId).val() || '',
        durationFrom: $('#durationFrom' + rowId).val() || '',
        durationTo: $('#durationTo' + rowId).val() || '',
        proceedingsPublished: $('#proceedingsPublished' + rowId).val() || '',
        supportingDocuments:uploadedFiles['supportingDocuments'+ rowId] || '',
        fieldId: uniqueFieldIdCounter 
    };
    uniqueFieldIdCounter++;
    formData.projectPapers.push(publicationprojectPapers);
});

    // formData.projectPapers = [
    //     {
    //         applicationNo: $('#applicationnogenearted').text(),
    //         titleOfPaper: $('#titleOfPaper').val() || '',
    //         subjectOfConference: $('#subjectOfConference').val() || '',
    //         organizingInstitutionCityCountry: $('#organizingInstitutionCityCountry').val() || '',
    //         durationFrom: $('#durationFrom').val() || '',
    //         durationTo: $('#durationTo').val() || '',
    //         proceedingsPublished: $('#proceedingsPublished').val() || '',
    //         supportingDocuments: $('#supportingDocuments').val() || ''
    //     }
    // ];

    $.ajax({
        url: 'https://ansiss.res.in/api/v1/stage-two/insert-data',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(formData), 
        success: function(response) {
        Swal.fire({
            icon: 'success',
            title: 'Form is Finally Submitted successfully',
            text: 'Please Wait For Interview Call Our team will contact You Soon!'
        }).then((result) => {
                if (result.isConfirmed) {
                    generatePDF(formData);
                }
            })
    
        console.log('Form submitted successfully:', response);
    },
    error: function(xhr, status, error) {
     
        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: 'Error submitting form: ' + error
        });
        console.error('Error submitting form:', error);
    }
    });
});

     function generatePDF(formData) {
    const { jsPDF } = window.jspdf;
    const doc = new jsPDF();

    // Custom styles configuration
    const titleFont = "helvetica";
    const titleFontSize = 14;
    const titleColor = [0, 0, 0]; // Black

    const fieldFont = "helvetica";
    const fieldFontSize = 10;
    const fieldLabelColor = [0, 0, 0]; // Black
    const fieldValueColor = [50, 50, 50]; // Dark gray

    const lineHeight = 7;
    const pageWidth = doc.internal.pageSize.getWidth();
    const borderMargin = 15;

    // Set up title styles
    doc.setFont(titleFont, "bold");
    doc.setFontSize(titleFontSize);
    doc.setTextColor(...titleColor);

    // Title - Centered heading
    const titleText = "A.N. Sinha Institue Of Social Studies,Patna(Application Receipt)";
    const titleX = (pageWidth - doc.getTextWidth(titleText)) / 2;
    doc.text(titleText, titleX, 10); // Y position set to 10 for top margin

    // Set up field styles
    doc.setFont(fieldFont, "bold");
    doc.setFontSize(fieldFontSize);
    doc.setTextColor(...fieldLabelColor);

    // Define the starting y position for the content
    let y = 30; // Adjusted to start content below the title and subtitle

    // Function to add two fields with a full-width border and a vertical separator
    function addTwoFieldsOnSameLineWithBorder(key1, value1, key2, value2, x, y) {
        const totalWidth = pageWidth - borderMargin * 2;
        const separatorX = x + totalWidth / 2;

        // Draw the full-width border
        doc.setDrawColor(0, 0, 0); // Black border
        doc.setLineWidth(0.5); // Border thickness
        doc.rect(x - 2, y - lineHeight, totalWidth, lineHeight + 4);

        // Draw the vertical separator
        doc.line(separatorX, y - lineHeight, separatorX, y + 4);

        // Set text color and write the content
        doc.setTextColor(...fieldLabelColor);
        doc.text(`${key1}:`, x + 2, y);
        doc.setTextColor(...fieldValueColor);
        doc.text(value1, x + 2 + doc.getTextWidth(`${key1}: `), y);

        doc.setTextColor(...fieldLabelColor);
        doc.text(`${key2}:`, separatorX + 2, y);
        doc.setTextColor(...fieldValueColor);
        doc.text(value2, separatorX + 2 + doc.getTextWidth(`${key2}: `), y);
    }

    // Function to add a single field with a full-width border
    function addContentWithBorder(key, value, x, y, valueXOffset = 50) {
        const totalWidth = pageWidth - borderMargin * 2;

        // Draw the full-width border
        doc.setDrawColor(0, 0, 0); // Black border
        doc.setLineWidth(0.5); // Border thickness
        doc.rect(x - 2, y - lineHeight, totalWidth, lineHeight + 4);

        // Set text color and write the content
        doc.setTextColor(...fieldLabelColor);
        doc.text(`${key}:`, x + 2, y);
        doc.setTextColor(...fieldValueColor);
        doc.text(value, x + valueXOffset, y);
    }

    doc.setFont(fieldFont, "bold");
    doc.setFontSize(fieldFontSize);
    doc.setTextColor(...fieldLabelColor);
   // Personal Details
   addContentWithBorder("Application Number ", formData.applicationNo, borderMargin, y);
   y += lineHeight + 5;
   addContentWithBorder("Full Name", formData.fullName, borderMargin, y);
    y += lineHeight + 5;
    addContentWithBorder("post AppliedFor", formData.postAppliedFor, borderMargin, y);
    y += lineHeight + 5;
    addTwoFieldsOnSameLineWithBorder("Date of Birth", formData.dateOfBirth, "Gender", formData.gender, borderMargin, y);
    y += lineHeight + 5;
    addTwoFieldsOnSameLineWithBorder("Father/Spouse Name", formData.fatherOrSpouseName, "Marital Status", formData.maritalStatus, borderMargin, y);
    y += lineHeight + 5;
    addContentWithBorder("Mailing Address Line 1", formData.mailingAddressLine1, borderMargin, y);
    y += lineHeight + 5;
    addContentWithBorder("Mailing Address Line 2", formData.mailingAddressLine2, borderMargin, y);
    y += lineHeight + 5;
    addTwoFieldsOnSameLineWithBorder("Mailing Pin Code", formData.mailingPinCode, "Mailing Telephone Number", formData.mailingTelephoneNumber, borderMargin, y);
    y += lineHeight + 5;
    addContentWithBorder("Permanent Address Line 1", formData.permanentAddressLine1, borderMargin, y);
    y += lineHeight + 5;
    addContentWithBorder("Permanent Address Line 2", formData.permanentAddressLine2, borderMargin, y);
    y += lineHeight + 5;
    addTwoFieldsOnSameLineWithBorder("Permanent Pin Code", formData.permanentPinCode, "Permanent Telephone Number", formData.permanentTelephoneNumber, borderMargin, y);
    y += lineHeight + 5;
    addTwoFieldsOnSameLineWithBorder("Mobile No", formData.mobileNo, "Email Address", formData.emailAddress, borderMargin, y);
    y += lineHeight + 5;
    addTwoFieldsOnSameLineWithBorder("Category", formData.category, "Nationality", formData.nationality, borderMargin, y);
    y += lineHeight + 5;
    addTwoFieldsOnSameLineWithBorder("Present Employer", formData.presentEmployer, "Title", formData.title, borderMargin, y);
    y += lineHeight + 5;
    addContentWithBorder("State Of Domicile", formData.stateOfDomicile, borderMargin, y);
    y += lineHeight + 5;
    addContentWithBorder("Specific Subject", formData.specificSubject, borderMargin, y);
    y += lineHeight + 5;

    if (y >= 250) {
        doc.addPage();
        y = 20;
    } else {
        y += lineHeight * 2;
    }
    // Post Graduation Section
    doc.setFontSize(titleFontSize);
    doc.setTextColor(...titleColor);
    doc.text("Summary of Teaching Experience/Performance UG", 10, y);
    y += lineHeight + 5;

    doc.setFont(fieldFont, "bold");
    doc.setFontSize(fieldFontSize);
    doc.setTextColor(...fieldLabelColor);

    addTwoFieldsOnSameLineWithBorder("UGC Year", formData.ugcYear, "UGC Document", formData.ugcDoc, borderMargin, y);
    y += lineHeight + 5;
    addTwoFieldsOnSameLineWithBorder("Total Teaching From", formData.totalTeachingFrom, "Total Teaching To", formData.TotalTeachingTo, borderMargin, y);
    y += lineHeight + 5;
    addTwoFieldsOnSameLineWithBorder("Short Term From", formData.shortTermFrom, "Short Term To", formData.shortTermTo, borderMargin, y);
    y += lineHeight + 5;
    addTwoFieldsOnSameLineWithBorder("Short Term Years", formData.shortTermYears, "Short Term Months", formData.shortTermMonths, borderMargin, y);
    y += lineHeight + 5;
    addContentWithBorder("Membership/Fellowship Institutions", formData.membershipFellowshipInstitutions, borderMargin, y);
    y += lineHeight + 5;
    addContentWithBorder("Other Activities", formData.otherActivities, borderMargin, y);
    y += lineHeight + 5;
    addContentWithBorder("Other Relevant Information", formData.otherRelevantInformation, borderMargin, y);
    y += lineHeight + 5;
    addContentWithBorder("Break Academic Career", formData.breakAcademicCareer, borderMargin, y);
    y += lineHeight + 5;
    addContentWithBorder("Studies College/University", formData.studiesCollegeUniversity, borderMargin, y);
    y += lineHeight + 5;
    addContentWithBorder("Court Of Law", formData.courtOfLaw, borderMargin, y);
    y += lineHeight + 5;

    if (y >= 250) {
        doc.addPage();
        y = 20;
    } else {
        y += lineHeight * 2;
    }
    addContentWithBorder("Submit Resignation", formData.submitResignation, borderMargin, y);
    y += lineHeight + 5;
    addContentWithBorder("Addresses First References", formData.addressesFirstReferences, borderMargin, y);
    y += lineHeight + 5;
    addContentWithBorder("Addresses Second References", formData.addressesSecondReferences, borderMargin, y);
    y += lineHeight + 5;
    addContentWithBorder("Fulfill Essential Advertisement", formData.fulfillEssentialAdvertisement, borderMargin, y);
    y += lineHeight + 5;
    addTwoFieldsOnSameLineWithBorder("Place", formData.place, "Date", formData.date, borderMargin, y);
    
    if (y >= 250) {
        doc.addPage();
        y = 20;
    } else {
        y += lineHeight * 2;
    }
    // Post Graduation Section
    doc.setFontSize(titleFontSize);
    doc.setTextColor(...titleColor);
    doc.text("PhD  Details", 10, y);
    y += lineHeight + 5;

    doc.setFont(fieldFont, "bold");
    doc.setFontSize(fieldFontSize);
    doc.setTextColor(...fieldLabelColor);

    addContentWithBorder("PhD Board/College/University", formData.phdBoardCollegeUniversity, borderMargin, y);
    y += lineHeight + 5;
    addTwoFieldsOnSameLineWithBorder("PhD Percentage Of Marks", formData.phdPercentageOfMarks, "PhD Year Of Passing/Award", formData.phdYearOfPassingAward, borderMargin, y);
    y += lineHeight + 5;
    addContentWithBorder("PhD Subjects", formData.phdSubjects,   borderMargin, y);
    y += lineHeight + 5;
    if (y >= 250) {
        doc.addPage();
        y = 20;
    } else {
        y += lineHeight * 2;
    }

    // Post Graduation Section
    doc.setFontSize(titleFontSize);
    doc.setTextColor(...titleColor);
    doc.text("Post Graduation Details", 10, y);
    y += lineHeight + 5;

    doc.setFont(fieldFont, "bold");
    doc.setFontSize(fieldFontSize);
    doc.setTextColor(...fieldLabelColor);

    addTwoFieldsOnSameLineWithBorder("Post Graduation Board/College/University", formData.postGraduationBoardCollegeUniversity, "Post Graduation Percentage Of Marks", formData.postGraduationPercentageOfMarks, borderMargin, y);
    y += lineHeight + 5;
    addTwoFieldsOnSameLineWithBorder("Post Graduation Subjects", formData.postGraduationSubjects, "Post Graduation Year Of Passing/Award", formData.postGraduationYearOfPassingAward, borderMargin, y);
    y += lineHeight + 5;

    if (y >= 250) {
        doc.addPage();
        y = 20;
    } else {
        y += lineHeight * 2;
    }
    // Graduation Section
    doc.setFontSize(titleFontSize);
    doc.setTextColor(...titleColor);
    doc.text("Graduation Details", 10, y);
    y += lineHeight + 5;

    doc.setFont(fieldFont, "bold");
    doc.setFontSize(fieldFontSize);
    doc.setTextColor(...fieldLabelColor);

    addTwoFieldsOnSameLineWithBorder("Graduation Board/College/University", formData.graduationBoardCollegeUniversity, "Graduation Percentage Of Marks", formData.graduationPercentageOfMarks, borderMargin, y);
    y += lineHeight + 5;
    addTwoFieldsOnSameLineWithBorder("Graduation Subjects", formData.graduationSubjects, "Graduation Year Of Passing/Award", formData.graduationYearOfPassingAward, borderMargin, y);
    y += lineHeight + 5;

    // XII Section
    doc.setFontSize(titleFontSize);
    doc.setTextColor(...titleColor);
    doc.text("12th Details", 10, y);
    y += lineHeight + 5;
    if (y >= 250) {
        doc.addPage();
        y = 20;
    } else {
        y += lineHeight * 2;
    }
    doc.setFont(fieldFont, "bold");
    doc.setFontSize(fieldFontSize);
    doc.setTextColor(...fieldLabelColor);

    addTwoFieldsOnSameLineWithBorder("XII Board/College/University", formData.xiiBoardCollegeUniversity, "XII Percentage Of Marks", formData.xiiPercentageOfMarks, borderMargin, y);
    y += lineHeight + 5;
    addTwoFieldsOnSameLineWithBorder("XII Subjects", formData.xiiSubjects, "XII Year Of Passing/Award", formData.xiiYearOfPassingAward, borderMargin, y);
    y += lineHeight + 5;

    if (y >= 250) {
        doc.addPage();
        y = 20;
    } else {
        y += lineHeight * 2;
    }
    // X Section
    doc.setFontSize(titleFontSize);
    doc.setTextColor(...titleColor);
    doc.text("10th Details", 10, y);
    y += lineHeight + 5;

    doc.setFont(fieldFont, "bold");
    doc.setFontSize(fieldFontSize);
    doc.setTextColor(...fieldLabelColor);

    addTwoFieldsOnSameLineWithBorder("X Board/College/University", formData.xBoardCollegeUniversity, "X Percentage Of Marks", formData.xPercentageOfMarks, borderMargin, y);
    y += lineHeight + 5;
    addTwoFieldsOnSameLineWithBorder("X Subjects", formData.xSubjects, "X Year Of Passing/Award", formData.xYearOfPassingAward, borderMargin, y);
    y += lineHeight + 5;
    if (y >= 250) {
        doc.addPage();
        y = 20;
    } else {
        y += lineHeight * 2;
    }

    // Others Section
    doc.setFontSize(titleFontSize);
    doc.setTextColor(...titleColor);
    doc.text("Other Qualification Details", 10, y);
    y += lineHeight + 5;

    doc.setFont(fieldFont, "bold");
    doc.setFontSize(fieldFontSize);
    doc.setTextColor(...fieldLabelColor);

    addTwoFieldsOnSameLineWithBorder("Other Board/College/University", formData.othersBoardCollegeUniversity, "Other Percentage Of Marks", formData.othersPercentageOfMarks, borderMargin, y);
    y += lineHeight + 5;
    addTwoFieldsOnSameLineWithBorder("Other Subjects", formData.othersSubjects, "Other Year Of Passing/Award", formData.othersYearOfPassingAward, borderMargin, y);
    y += lineHeight + 5;
    if (y >= 250) {
        doc.addPage();
        y = 20;
    } else {
        y += lineHeight * 2;
    }

    // New Section - Research Experience

  doc.setFontSize(titleFontSize);
    doc.setTextColor(...titleColor);
    doc.text("Research Experience", borderMargin, y);
    y += lineHeight + 5;

    doc.setFont(fieldFont, "bold");
    doc.setFontSize(fieldFontSize);
    doc.setTextColor(...fieldLabelColor);

    // Additional Information
    addContentWithBorder("PhD Awarded Year", formData.phdAwarded, borderMargin, y);
    y += lineHeight + 5;
    addContentWithBorder("PhD Thesis Awarded", formData.phdThesisAwarded, borderMargin, y);
    y += lineHeight + 5;
    addContentWithBorder("UGC CSIR", formData.ugcCSIR, borderMargin, y);
    y += lineHeight + 5;
    addContentWithBorder("Student Related Co-Curricular", formData.studentRelatedCoCurricular, borderMargin, y);
    y += lineHeight + 5;
    addContentWithBorder("Contribution to Corporate", formData.contributionToCorporate, borderMargin, y);
    if (y >= 250) {
        doc.addPage();
        y = 20;
    } else {
        y += lineHeight * 2;
    }

 // Employment Experience Section
 doc.setFontSize(titleFontSize);
    doc.setTextColor(...titleColor);
    doc.text("Employment Experience", borderMargin, y);
    y += lineHeight + 5;

    doc.setFont(fieldFont, "bold");
    doc.setFontSize(fieldFontSize);
    doc.setTextColor(...fieldLabelColor);

    // Loop through and add each employment experience
    formData.detailsEmploymentExperience.forEach((employment) => {
        addContentWithBorder("Name Of Employer", employment.nameOfEmployer, borderMargin, y);
        y += lineHeight + 5;
        addContentWithBorder("Status Institute", employment.statusInstitute, borderMargin, y);
        y += lineHeight + 5;
        addContentWithBorder("Post Held Designation", employment.postHeldDesignation, borderMargin, y);
        y += lineHeight + 5;
        addContentWithBorder("Period Employment From", employment.periodEmploymentFrom, borderMargin, y);
        y += lineHeight + 5;
        addContentWithBorder("Period Employment To", employment.periodEmploymentTo, borderMargin, y);
        y += lineHeight + 5;
        addContentWithBorder("Basic Salary Last Drawn", employment.basicSalaryLastDrawnPayScaleAndGradePay, borderMargin, y);
        y += lineHeight + 5;
        addContentWithBorder("Nature Of Duties", employment.natureOfDuties, borderMargin, y);
        y += lineHeight + 5;
      
    });
    if (y >= 250) {
        doc.addPage();
        y = 20;
    } else {
        y += lineHeight * 2;
    }

    // Project Undertaken Section
    doc.setFontSize(titleFontSize);
    doc.setTextColor(...titleColor);
    doc.text("Research Projects Undertaken", borderMargin, y);
    y += lineHeight + 5;

    doc.setFont(fieldFont, "bold");
    doc.setFontSize(fieldFontSize);
    doc.setTextColor(...fieldLabelColor);

    // Loop through and add each project undertaken
    formData.projectUnderTaken.forEach((project) => {
        addContentWithBorder("Research Title/Subject", project.researchTitleSubject, borderMargin, y);
        y += lineHeight + 5;
        addContentWithBorder("Research Project Type", project.researchProjectType, borderMargin, y);
        y += lineHeight + 5;
        addContentWithBorder("Research Date Commencement", project.researchDateCommencement, borderMargin, y);
        y += lineHeight + 5;
        addContentWithBorder("Research Date Completion", project.researchDateCompletion, borderMargin, y);
        y += lineHeight + 5;
        addContentWithBorder("Research Total Grants Received", project.researchTotalGrantsReceivedRs, borderMargin, y);
        y += lineHeight + 5;
        addContentWithBorder("Research Name Sponsoring Agency", project.researchNameSponsoringAgency, borderMargin, y);
        y += lineHeight + 5;
        addContentWithBorder("Research Outcome Outputs Sent Govt", project.researchOutcomeOutputsSentGovt, borderMargin, y);
        y += lineHeight + 5;
        addContentWithBorder("Research Final Report Published", project.researchFinalReportPublished, borderMargin, y);
        y += lineHeight + 5;

    });
    if (y >= 250) {
        doc.addPage();
        y = 20;
    } else {
        y += lineHeight * 2;
    }
        // Project Undertaken Section
        doc.setFontSize(titleFontSize);
    doc.setTextColor(...titleColor);
    doc.text(" Chapters contributed in edited books", borderMargin, y);
    y += lineHeight + 5;

    doc.setFont(fieldFont, "bold");
    doc.setFontSize(fieldFontSize);
    doc.setTextColor(...fieldLabelColor);

    formData.reseachContributed.forEach((contribution) => {
    addContentWithBorder("Contributed Title Of Chapter", contribution.contributedTitleOfChapter, borderMargin, y);
    y += lineHeight + 5;
    addContentWithBorder("Contributed Title Of Book", contribution.ContributedTitleOfBook, borderMargin, y);
    y += lineHeight + 5;
    addContentWithBorder("Contributed Author Type", contribution.ContributedauthorType, borderMargin, y);
    y += lineHeight + 5;
    addContentWithBorder("Contributed Publisher", contribution.ContributedPublisher, borderMargin, y);
    y += lineHeight + 5;
    addContentWithBorder("Contributed Month", contribution.contrubutedMonth, borderMargin, y);
    y += lineHeight + 5;
    addContentWithBorder("Contributed Year", contribution.contrubutedYear, borderMargin, y);
    y += lineHeight + 5;
    addContentWithBorder("Contributed ISBN/ISSN No", contribution.ContrubutedIsbnIssnNo, borderMargin, y);
    y += lineHeight + 5;

});
if (y >= 250) {
        doc.addPage();
        y = 20;
    } else {
        y += lineHeight * 2;
    }
      // Project Undertaken Section
      doc.setFontSize(titleFontSize);
    doc.setTextColor(...titleColor);
    doc.text("Research/Articles/Papers published in Journals", borderMargin, y);
    y += lineHeight + 5;

    doc.setFont(fieldFont, "bold");
    doc.setFontSize(fieldFontSize);
    doc.setTextColor(...fieldLabelColor);

// Loop through and add each published research
formData.researchPublished.forEach((publication) => {
    addContentWithBorder("Title Of Article", publication.titleOfArticle, borderMargin, y);
    y += lineHeight + 5;
    addContentWithBorder("Name Of Journal", publication.nameOfJournal, borderMargin, y);
    y += lineHeight + 5;
    addContentWithBorder("Author Type", publication.authorType, borderMargin, y);
    y += lineHeight + 5;
    addContentWithBorder("Publication Details", publication.publicationDetails, borderMargin, y);
    y += lineHeight + 5;
    addContentWithBorder("Referred Status", publication.referredStatus, borderMargin, y);
    y += lineHeight + 5;
    addContentWithBorder("Research ISBN/ISSN No", publication.researchIsbnIssnNo, borderMargin, y);
    y += lineHeight + 5;
    addContentWithBorder("Level", publication.level, borderMargin, y);
    y += lineHeight + 5;
    addContentWithBorder("Impact Factor", publication.impactFactor, borderMargin, y);
    y += lineHeight + 5;
 
});
if (y >= 250) {
        doc.addPage();
        y = 20;
    } else {
        y += lineHeight * 2;
    }

      // Project Undertaken Section
      doc.setFontSize(titleFontSize);
    doc.setTextColor(...titleColor);
    doc.text("Papers presented in Regional", borderMargin, y);
    y += lineHeight + 5;

    doc.setFont(fieldFont, "bold");
    doc.setFontSize(fieldFontSize);
    doc.setTextColor(...fieldLabelColor);
// Loop through and add each project paper
formData.projectPapers.forEach((paper) => {
    addContentWithBorder("Title Of Paper", paper.titleOfPaper, borderMargin, y);
    y += lineHeight + 5;
    addContentWithBorder("Subject Of Conference", paper.subjectOfConference, borderMargin, y);
    y += lineHeight + 5;
    addContentWithBorder("Organizing Institution City/Country", paper.organizingInstitutionCityCountry, borderMargin, y);
    y += lineHeight + 5;
    addContentWithBorder("Duration From", paper.durationFrom, borderMargin, y);
    y += lineHeight + 5;
    addContentWithBorder("Duration To", paper.durationTo, borderMargin, y);
    y += lineHeight + 5;
    addContentWithBorder("Proceedings Published", paper.proceedingsPublished, borderMargin, y);
    y += lineHeight + 5;

});
if (y >= 250) {
        doc.addPage();
        y = 20;
    } else {
        y += lineHeight * 2;
    }
  
      // Project Undertaken Section
      doc.setFontSize(titleFontSize);
    doc.setTextColor(...titleColor);
    doc.text("CATEGORY III :  RESEARCH AND ACADEMIC CONTRIBUTION", borderMargin, y);
    y += lineHeight + 5;

    doc.setFont(fieldFont, "bold");
    doc.setFontSize(fieldFontSize);
    doc.setTextColor(...fieldLabelColor);
// Loop through and add each publication in book format
formData.reseachPublicationBook.forEach((book) => {
    addContentWithBorder("Books Title Of The Book", book.booksTitleOfTheBook, borderMargin, y);
    y += lineHeight + 5;
    addContentWithBorder("Books Sole Author/Coauthor", book.booksSoleAuthorCoauthor, borderMargin, y);
    y += lineHeight + 5;
    addContentWithBorder("Books Name Publisher Country", book.booksNamePublisherCountry, borderMargin, y);
    y += lineHeight + 5;
    addContentWithBorder("Books Month Year Publication", book.booksMonthYearPublication, borderMargin, y);
    y += lineHeight + 5;
    addContentWithBorder("Books ISBN/ISSN No", book.booksIsbnIssnNo, borderMargin, y);
    y += lineHeight + 5;

});
if (y >= 250) {
        doc.addPage();
        y = 20;
    } else {
        y += lineHeight * 2;
    }

    // Save the PDF
    doc.save("formData.pdf");
}
const formData = {
  
};
})

    </script>
    </main>
    <footer class="footer-area black-bg-color">
    <div class="container">
      <div class="row">
        <div class="col-lg-3 col-sm-6">
          <div class="single-footer-widget bg-f9f5f1">
            <a href="/" class="logo">
              <img src="/img/LOGO1.png" alt="Image" style="background-color: #DAA520; border-radius: 20px;">
            </a>
                 
                    <ul class="social-icon">
                        <li>
                          <span>Follow us:</span>
                        </li>
                        <li>
                          <a href="#" class="social-button social-button--facebook" aria-label="Facebook">
                            <i class="fab fa-facebook-f"></i>
                          </a>
                        </li>
                        <li>
                          <a href="#" class="social-button social-button--linkedin" aria-label="LinkedIn">
                            <i class="fab fa-linkedin-in"></i>
                          </a>
                        </li>
                        <li>
                          <a href="#" class="social-button social-button--snapchat" aria-label="Snapchat">
                            <i class="fab fa-snapchat-ghost"></i>
                          </a>
                        </li>
                        <li>
                          <a href="#" class="social-button social-button--twitter" aria-label="Twitter">
                            <i class="fab fa-twitter"></i>
                        </a>
                        </li>
                      </ul>
                </div>
            </div>

            <div class="col-lg-3 col-sm-6">
                <div class="single-footer-widget">
                    <h3>Useful link</h3>
<div class="menu-useful-links-container"><ul class="import-link"><li id="menu-item-7249" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-7249"><a href="#">Infrastructure Faclities</a></li>

<li id="menu-item-7250" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-7250"><a href="#">ANSISS Ph.D. Alumni</a></li>
<li id="menu-item-7251" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-7251"><a href="#">Right To Information Act</a></li>
<li id="menu-item-7252" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-7252"><a href="#">Internal Complaints Committee</a></li>
<li id="menu-item-7274" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-7274"><a href="#">Tender Notices</a></li>
</ul></div>					
                </div>
            </div>

            <div class="col-lg-3 col-sm-6">
                <div class="single-footer-widget">
                    <h3>Quick Links</h3>
<div class="menu-quick-links-container"><ul class="import-link"><li id="menu-item-7036" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-7036"><a href="#">Current Research</a></li>
<li id="menu-item-7039" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-7039"><a href="#">Seminars and Training</a></li>
<li id="menu-item-10887" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-10887"><a href="#">Post-Doctoral Research</a></li>
<li id="menu-item-7090" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-7090"><a href="#">SRTT Programmes</a></li>
<li id="menu-item-7093" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-7093"><a href="#">Databank</a></li>
<li id="menu-item-7094" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-7094"><a href="#">Certificate Courses</a></li>
<li id="menu-item-14008" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-14008"><a href="#">Grant and Internship</a></li>
</ul></div>				
                </div>
            </div>

            <div class="col-lg-3 col-sm-6">
                <div class="single-footer-widget">
                    <h3>Inquiries</h3>

                    <ul class="address">
                        <li class="location">
                            <i class="fa fa-map-marker" aria-hidden="true"></i>
                            <span>Institute for Social  Studies
                        
                            <br> North West Gandhi Maidan
                            <br>  Patna - 800 001  </span> 
                            <span>
                                <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3597.4965245532367!2d85.13971057323835!3d25.6216413143824!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x39ed5851297fd585%3A0xa5c23a4b14d78ca9!2sA%20N%20Sinha%20Institute%20of%20Social%20Studies!5e0!3m2!1sen!2sin!4v1722628045557!5m2!1sen!2sin" width="250" height="50" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>

                            </span>
                            
                        </li>
                        <li>
                            <i class="fa fa-envelope" aria-hidden="true"></i>
                            <span>Email</span>
                            <a href="mailto:<EMAIL>"><EMAIL></a>
                        </li>
                        <li>
                            <i class="fa fa-phone" aria-hidden="true"></i>
                            <span>Phone</span>
                            <a href="tel:+91-080-23215468">+0612-2219395, Fax No.: 0612-2219226</a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</footer>

<div class="copy-right-area">
    <div class="container">
        <div class="row">
            <div class="col-md-6 col-lg-6 col-sm-6 col-12">
                <p>
                    Copyright © 2024 ANSISS. Design &amp; Developed By 
                    <a href="https://codebuckets.in/" target="_blank">Codebucket Solutions PVT LTD</a>
                </p>
            </div>
            <div class="col-md-6 col-lg-6 col-sm-6 col-12">
                <h2 style="color:#fff; font-size:16px; text-align:right;">Total Visitors: 54262</h2>
            </div>
            
        </div>
        
    </div>
</div>

<script src="https://code.jquery.com/jquery-3.7.0.js"></script>
<script src="/js/bootstrap.bundle.min.js"></script>
<script src="/js/owl.carousel.min.js"></script>

<script type="text/javascript" src="//translate.google.com/translate_a/element.js?cb=googleTranslateElementInit"></script>

<script type="text/javascript">
function googleTranslateElementInit() {
new google.translate.TranslateElement({pageLanguage: 'en' , includedLanguages : 'en,hi'}, 'google_translate_element');
}
</script>

<script>
jQuery(document).ready(function($) {
var delay = 100; setTimeout(function() {
$('.elementor-tab-title').removeClass('elementor-active');
$('.elementor-tab-content').css('display', 'none'); }, delay);
});
</script>

<script>
$( document ).ready(function() {
$('.dropdown-toggle').append('<span class="caret"></span>');
$(".dropdown-menu li ").addClass("sub-dropdown");
$(".dropdown-menu li ul").addClass("sub-dropdown-menu");
$(".dropdown-menu li ul").removeClass("dropdown-menu");
$(".sub-dropdown-menu li").removeClass("sub-dropdown");
$(".sub-dropdown-menu li a").removeClass("dropdown-item");
});
</script>

<script>
document.addEventListener('DOMContentLoaded', function() {
  document.querySelector('.navbar-toggler').addEventListener('click', function() {
    var navbarNav = document.getElementById('bs-example-navbar-collapse-1');
    if (navbarNav.classList.contains('show')) {
      navbarNav.classList.remove('show');
    } else {
      navbarNav.classList.add('show');
    }
  });
});
</script>

</body>
</html>
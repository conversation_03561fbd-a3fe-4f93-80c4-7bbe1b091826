// Simple Single Page Application Router
class SPARouter {
    constructor() {
        this.routes = new Map();
        this.currentPage = '';
        this.init();
    }

    init() {
        // Handle browser back/forward buttons
        window.addEventListener('popstate', (e) => {
            this.loadPage(window.location.pathname, false);
        });

        // Handle initial page load
        document.addEventListener('DOMContentLoaded', () => {
            this.loadPage(window.location.pathname, false);
        });

        // Intercept all link clicks
        document.addEventListener('click', (e) => {
            const link = e.target.closest('a');
            if (link && this.isInternalLink(link.href)) {
                e.preventDefault();
                const path = new URL(link.href).pathname;
                this.navigate(path);
            }
        });
    }

    isInternalLink(href) {
        try {
            const url = new URL(href);
            return url.origin === window.location.origin;
        } catch {
            return false;
        }
    }

    addRoute(path, htmlFile) {
        this.routes.set(path, htmlFile);
    }

    navigate(path) {
        this.loadPage(path, true);
    }

    async loadPage(path, updateHistory = true) {
        // Normalize path
        if (path === '/' || path === '') {
            path = '/';
        }

        // Find the corresponding HTML file
        let htmlFile = this.routes.get(path);
        
        if (!htmlFile) {
            // Try to find a matching route
            for (let [route, file] of this.routes) {
                if (route === path) {
                    htmlFile = file;
                    break;
                }
            }
        }

        // Default to homepage if no route found
        if (!htmlFile) {
            htmlFile = 'homepage.html';
        }

        try {
            // Show loading indicator
            this.showLoading();

            // Fetch the HTML content
            const response = await fetch(htmlFile);
            if (!response.ok) {
                throw new Error(`Failed to load ${htmlFile}`);
            }

            const html = await response.text();
            
            // Parse the HTML and extract the body content
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');
            
            // Replace the current page content
            document.body.innerHTML = doc.body.innerHTML;
            
            // Update the page title
            if (doc.title) {
                document.title = doc.title;
            }

            // Update browser history
            if (updateHistory && path !== window.location.pathname) {
                history.pushState({ path }, '', path);
            }

            // Hide loading indicator
            this.hideLoading();

            // Reinitialize any scripts that need to run on the new page
            this.initPageScripts();

            this.currentPage = path;

        } catch (error) {
            console.error('Error loading page:', error);
            this.hideLoading();
            this.showError('Failed to load page. Please try again.');
        }
    }

    showLoading() {
        // Create or show loading indicator
        let loader = document.getElementById('spa-loader');
        if (!loader) {
            loader = document.createElement('div');
            loader.id = 'spa-loader';
            loader.innerHTML = `
                <div style="
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(255,255,255,0.8);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    z-index: 9999;
                ">
                    <div style="text-align: center;">
                        <div style="
                            border: 4px solid #f3f3f3;
                            border-top: 4px solid #3498db;
                            border-radius: 50%;
                            width: 40px;
                            height: 40px;
                            animation: spin 1s linear infinite;
                            margin: 0 auto 10px;
                        "></div>
                        <p>Loading...</p>
                    </div>
                </div>
                <style>
                    @keyframes spin {
                        0% { transform: rotate(0deg); }
                        100% { transform: rotate(360deg); }
                    }
                </style>
            `;
            document.body.appendChild(loader);
        }
        loader.style.display = 'block';
    }

    hideLoading() {
        const loader = document.getElementById('spa-loader');
        if (loader) {
            loader.style.display = 'none';
        }
    }

    showError(message) {
        alert(message); // Simple error handling - you can improve this
    }

    initPageScripts() {
        // Reinitialize any JavaScript that needs to run on page load
        // For example, carousel, dropdowns, etc.
        
        // Trigger custom event for other scripts to listen to
        window.dispatchEvent(new CustomEvent('pageLoaded', { 
            detail: { path: this.currentPage } 
        }));
    }
}

// Initialize the router
const router = new SPARouter();

// Add all your routes
router.addRoute('/', 'homepage.html');
router.addRoute('/about', 'about.html');
router.addRoute('/posting', 'posting.html');
router.addRoute('/qualification', 'qualification.html');
router.addRoute('/logout', 'logout.html');
router.addRoute('/bocmembers', 'bocmembers.html');
router.addRoute('/facilites', 'facilites.html');
router.addRoute('/computerlab', 'computerlab.html');
router.addRoute('/director-tenure', 'director-tenure.html');
router.addRoute('/facultymember', 'facultymember.html');
router.addRoute('/campus', 'campus.html');
router.addRoute('/ansissLibrary', 'ansissLibrary.html');
router.addRoute('/register', 'register.html');
router.addRoute('/carrier-view', 'carrier-view.html');
router.addRoute('/annualReport', 'annualReport.html');
router.addRoute('/workingPaper', 'workingPaper.html');
router.addRoute('/newsLetter', 'newsLetter.html');
router.addRoute('/Phd_Program', 'Phd_Program.html');
router.addRoute('/Phd_Scholor', 'Phd_Scholor.html');
router.addRoute('/e_Content', 'e_Content.html');
router.addRoute('/completed_project', 'completed_project.html');
router.addRoute('/OnGoingProject', 'OnGoingProject.html');
router.addRoute('/Workshop', 'Workshop.html');
router.addRoute('/Dialogue_series', 'Dialogue_series.html');
router.addRoute('/internal_seminar', 'internal_seminar.html');
router.addRoute('/adminlogin', 'admin/adminlogin.html');
router.addRoute('/adminform', 'admin/adminform.html');
router.addRoute('/adminform1', 'admin/adminform1.html');
router.addRoute('/admindashboard', 'admin/admindashboard.html');
router.addRoute('/politicalfaculty', 'politicalfaculty.html');
router.addRoute('/psychologyfaculty', 'psychologyfaculty.html');
router.addRoute('/sociologyfaculty', 'sociologyfaculty.html');
router.addRoute('/statisticsfaculty', 'statisticsfaculty.html');
router.addRoute('/cnvpsfaculty', 'cnvpsfaculty.html');
router.addRoute('/geographyfaculty', 'geographyfaculty.html');
router.addRoute('/gallary', 'gallary.html');
router.addRoute('/journal_about', 'journal_about.html');
router.addRoute('/journal_editorial_board', 'journal_editorial_board.html');
router.addRoute('/journal_Subscription', 'journal_Subscription.html');
router.addRoute('/journal_submitionOfPaper', 'journal_submitionOfPaper.html');
router.addRoute('/journal_JSES_valume', 'journal_JSES_valume.html');
router.addRoute('/journal_contactUs', 'journal_contactUs.html');
router.addRoute('/doctoral_fellow', 'doctoral_fellow.html');
router.addRoute('/director_tenior', 'director_tenior.html');
router.addRoute('/administration', 'administration.html');
router.addRoute('/Director_desk', 'Director_desk.html');
router.addRoute('/HallBooking', 'HallBooking.html');
router.addRoute('/workstation', 'workstation.html');

// Make router globally available
window.router = router;

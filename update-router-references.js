const fs = require('fs');
const path = require('path');

// Function to update router references in HTML files
function updateRouterReferences() {
    const htmlDir = 'html';
    const files = fs.readdirSync(htmlDir);

    files.forEach(file => {
        if (file.endsWith('.html') && file !== 'index.html') {
            const filePath = path.join(htmlDir, file);
            let content = fs.readFileSync(filePath, 'utf8');
            
            // Replace old router reference with new modular system
            const oldRouter = '<script src="/js/spa-router.js" defer></script>';
            const newRouter = '<script src="/js/routes/index.js" defer></script>';
            
            if (content.includes(oldRouter)) {
                content = content.replace(oldRouter, newRouter);
                fs.writeFileSync(filePath, content);
                console.log(`Updated: ${file}`);
            } else if (content.includes('/js/spa-router.js')) {
                content = content.replace('/js/spa-router.js', '/js/routes/index.js');
                fs.writeFileSync(filePath, content);
                console.log(`Updated: ${file}`);
            }
        }
    });

    console.log('\n✅ Router references updated in all HTML files!');
}

// Run the update
updateRouterReferences();

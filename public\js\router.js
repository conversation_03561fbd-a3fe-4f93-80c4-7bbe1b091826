// Simple client-side router
class Router {
  constructor() {
    this.routes = {};
    this.init();
  }

  init() {
    window.addEventListener("popstate", () => this.handleRoute());
    document.addEventListener("DOMContentLoaded", () => this.handleRoute());

    // Handle all anchor clicks
    document.addEventListener("click", (e) => {
      if (
        e.target.tagName === "A" &&
        e.target.href.startsWith(window.location.origin)
      ) {
        e.preventDefault();
        this.navigate(e.target.pathname);
      }
    });
  }

  addRoute(path, htmlFile) {
    this.routes[path] = htmlFile;
  }

  navigate(path) {
    history.pushState(null, null, path);
    this.handleRoute();
  }

  async handleRoute() {
    const path = window.location.pathname;
    const htmlFile = this.routes[path] || this.routes["/"] || "homepage.html";

    try {
      const response = await fetch(htmlFile);
      const html = await response.text();
      document.documentElement.innerHTML = html;
    } catch (error) {
      console.error("Error loading page:", error);
    }
  }
}

// Initialize router with your routes
const router = new Router();
router.addRoute("/about", "about.html");
router.addRoute("/administration", "administration.html");
router.addRoute("/annualReport", "annualReport.html");
router.addRoute("/ansissLibrary", "ansissLibrary.html");
router.addRoute("/bocmembers", "bocmembers.html");
router.addRoute("/campus", "campus.html");
router.addRoute("/carrier-view", "carrier-view.html");
router.addRoute("/cnvpsfaculty", "cnvpsfaculty.html");
router.addRoute("/completed_project", "completed_project.html");
router.addRoute("/computerlab", "computerlab.html");
router.addRoute("/dashboard", "dashboard.html");
router.addRoute("/Dialogue_series", "Dialogue_series.html");
router.addRoute("/director-tenure", "director-tenure.html");
router.addRoute("/Director_desk", "Director_desk.html");
router.addRoute("/director_tenior", "director_tenior.html");
router.addRoute("/doctoral-fellow", "doctoral-fellow.html");
router.addRoute("/doctoral_fellow", "doctoral_fellow.html");
router.addRoute("/e_Content", "e_Content.html");
router.addRoute("/facilites", "facilites.html");
router.addRoute("/facultymember", "facultymember.html");
router.addRoute("/gallary", "gallary.html");
router.addRoute("/geographyfaculty", "geographyfaculty.html");
router.addRoute("/", "homepage.html");
router.addRoute("/index", "index.html");
router.addRoute("/internal_seminar", "internal_seminar.html");
router.addRoute("/journal_about", "journal_about.html");
router.addRoute("/journal_contactUs", "journal_contactUs.html");
router.addRoute("/journal_editorial_board", "journal_editorial_board.html");
router.addRoute("/journal_JSES_valume", "journal_JSES_valume.html");
router.addRoute("/journal_submitionOfPaper", "journal_submitionOfPaper.html");
router.addRoute("/journal_Subscription", "journal_Subscription.html");
router.addRoute("/logout", "logout.html");
router.addRoute("/newsLetter", "newsLetter.html");
router.addRoute("/OnGoingProject", "OnGoingProject.html");
router.addRoute("/Phd_Program", "Phd_Program.html");
router.addRoute("/Phd_Scholor", "Phd_Scholor.html");
router.addRoute("/politicalfaculty", "politicalfaculty.html");
router.addRoute("/psychologyfaculty", "psychologyfaculty.html");
router.addRoute("/register-form", "register-form.html");
router.addRoute("/sociologyfaculty", "sociologyfaculty.html");
router.addRoute("/statisticsfaculty", "statisticsfaculty.html");
router.addRoute("/workingPaper", "workingPaper.html");
router.addRoute("/workshop", "workshop.html");
router.addRoute("/workstation", "workstation.html");

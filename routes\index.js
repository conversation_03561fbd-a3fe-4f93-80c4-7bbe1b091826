// Router Index - Loads all routing components
(function () {
  "use strict";

  // Function to load script dynamically
  function loadScript(src) {
    return new Promise((resolve, reject) => {
      const script = document.createElement("script");
      script.src = src;
      script.onload = resolve;
      script.onerror = reject;
      document.head.appendChild(script);
    });
  }

  // Function to initialize routing system
  async function initializeRouting() {
    try {
      // Load route configuration first
      await loadScript("/routes/route-config.js");

      // Then load the main router
      await loadScript("/routes/main-router.js");

      console.log("✅ Routing system initialized successfully");
    } catch (error) {
      console.error("❌ Failed to initialize routing system:", error);
    }
  }

  // Initialize when DOM is ready
  if (document.readyState === "loading") {
    document.addEventListener("DOMContentLoaded", initializeRouting);
  } else {
    initializeRouting();
  }
})();

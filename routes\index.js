var express = require("express");
var router = express.Router();
require("dotenv").config();
const productName = "ANSISS";
const fileUrl = process.env.BASEURL;


const renderPage = (page) => (req, res, next) => res.render(page, {});

/* GET home page. */
router.get("/", function (req, res, next) {
    res.render("homepage", {});
});

router.get("/posting", renderPage("posting"));
router.get("/qualification", renderPage("qualification"));
router.get("/logout", renderPage("logout"));
router.get("/about", renderPage("about"));
router.get("/bocmembers", renderPage("bocmembers"));
router.get("/facilites", renderPage("facilites"));
router.get("/computerlab", renderPage("computerlab"));
router.get("/director-tenure", renderPage("director-tenure"));
router.get("/facultymember", renderPage("facultymember"));
router.get("/campus", renderPage("campus"));
router.get("/ansissLibrary", renderPage("ansissLibrary"));
router.get("/register", renderPage("register"));
router.get("/carrier-view", renderPage("carrier-view"));
router.get("/annualReport", renderPage("annualReport"));
router.get("/workingPaper", renderPage("workingPaper"));
router.get("/newsLetter", renderPage("newsLetter"));
router.get("/Phd_Program", renderPage("Phd_Program"));
router.get("/Phd_Scholor", renderPage("Phd_Scholor"));
router.get("/e_Content", renderPage("e_Content"));
router.get("/completed_project", renderPage("completed_project"));
router.get("/OnGoingProject", renderPage("OnGoingProject"));
router.get("/Workshop", renderPage("Workshop"));
router.get("/Dialogue_series", renderPage("Dialogue_series"));
router.get("/internal_seminar", renderPage("internal_seminar"));
router.get("/adminlogin", renderPage("admin/adminlogin"));
router.get("/adminform", renderPage("admin/adminform"));
router.get("/adminform1", renderPage("admin/adminform1"));
router.get("/admindashboard", renderPage("admin/admindashboard"));
router.get("/politicalfaculty", renderPage("politicalfaculty"));
router.get("/psychologyfaculty", renderPage("psychologyfaculty"));
router.get("/sociologyfaculty", renderPage("sociologyfaculty"));
router.get("/statisticsfaculty", renderPage("statisticsfaculty"));
router.get("/cnvpsfaculty", renderPage("cnvpsfaculty"));
router.get("/geographyfaculty", renderPage("geographyfaculty"));
router.get("/gallary", renderPage("gallary"));
//journal section
router.get("/journal_about", renderPage("journal_about"));
router.get("/journal_editorial_board", renderPage("journal_editorial_board"));
router.get("/journal_Subscription", renderPage("journal_Subscription"));
router.get("/journal_submitionOfPaper", renderPage("journal_submitionOfPaper"));
router.get("/journal_JSES_valume", renderPage("journal_JSES_valume"));
router.get("/journal_contactUs", renderPage("journal_contactUs"));
router.get("/doctoral_fellow", renderPage("doctoral_fellow"));
router.get("/director_tenior", renderPage("director_tenior"));
router.get("/administration", renderPage("administration"));
router.get("/Director_desk", renderPage("Director_desk"));
router.get("/HallBooking", renderPage("HallBooking"));
router.get("/workstation", renderPage("workstation"));

module.exports = router;

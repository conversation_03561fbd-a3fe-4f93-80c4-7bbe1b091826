// Route Configuration
const routeConfig = {
  // Main pages
  home: {
    path: '/',
    file: 'homepage.html',
    title: 'ANSISS - Home'
  },
  
  // About section
  about: {
    path: '/about',
    file: 'about.html',
    title: 'About Us - ANSISS'
  },
  bocmembers: {
    path: '/bocmembers',
    file: 'bocmembers.html',
    title: 'BOC Members - ANSISS'
  },
  facultymember: {
    path: '/facultymember',
    file: 'facultymember.html',
    title: 'Faculty Members - ANSISS'
  },
  doctoral_fellow: {
    path: '/doctoral_fellow',
    file: 'doctoral_fellow.html',
    title: 'PhD Scholars - ANSISS'
  },
  director_tenior: {
    path: '/director_tenior',
    file: 'director_tenior.html',
    title: 'Director Tenure - ANSISS'
  },
  Director_desk: {
    path: '/Director_desk',
    file: 'Director_desk.html',
    title: 'Director Desk - ANSISS'
  },
  administration: {
    path: '/administration',
    file: 'administration.html',
    title: 'Administration - ANSISS'
  },

  // Infrastructure section
  campus: {
    path: '/campus',
    file: 'campus.html',
    title: 'Campus - ANSISS'
  },
  ansissLibrary: {
    path: '/ansissLibrary',
    file: 'ansissLibrary.html',
    title: 'Library - ANSISS'
  },
  computerlab: {
    path: '/computerlab',
    file: 'computerlab.html',
    title: 'Computer Lab - ANSISS'
  },
  facilites: {
    path: '/facilites',
    file: 'facilites.html',
    title: 'Facilities - ANSISS'
  },

  // Activities section
  Phd_Program: {
    path: '/Phd_Program',
    file: 'Phd_Program.html',
    title: 'PhD Program - ANSISS'
  },
  e_Content: {
    path: '/e_Content',
    file: 'e_Content.html',
    title: 'e-Content - ANSISS'
  },
  completed_project: {
    path: '/completed_project',
    file: 'completed_project.html',
    title: 'Completed Projects - ANSISS'
  },
  OnGoingProject: {
    path: '/OnGoingProject',
    file: 'OnGoingProject.html',
    title: 'Ongoing Projects - ANSISS'
  },
  Workshop: {
    path: '/Workshop',
    file: 'Workshop.html',
    title: 'Workshops - ANSISS'
  },
  Dialogue_series: {
    path: '/Dialogue_series',
    file: 'Dialogue_series.html',
    title: 'Dialogue Series - ANSISS'
  },
  internal_seminar: {
    path: '/internal_seminar',
    file: 'internal_seminar.html',
    title: 'Internal Seminars - ANSISS'
  },
  workstation: {
    path: '/workstation',
    file: 'workstation.html',
    title: 'Work Station - ANSISS'
  },

  // Publications section
  annualReport: {
    path: '/annualReport',
    file: 'annualReport.html',
    title: 'Annual Report - ANSISS'
  },
  workingPaper: {
    path: '/workingPaper',
    file: 'workingPaper.html',
    title: 'Working Papers - ANSISS'
  },
  newsLetter: {
    path: '/newsLetter',
    file: 'newsLetter.html',
    title: 'Newsletters - ANSISS'
  },

  // Journal section
  journal_about: {
    path: '/journal_about',
    file: 'journal_about.html',
    title: 'Journal - ANSISS'
  },
  journal_editorial_board: {
    path: '/journal_editorial_board',
    file: 'journal_editorial_board.html',
    title: 'Editorial Board - ANSISS'
  },
  journal_Subscription: {
    path: '/journal_Subscription',
    file: 'journal_Subscription.html',
    title: 'Journal Subscription - ANSISS'
  },
  journal_submitionOfPaper: {
    path: '/journal_submitionOfPaper',
    file: 'journal_submitionOfPaper.html',
    title: 'Submit Paper - ANSISS'
  },
  journal_JSES_valume: {
    path: '/journal_JSES_valume',
    file: 'journal_JSES_valume.html',
    title: 'JSES Volume - ANSISS'
  },
  journal_contactUs: {
    path: '/journal_contactUs',
    file: 'journal_contactUs.html',
    title: 'Contact Us - ANSISS'
  },

  // Other pages
  'carrier-view': {
    path: '/carrier-view',
    file: 'carrier-view.html',
    title: 'Career - ANSISS'
  },
  gallary: {
    path: '/gallary',
    file: 'gallary.html',
    title: 'Gallery - ANSISS'
  },
  register: {
    path: '/register',
    file: 'register.html',
    title: 'Register - ANSISS'
  },
  posting: {
    path: '/posting',
    file: 'posting.html',
    title: 'Posting - ANSISS'
  },
  qualification: {
    path: '/qualification',
    file: 'qualification.html',
    title: 'Qualification - ANSISS'
  },
  logout: {
    path: '/logout',
    file: 'logout.html',
    title: 'Logout - ANSISS'
  },
  HallBooking: {
    path: '/HallBooking',
    file: 'HallBooking.html',
    title: 'Hall Booking - ANSISS'
  },

  // Faculty pages
  politicalfaculty: {
    path: '/politicalfaculty',
    file: 'politicalfaculty.html',
    title: 'Political Science Faculty - ANSISS'
  },
  psychologyfaculty: {
    path: '/psychologyfaculty',
    file: 'psychologyfaculty.html',
    title: 'Psychology Faculty - ANSISS'
  },
  sociologyfaculty: {
    path: '/sociologyfaculty',
    file: 'sociologyfaculty.html',
    title: 'Sociology Faculty - ANSISS'
  },
  statisticsfaculty: {
    path: '/statisticsfaculty',
    file: 'statisticsfaculty.html',
    title: 'Statistics Faculty - ANSISS'
  },
  cnvpsfaculty: {
    path: '/cnvpsfaculty',
    file: 'cnvpsfaculty.html',
    title: 'CNVPS Faculty - ANSISS'
  },
  geographyfaculty: {
    path: '/geographyfaculty',
    file: 'geographyfaculty.html',
    title: 'Geography Faculty - ANSISS'
  }
};

// Admin routes (if needed)
const adminRoutes = {
  adminlogin: {
    path: '/adminlogin',
    file: 'admin/adminlogin.html',
    title: 'Admin Login - ANSISS'
  },
  adminform: {
    path: '/adminform',
    file: 'admin/adminform.html',
    title: 'Admin Form - ANSISS'
  },
  adminform1: {
    path: '/adminform1',
    file: 'admin/adminform1.html',
    title: 'Admin Form 1 - ANSISS'
  },
  admindashboard: {
    path: '/admindashboard',
    file: 'admin/admindashboard.html',
    title: 'Admin Dashboard - ANSISS'
  }
};

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { routeConfig, adminRoutes };
} else {
  window.routeConfig = routeConfig;
  window.adminRoutes = adminRoutes;
}

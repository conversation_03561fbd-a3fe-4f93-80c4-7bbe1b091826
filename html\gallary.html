<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>ANSISS - A.N. Sinha Institute of Social Studies</title>
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css"
      integrity="sha512-SfTiTlX6kk+qitfevl/7LibUOeJWlt9rbyDn92a1DqWOw9vWG2MFoays0sgObmWazO5BQPiFucnnEAjpAB+/Sw=="
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css"
    />
    <link href="/css/bootstrap.min.css" rel="stylesheet" />
    <link href="/css/owl.carousel.min.css" rel="stylesheet" />
    <link href="/css/owl.theme.default.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="/css/style.css" />
    <link rel="icon" type="image/png" href="/img/LOGO1.png" />
    <!-- Include SPA Router -->
    <script src="/js/spa-router.js" defer></script>
    <style>
      body {
        background: #fff !important;
      }
      html body {
        font-family: "Poppins", sans-serif !important;
      }
      .dropdown:hover,
      .navigation li:hover {
        color: #f96126;
      }
      .navigation li {
        padding: 6px 0;
      }
      .dropdown:hover .dropdown-menu {
        display: block !important;
      }
      .sub-dropdown-menu {
        display: none;
        position: absolute;
        right: 0;
        top: 0 !important;
        background: rgba(0, 0, 0, 0.7);
        width: 100%;
        padding: 6px 0;
        list-style: none;
        margin: 0;
        border-radius: 5px;
      }
      .sub-dropdown:hover .sub-dropdown-menu {
        display: block;
      }
      .sub-dropdown-menu li {
        padding: 10px 0;
      }
      .sub-dropdown-menu li a {
        color: #ffffff;
        text-decoration: none;
        padding: 0px 20px;
      }
      .sub-dropdown a {
        display: flex !important;
        justify-content: space-between;
        align-items: center;
      }
      .logo-section {
        padding: 10px 0;
        background-color: #f8f9fa;
      }
      .logo-area {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
      .logo img,
      .logo-right img {
        max-width: 100%;
        height: auto;
      }
      .logo-text {
        text-align: center;
      }
      .logo-text h3 {
        font-size: 1.5rem;
        margin: 0;
      }
      .logo-text h2 {
        font-size: 1.2rem;
        margin: 0;
      }
      .top-header {
        background-color: #f8f9fa;
        padding: 10px 0;
      }
      .top-menus ul.top-menu-list {
        display: flex;
        justify-content: space-between;
        align-items: center;
        list-style: none;
        padding: 0;
        margin: 0;
      }
      .top-menu-item {
        margin: 0 10px;
      }
      .top-menu-item a {
        color: #333;
        text-decoration: none;
        font-size: 14px;
      }
      .social-icons-list {
        display: flex;
        list-style: none;
        padding: 0;
        margin: 0;
      }
      .social-icons-list li {
        margin: 0 5px;
      }
      .social-button {
        color: #fff;
        background-color: #007bff;
        padding: 5px 10px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 30px;
        height: 30px;
        text-decoration: none;
      }
      .social-button:hover {
        background-color: #0056b3;
      }
      .dropdown-menu li a {
        color: black !important;
      }
      .dropdown-menu li a:hover {
        color: #f96126 !important;
      }
      /* Responsive Styles */
      @media (max-width: 768px) {
        .logo-area {
          flex-direction: column;
          align-items: center;
        }
        .logo,
        .logo-right {
          text-align: center;
          margin-bottom: 10px;
        }
        .logo-text h3 {
          font-size: 1.2rem;
        }
        .logo-text h2 {
          font-size: 1rem;
        }
        .logo-right {
          order: -1;
        }
        .top-menus ul.top-menu-list {
          flex-direction: column;
          align-items: flex-start;
        }
        .top-menu-item {
          margin: 5px 0;
        }
        .social-icons {
          margin-top: 10px;
        }
        .social-icons-list {
          justify-content: flex-start;
        }
      }

      /* Professional Gallery Styles */
      .gallery-section {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
        padding: 60px 0;
      }

      .gallery-title {
        font-size: 3rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 20px;
        position: relative;
      }

      .gallery-title::after {
        content: "";
        position: absolute;
        bottom: -10px;
        left: 50%;
        transform: translateX(-50%);
        width: 100px;
        height: 4px;
        background: linear-gradient(45deg, #3498db, #2980b9);
        border-radius: 2px;
      }

      .gallery-subtitle {
        font-size: 1.2rem;
        color: #7f8c8d;
        margin-bottom: 40px;
      }

      /* Filter Buttons */
      .gallery-filter {
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
        gap: 15px;
        margin-bottom: 40px;
      }

      .filter-btn {
        background: #fff;
        border: 2px solid #3498db;
        color: #3498db;
        padding: 12px 24px;
        border-radius: 25px;
        cursor: pointer;
        transition: all 0.3s ease;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 1px;
      }

      .filter-btn:hover,
      .filter-btn.active {
        background: #3498db;
        color: #fff;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);
      }

      /* Gallery Grid */
      .gallery-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 30px;
        padding: 20px 0;
      }

      .gallery-item {
        opacity: 1;
        transform: scale(1);
        transition: all 0.5s ease;
      }

      .gallery-item.hidden {
        opacity: 0;
        transform: scale(0.8);
        pointer-events: none;
      }

      .gallery-card {
        position: relative;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        background: #fff;
      }

      .gallery-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
      }

      .gallery-card img {
        width: 100%;
        height: 250px;
        object-fit: cover;
        transition: transform 0.3s ease;
      }

      .gallery-card:hover img {
        transform: scale(1.1);
      }

      .gallery-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          45deg,
          rgba(52, 152, 219, 0.9),
          rgba(155, 89, 182, 0.9)
        );
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: all 0.3s ease;
      }

      .gallery-card:hover .gallery-overlay {
        opacity: 1;
      }

      .gallery-content {
        text-align: center;
        color: #fff;
        padding: 20px;
      }

      .gallery-content h5 {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 10px;
      }

      .gallery-content p {
        font-size: 1rem;
        margin-bottom: 20px;
        opacity: 0.9;
      }

      .view-btn {
        background: #fff;
        color: #3498db;
        border: none;
        padding: 12px 24px;
        border-radius: 25px;
        cursor: pointer;
        font-weight: 600;
        transition: all 0.3s ease;
        text-transform: uppercase;
        letter-spacing: 1px;
      }

      .view-btn:hover {
        background: #f8f9fa;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
      }

      /* Lightbox Styles */
      .lightbox-modal {
        display: none;
        position: fixed;
        z-index: 9999;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.9);
        animation: fadeIn 0.3s ease;
      }

      .lightbox-content {
        position: relative;
        margin: auto;
        padding: 20px;
        width: 90%;
        max-width: 800px;
        top: 50%;
        transform: translateY(-50%);
        text-align: center;
      }

      .lightbox-close {
        position: absolute;
        top: 15px;
        right: 35px;
        color: #fff;
        font-size: 40px;
        font-weight: bold;
        cursor: pointer;
        z-index: 10000;
      }

      .lightbox-close:hover {
        color: #3498db;
      }

      #lightboxImage {
        max-width: 100%;
        max-height: 70vh;
        border-radius: 10px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
      }

      .lightbox-caption {
        color: #fff;
        padding: 20px 0;
      }

      .lightbox-caption h4 {
        font-size: 1.8rem;
        margin-bottom: 10px;
      }

      .lightbox-caption p {
        font-size: 1.1rem;
        opacity: 0.8;
      }

      .lightbox-nav {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        width: 100%;
        display: flex;
        justify-content: space-between;
        padding: 0 20px;
      }

      .lightbox-prev,
      .lightbox-next {
        background: rgba(52, 152, 219, 0.8);
        color: #fff;
        border: none;
        padding: 15px 20px;
        cursor: pointer;
        font-size: 18px;
        border-radius: 50%;
        transition: all 0.3s ease;
      }

      .lightbox-prev:hover,
      .lightbox-next:hover {
        background: #3498db;
        transform: scale(1.1);
      }

      @keyframes fadeIn {
        from {
          opacity: 0;
        }
        to {
          opacity: 1;
        }
      }

      /* Responsive Design */
      @media (max-width: 768px) {
        .gallery-title {
          font-size: 2rem;
        }

        .gallery-grid {
          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
          gap: 20px;
        }

        .filter-btn {
          padding: 10px 20px;
          font-size: 0.9rem;
        }

        .gallery-card img {
          height: 200px;
        }

        .lightbox-content {
          width: 95%;
          padding: 10px;
        }

        .lightbox-nav {
          padding: 0 10px;
        }
      }

      @media (max-width: 480px) {
        .gallery-grid {
          grid-template-columns: 1fr;
        }

        .gallery-filter {
          flex-direction: column;
          align-items: center;
        }

        .filter-btn {
          width: 200px;
        }
      }
    </style>
  </head>

  <body>
    <section class="top-header">
      <div class="container-fluid">
        <div class="row">
          <div class="col-md-12 col-lg-12 col-sm-12 col-12">
            <div class="top-menus">
              <ul class="top-menu-list">
                <li class="top-menu-item">
                  <a href="#"
                    ><i class="fa fa-phone" aria-hidden="true"></i>
                    +91-0612-2219395, 2219226</a
                  >
                </li>
                <li class="top-menu-item">
                  <a href="#"
                    ><i class="fa fa-envelope" aria-hidden="true"></i
                    ><EMAIL></a
                  >
                </li>
                <li class="top-menu-item">
                  <a href="#">Skip to main content</a>
                </li>
                <li class="top-menu-item">
                  <a href="#">Screen Reader Access</a>
                </li>
                <li class="social-icons">
                  <ul class="social-icons-list">
                    <li>
                      <i class="fab fa-twitter"></i>
                    </li>
                    <li>
                      <i class="fab fa-linkedin-in"></i>
                    </li>
                    <li>
                      <i class="fab fa-facebook-f"></i>
                    </li>
                    <li>
                      <a
                        href="https://webmail.ansiss.res.in"
                        class="social-button social-button--webmail"
                        aria-label="Webmail"
                      >
                        <i class="fas fa-envelope"></i>
                      </a>
                    </li>
                    <li class="nav-item" id="google_translate_element"></li>
                  </ul>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </section>
    <header class="logo-section">
      <div class="container-fluid">
        <div class="row">
          <div class="logo-area">
            <div class="col-md-2 col-lg-2 col-sm-2 col-12">
              <div class="logo">
                <a href="/">
                  <img
                    src="/img/LOGO1.png"
                    alt="img-logo"
                    class="img-fluid"
                    style="filter: drop-shadow(0 0 0.5rem rgb(253, 253, 253))"
                  />
                </a>
              </div>
            </div>
            <div class="col-md-8 col-lg-8 col-sm-8 col-12">
              <div class="logo-text">
                <h3>अनुग्रह नारायण सिंह समाज अध्ययन संस्थान</h3>
                <h2 id="name-eng">A N Sinha Institute of Social Studies</h2>
              </div>
            </div>
            <div class="col-md-2 col-lg-2 col-sm-2 col-6">
              <div class="logo-right">
                <img
                  src="/img/directorhd.png"
                  alt="img-logo"
                  class="img-fluid"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </header>

    <section class="navMenu">
      <div class="container">
        <div class="row">
          <nav class="navbar navbar-expand-lg">
            <button
              class="navbar-toggler"
              type="button"
              data-bs-toggle="collapse"
              data-bs-target="#navbarTogglerDemo01"
              aria-controls="navbarTogglerDemo01"
              aria-expanded="false"
              aria-label="Toggle navigation"
            >
              <i class="fas fa-bars"></i>
            </button>
            <div
              class="collapse navbar-collapse"
              id="bs-example-navbar-collapse-1"
            >
              <div class="menu-primary-menu-container">
                <ul class="nav navbar-nav navigation main-nav">
                  <li class=""><a class="dropdown-item" href="/">Home</a></li>
                  <li class="dropdown">
                    <a
                      class="dropdown-toggle all-toggle"
                      href="javascript:void(0)"
                      >About Us</a
                    >
                    <ul class="dropdown-menu">
                      <li class="">
                        <a class="dropdown-item" href="/about">About Us</a>
                      </li>
                      <li class="">
                        <a class="dropdown-item" href="/bocmembers"
                          >BOC Members</a
                        >
                      </li>
                      <li class="">
                        <a class="dropdown-item" href="/facultymember"
                          >Faculty Members</a
                        >
                      </li>
                      <li class="">
                        <a class="dropdown-item" href="/doctoral_fellow"
                          >PHD Scholar</a
                        >
                      </li>
                      <li class="">
                        <a class="dropdown-item" href="/director_tenior"
                          >Director Tenure</a
                        >
                      </li>
                      <li class="">
                        <a class="dropdown-item" href="/Director_desk"
                          >Director Desk</a
                        >
                      </li>
                      <li class="">
                        <a class="dropdown-item" href="/administration"
                          >Administration</a
                        >
                      </li>
                    </ul>
                  </li>
                  <li class="dropdown">
                    <a
                      class="dropdown-toggle all-toggle"
                      href="javascript:void(0)"
                      >Infrastructure</a
                    >
                    <ul class="dropdown-menu">
                      <li class="">
                        <a class="dropdown-item" href="/campus"
                          >Building & Campus</a
                        >
                      </li>
                      <li class="">
                        <a class="dropdown-item" href="/ansissLibrary"
                          >ANSISS Library</a
                        >
                      </li>
                      <li class="">
                        <a
                          class="dropdown-item"
                          href="http://eg4.nic.in/CGOVLIB1/OPAC/Default.aspx?LIB_CODE=ANSISS"
                          >ANSISS e-Library</a
                        >
                      </li>
                      <li class="">
                        <a class="dropdown-item" href="/computerlab"
                          >Computer Centre</a
                        >
                      </li>
                      <li class="">
                        <a class="dropdown-item" href="/facilites"
                          >ANSISS Facilities</a
                        >
                      </li>
                    </ul>
                  </li>
                  <li class="dropdown">
                    <a
                      class="dropdown-toggle all-toggle"
                      href="javascript:void(0)"
                      >Activities</a
                    >
                    <ul class="dropdown-menu">
                      <li class="">
                        <a class="dropdown-item" href="/Phd_Program"
                          >Ph. D Programme</a
                        >
                      </li>
                      <li class="">
                        <a class="dropdown-item" href="/e_Content">e-Content</a>
                      </li>
                      <li class="">
                        <a class="dropdown-item" href="/completed_project"
                          >Completed Projects</a
                        >
                      </li>
                      <li class="">
                        <a class="dropdown-item" href="/OnGoingProject"
                          >On Going Projects</a
                        >
                      </li>
                      <li class="">
                        <a class="dropdown-item" href="/Workshop"
                          >Workshop/Training</a
                        >
                      </li>
                      <li class="">
                        <a class="dropdown-item" href="/Dialogue_series"
                          >Dialogue Series</a
                        >
                      </li>
                      <li class="">
                        <a class="dropdown-item" href="/internal_seminar"
                          >Internal Seminars</a
                        >
                      </li>
                      <li class="">
                        <a class="dropdown-item" href="/workstation"
                          >Work Station</a
                        >
                      </li>
                    </ul>
                  </li>
                  <li class="dropdown">
                    <a
                      class="dropdown-toggle all-toggle"
                      href="javascript:void(0)"
                      >Publications</a
                    >
                    <ul class="dropdown-menu">
                      <li class="">
                        <a class="dropdown-item" href="/annualReport"
                          >Annual Report</a
                        >
                      </li>
                      <li class="">
                        <a class="dropdown-item" href="/workingPaper"
                          >Working Paper</a
                        >
                      </li>
                      <li class="">
                        <a class="dropdown-item" href="/newsLetter"
                          >Newsletters</a
                        >
                      </li>
                    </ul>
                  </li>
                  <li class="">
                    <a class="dropdown-item" href="/journal_about">Journal</a>
                  </li>
                  <li class="">
                    <a class="dropdown-item" href="/carrier-view">Career</a>
                  </li>
                  <li class="">
                    <a class="dropdown-item" href="/gallary"
                      >Gallery<img
                        style="width: 40px; display: inline-block"
                        src="/images/new-gif-image.gif"
                    /></a>
                  </li>
                </ul>
              </div>
            </div>
          </nav>
        </div>
      </div>
    </section>
    <main class="main-content">
      <!-- Professional Gallery Section -->
      <section class="gallery-section py-5">
        <div class="container">
          <div class="row">
            <div class="col-12 text-center mb-5">
              <h1 class="gallery-title">ANSISS Event Gallery</h1>
              <p class="gallery-subtitle">
                Capturing moments from our academic journey and institutional
                events
              </p>
            </div>
          </div>

          <!-- Gallery Filter Buttons -->
          <div class="row mb-4">
            <div class="col-12 text-center">
              <div class="gallery-filter">
                <button class="filter-btn active" data-filter="all">All</button>
                <button class="filter-btn" data-filter="events">Events</button>
                <button class="filter-btn" data-filter="campus">Campus</button>
                <button class="filter-btn" data-filter="seminars">
                  Seminars
                </button>
                <button class="filter-btn" data-filter="facilities">
                  Facilities
                </button>
              </div>
            </div>
          </div>

          <!-- Gallery Grid -->
          <div class="gallery-grid" id="galleryGrid">
            <!-- Event Images -->
            <div class="gallery-item" data-category="events">
              <div class="gallery-card">
                <img
                  src="/img/gallery18.jpeg"
                  alt="ANSISS Event"
                  loading="lazy"
                />
                <div class="gallery-overlay">
                  <div class="gallery-content">
                    <h5>Academic Event</h5>
                    <p>Special academic gathering</p>
                    <button
                      class="view-btn"
                      onclick="openLightbox('/img/gallery18.jpeg', 'Academic Event')"
                    >
                      <i class="fas fa-eye"></i> View
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <div class="gallery-item" data-category="events">
              <div class="gallery-card">
                <img
                  src="/img/gallery19.jpeg"
                  alt="ANSISS Event"
                  loading="lazy"
                />
                <div class="gallery-overlay">
                  <div class="gallery-content">
                    <h5>Conference</h5>
                    <p>Annual conference proceedings</p>
                    <button
                      class="view-btn"
                      onclick="openLightbox('/img/gallery19.jpeg', 'Conference')"
                    >
                      <i class="fas fa-eye"></i> View
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <div class="gallery-item" data-category="seminars">
              <div class="gallery-card">
                <img src="/img/gallery11.jpeg" alt="Seminar" loading="lazy" />
                <div class="gallery-overlay">
                  <div class="gallery-content">
                    <h5>Research Seminar</h5>
                    <p>Faculty research presentation</p>
                    <button
                      class="view-btn"
                      onclick="openLightbox('/img/gallery11.jpeg', 'Research Seminar')"
                    >
                      <i class="fas fa-eye"></i> View
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <div class="gallery-item" data-category="seminars">
              <div class="gallery-card">
                <img src="/img/gallery12.jpeg" alt="Workshop" loading="lazy" />
                <div class="gallery-overlay">
                  <div class="gallery-content">
                    <h5>Workshop</h5>
                    <p>Training and development session</p>
                    <button
                      class="view-btn"
                      onclick="openLightbox('/img/gallery12.jpeg', 'Workshop')"
                    >
                      <i class="fas fa-eye"></i> View
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <div class="gallery-item" data-category="events">
              <div class="gallery-card">
                <img
                  src="/img/gallery14.jpeg"
                  alt="Cultural Event"
                  loading="lazy"
                />
                <div class="gallery-overlay">
                  <div class="gallery-content">
                    <h5>Cultural Program</h5>
                    <p>Annual cultural celebration</p>
                    <button
                      class="view-btn"
                      onclick="openLightbox('/img/gallery14.jpeg', 'Cultural Program')"
                    >
                      <i class="fas fa-eye"></i> View
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <div class="gallery-item" data-category="events">
              <div class="gallery-card">
                <img
                  src="/img/gallery15.jpeg"
                  alt="Graduation"
                  loading="lazy"
                />
                <div class="gallery-overlay">
                  <div class="gallery-content">
                    <h5>Graduation Ceremony</h5>
                    <p>PhD scholars graduation</p>
                    <button
                      class="view-btn"
                      onclick="openLightbox('/img/gallery15.jpeg', 'Graduation Ceremony')"
                    >
                      <i class="fas fa-eye"></i> View
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <div class="gallery-item" data-category="campus">
              <div class="gallery-card">
                <img
                  src="/img/gallery16.jpeg"
                  alt="Campus View"
                  loading="lazy"
                />
                <div class="gallery-overlay">
                  <div class="gallery-content">
                    <h5>Campus Life</h5>
                    <p>Daily campus activities</p>
                    <button
                      class="view-btn"
                      onclick="openLightbox('/img/gallery16.jpeg', 'Campus Life')"
                    >
                      <i class="fas fa-eye"></i> View
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <div class="gallery-item" data-category="facilities">
              <div class="gallery-card">
                <img src="/img/picture1.jpg" alt="Library" loading="lazy" />
                <div class="gallery-overlay">
                  <div class="gallery-content">
                    <h5>Library Facility</h5>
                    <p>State-of-the-art library</p>
                    <button
                      class="view-btn"
                      onclick="openLightbox('/img/picture1.jpg', 'Library Facility')"
                    >
                      <i class="fas fa-eye"></i> View
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <div class="gallery-item" data-category="facilities">
              <div class="gallery-card">
                <img
                  src="/img/picture2.jpg"
                  alt="Computer Lab"
                  loading="lazy"
                />
                <div class="gallery-overlay">
                  <div class="gallery-content">
                    <h5>Computer Center</h5>
                    <p>Modern computing facilities</p>
                    <button
                      class="view-btn"
                      onclick="openLightbox('/img/picture2.jpg', 'Computer Center')"
                    >
                      <i class="fas fa-eye"></i> View
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <div class="gallery-item" data-category="campus">
              <div class="gallery-card">
                <img
                  src="/img/picture3.jpg"
                  alt="Campus Building"
                  loading="lazy"
                />
                <div class="gallery-overlay">
                  <div class="gallery-content">
                    <h5>Main Building</h5>
                    <p>Administrative building</p>
                    <button
                      class="view-btn"
                      onclick="openLightbox('/img/picture3.jpg', 'Main Building')"
                    >
                      <i class="fas fa-eye"></i> View
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <div class="gallery-item" data-category="facilities">
              <div class="gallery-card">
                <img
                  src="/img/picture5.jpg"
                  alt="Conference Hall"
                  loading="lazy"
                />
                <div class="gallery-overlay">
                  <div class="gallery-content">
                    <h5>Conference Hall</h5>
                    <p>Modern conference facility</p>
                    <button
                      class="view-btn"
                      onclick="openLightbox('/img/picture5.jpg', 'Conference Hall')"
                    >
                      <i class="fas fa-eye"></i> View
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <div class="gallery-item" data-category="campus">
              <div class="gallery-card">
                <img src="/img/picture6.jpg" alt="Garden Area" loading="lazy" />
                <div class="gallery-overlay">
                  <div class="gallery-content">
                    <h5>Campus Garden</h5>
                    <p>Beautiful green spaces</p>
                    <button
                      class="view-btn"
                      onclick="openLightbox('/img/picture6.jpg', 'Campus Garden')"
                    >
                      <i class="fas fa-eye"></i> View
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <div class="gallery-item" data-category="facilities">
              <div class="gallery-card">
                <img
                  src="/img/picture7.jpg"
                  alt="Research Center"
                  loading="lazy"
                />
                <div class="gallery-overlay">
                  <div class="gallery-content">
                    <h5>Research Center</h5>
                    <p>Advanced research facilities</p>
                    <button
                      class="view-btn"
                      onclick="openLightbox('/img/picture7.jpg', 'Research Center')"
                    >
                      <i class="fas fa-eye"></i> View
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Lightbox Modal -->
      <div id="lightboxModal" class="lightbox-modal">
        <div class="lightbox-content">
          <span class="lightbox-close" onclick="closeLightbox()">&times;</span>
          <img id="lightboxImage" src="" alt="" />
          <div class="lightbox-caption">
            <h4 id="lightboxTitle"></h4>
            <p id="lightboxDescription"></p>
          </div>
          <div class="lightbox-nav">
            <button class="lightbox-prev" onclick="prevImage()">
              &#10094;
            </button>
            <button class="lightbox-next" onclick="nextImage()">
              &#10095;
            </button>
          </div>
        </div>
      </div>
    </main>
    <footer class="footer-area black-bg-color">
      <div class="container">
        <div class="row">
          <div class="col-lg-3 col-sm-6">
            <div class="single-footer-widget bg-f9f5f1">
              <a href="/" class="logo">
                <img
                  src="/img/LOGO1.png"
                  alt="Image"
                  style="background-color: #daa520; border-radius: 20px"
                />
              </a>

              <ul class="social-icon">
                <li>
                  <span>Follow us:</span>
                </li>
                <li>
                  <a
                    href="#"
                    class="social-button social-button--facebook"
                    aria-label="Facebook"
                  >
                    <i class="fab fa-facebook-f"></i>
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    class="social-button social-button--linkedin"
                    aria-label="LinkedIn"
                  >
                    <i class="fab fa-linkedin-in"></i>
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    class="social-button social-button--snapchat"
                    aria-label="Snapchat"
                  >
                    <i class="fab fa-snapchat-ghost"></i>
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    class="social-button social-button--twitter"
                    aria-label="Twitter"
                  >
                    <i class="fab fa-twitter"></i>
                  </a>
                </li>
              </ul>
            </div>
          </div>

          <div class="col-lg-3 col-sm-6">
            <div class="single-footer-widget">
              <h3>Useful link</h3>
              <div class="menu-useful-links-container">
                <ul class="import-link">
                  <li
                    id="menu-item-7249"
                    class="menu-item menu-item-type-post_type menu-item-object-page menu-item-7249"
                  >
                    <a href="#">Infrastructure Faclities</a>
                  </li>

                  <li
                    id="menu-item-7250"
                    class="menu-item menu-item-type-post_type menu-item-object-page menu-item-7250"
                  >
                    <a href="#">ANSISS Ph.D. Alumni</a>
                  </li>
                  <li
                    id="menu-item-7251"
                    class="menu-item menu-item-type-post_type menu-item-object-page menu-item-7251"
                  >
                    <a href="#">Right To Information Act</a>
                  </li>
                  <li
                    id="menu-item-7252"
                    class="menu-item menu-item-type-post_type menu-item-object-page menu-item-7252"
                  >
                    <a href="#">Internal Complaints Committee</a>
                  </li>
                  <li
                    id="menu-item-7274"
                    class="menu-item menu-item-type-post_type menu-item-object-page menu-item-7274"
                  >
                    <a href="#">Tender Notices</a>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          <div class="col-lg-3 col-sm-6">
            <div class="single-footer-widget">
              <h3>Quick Links</h3>
              <div class="menu-quick-links-container">
                <ul class="import-link">
                  <li
                    id="menu-item-7036"
                    class="menu-item menu-item-type-post_type menu-item-object-page menu-item-7036"
                  >
                    <a href="#">Current Research</a>
                  </li>
                  <li
                    id="menu-item-7039"
                    class="menu-item menu-item-type-post_type menu-item-object-page menu-item-7039"
                  >
                    <a href="#">Seminars and Training</a>
                  </li>
                  <li
                    id="menu-item-10887"
                    class="menu-item menu-item-type-post_type menu-item-object-page menu-item-10887"
                  >
                    <a href="#">Post-Doctoral Research</a>
                  </li>
                  <li
                    id="menu-item-7090"
                    class="menu-item menu-item-type-post_type menu-item-object-page menu-item-7090"
                  >
                    <a href="#">SRTT Programmes</a>
                  </li>
                  <li
                    id="menu-item-7093"
                    class="menu-item menu-item-type-post_type menu-item-object-page menu-item-7093"
                  >
                    <a href="#">Databank</a>
                  </li>
                  <li
                    id="menu-item-7094"
                    class="menu-item menu-item-type-post_type menu-item-object-page menu-item-7094"
                  >
                    <a href="#">Certificate Courses</a>
                  </li>
                  <li
                    id="menu-item-14008"
                    class="menu-item menu-item-type-custom menu-item-object-custom menu-item-14008"
                  >
                    <a href="#">Grant and Internship</a>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          <div class="col-lg-3 col-sm-6">
            <div class="single-footer-widget">
              <h3>Inquiries</h3>

              <ul class="address">
                <li class="location">
                  <i class="fa fa-map-marker" aria-hidden="true"></i>
                  <span
                    >Institute for Social Studies

                    <br />
                    North West Gandhi Maidan <br />
                    Patna - 800 001
                  </span>
                  <span>
                    <iframe
                      src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3597.4965245532367!2d85.13971057323835!3d25.6216413143824!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x39ed5851297fd585%3A0xa5c23a4b14d78ca9!2sA%20N%20Sinha%20Institute%20of%20Social%20Studies!5e0!3m2!1sen!2sin!4v1722628045557!5m2!1sen!2sin"
                      width="250"
                      height="50"
                      style="border: 0"
                      allowfullscreen=""
                      loading="lazy"
                      referrerpolicy="no-referrer-when-downgrade"
                    ></iframe>
                  </span>
                </li>
                <li>
                  <i class="fa fa-envelope" aria-hidden="true"></i>
                  <span>Email</span>
                  <a href="mailto:<EMAIL>"
                    ><EMAIL></a
                  >
                </li>
                <li>
                  <i class="fa fa-phone" aria-hidden="true"></i>
                  <span>Phone</span>
                  <a href="tel:+91-080-23215468"
                    >+0612-2219395, Fax No.: 0612-2219226</a
                  >
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </footer>

    <div class="copy-right-area">
      <div class="container">
        <div class="row">
          <div class="col-md-6 col-lg-6 col-sm-6 col-12">
            <p>
              Copyright © 2024 ANSISS. Design &amp; Developed By
              <a href="https://codebuckets.in/" target="_blank"
                >Codebucket Solutions PVT LTD</a
              >
            </p>
          </div>
          <div class="col-md-6 col-lg-6 col-sm-6 col-12">
            <h2 style="color: #fff; font-size: 16px; text-align: right">
              Total Visitors: 54262
            </h2>
          </div>
        </div>
      </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.7.0.js"></script>
    <script src="/js/bootstrap.bundle.min.js"></script>
    <script src="/js/owl.carousel.min.js"></script>

    <script
      type="text/javascript"
      src="//translate.google.com/translate_a/element.js?cb=googleTranslateElementInit"
    ></script>

    <script type="text/javascript">
      function googleTranslateElementInit() {
        new google.translate.TranslateElement(
          { pageLanguage: "en", includedLanguages: "en,hi" },
          "google_translate_element"
        );
      }
    </script>

    <script>
      jQuery(document).ready(function ($) {
        var delay = 100;
        setTimeout(function () {
          $(".elementor-tab-title").removeClass("elementor-active");
          $(".elementor-tab-content").css("display", "none");
        }, delay);
      });
    </script>

    <script>
      $(document).ready(function () {
        $(".dropdown-toggle").append('<span class="caret"></span>');
        $(".dropdown-menu li ").addClass("sub-dropdown");
        $(".dropdown-menu li ul").addClass("sub-dropdown-menu");
        $(".dropdown-menu li ul").removeClass("dropdown-menu");
        $(".sub-dropdown-menu li").removeClass("sub-dropdown");
        $(".sub-dropdown-menu li a").removeClass("dropdown-item");
      });
    </script>

    <script>
      document.addEventListener("DOMContentLoaded", function () {
        document
          .querySelector(".navbar-toggler")
          .addEventListener("click", function () {
            var navbarNav = document.getElementById(
              "bs-example-navbar-collapse-1"
            );
            if (navbarNav.classList.contains("show")) {
              navbarNav.classList.remove("show");
            } else {
              navbarNav.classList.add("show");
            }
          });
      });
    </script>

    <!-- Professional Gallery JavaScript -->
    <script>
      // Gallery functionality
      let currentImageIndex = 0;
      let galleryImages = [];

      // Initialize gallery
      document.addEventListener("DOMContentLoaded", function () {
        initializeGallery();
        setupFilterButtons();
        collectGalleryImages();
      });

      function initializeGallery() {
        // Add animation on scroll
        const observer = new IntersectionObserver((entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              entry.target.style.opacity = "1";
              entry.target.style.transform = "translateY(0)";
            }
          });
        });

        document.querySelectorAll(".gallery-item").forEach((item) => {
          item.style.opacity = "0";
          item.style.transform = "translateY(30px)";
          item.style.transition = "all 0.6s ease";
          observer.observe(item);
        });
      }

      function setupFilterButtons() {
        const filterButtons = document.querySelectorAll(".filter-btn");
        const galleryItems = document.querySelectorAll(".gallery-item");

        filterButtons.forEach((button) => {
          button.addEventListener("click", function () {
            const filter = this.getAttribute("data-filter");

            // Update active button
            filterButtons.forEach((btn) => btn.classList.remove("active"));
            this.classList.add("active");

            // Filter gallery items
            galleryItems.forEach((item) => {
              const category = item.getAttribute("data-category");

              if (filter === "all" || category === filter) {
                item.classList.remove("hidden");
                setTimeout(() => {
                  item.style.opacity = "1";
                  item.style.transform = "scale(1)";
                }, 100);
              } else {
                item.style.opacity = "0";
                item.style.transform = "scale(0.8)";
                setTimeout(() => {
                  item.classList.add("hidden");
                }, 300);
              }
            });
          });
        });
      }

      function collectGalleryImages() {
        const images = document.querySelectorAll(".gallery-card img");
        galleryImages = Array.from(images).map((img) => ({
          src: img.src,
          alt: img.alt,
          title: img.closest(".gallery-item").querySelector("h5").textContent,
          description: img.closest(".gallery-item").querySelector("p")
            .textContent,
        }));
      }

      function openLightbox(imageSrc, title) {
        const modal = document.getElementById("lightboxModal");
        const image = document.getElementById("lightboxImage");
        const titleElement = document.getElementById("lightboxTitle");
        const descriptionElement = document.getElementById(
          "lightboxDescription"
        );

        // Find current image index
        currentImageIndex = galleryImages.findIndex(
          (img) => img.src === imageSrc
        );

        // Set image and content
        image.src = imageSrc;
        titleElement.textContent = title;

        // Find description from gallery data
        const imageData = galleryImages[currentImageIndex];
        descriptionElement.textContent = imageData ? imageData.description : "";

        // Show modal
        modal.style.display = "block";
        document.body.style.overflow = "hidden";

        // Add keyboard navigation
        document.addEventListener("keydown", handleKeyboardNavigation);
      }

      function closeLightbox() {
        const modal = document.getElementById("lightboxModal");
        modal.style.display = "none";
        document.body.style.overflow = "auto";

        // Remove keyboard navigation
        document.removeEventListener("keydown", handleKeyboardNavigation);
      }

      function prevImage() {
        currentImageIndex =
          (currentImageIndex - 1 + galleryImages.length) % galleryImages.length;
        updateLightboxImage();
      }

      function nextImage() {
        currentImageIndex = (currentImageIndex + 1) % galleryImages.length;
        updateLightboxImage();
      }

      function updateLightboxImage() {
        const image = document.getElementById("lightboxImage");
        const titleElement = document.getElementById("lightboxTitle");
        const descriptionElement = document.getElementById(
          "lightboxDescription"
        );
        const currentImage = galleryImages[currentImageIndex];

        // Add fade effect
        image.style.opacity = "0";

        setTimeout(() => {
          image.src = currentImage.src;
          titleElement.textContent = currentImage.title;
          descriptionElement.textContent = currentImage.description;
          image.style.opacity = "1";
        }, 150);
      }

      function handleKeyboardNavigation(e) {
        switch (e.key) {
          case "Escape":
            closeLightbox();
            break;
          case "ArrowLeft":
            prevImage();
            break;
          case "ArrowRight":
            nextImage();
            break;
        }
      }

      // Close lightbox when clicking outside the image
      document
        .getElementById("lightboxModal")
        .addEventListener("click", function (e) {
          if (e.target === this) {
            closeLightbox();
          }
        });

      // Lazy loading for better performance
      if ("IntersectionObserver" in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              const img = entry.target;
              img.src = img.dataset.src || img.src;
              img.classList.remove("lazy");
              observer.unobserve(img);
            }
          });
        });

        document.querySelectorAll('img[loading="lazy"]').forEach((img) => {
          imageObserver.observe(img);
        });
      }

      // Add smooth scrolling to gallery section
      function scrollToGallery() {
        document.querySelector(".gallery-section").scrollIntoView({
          behavior: "smooth",
        });
      }

      // Add loading animation
      window.addEventListener("load", function () {
        const galleryItems = document.querySelectorAll(".gallery-item");
        galleryItems.forEach((item, index) => {
          setTimeout(() => {
            item.style.opacity = "1";
            item.style.transform = "translateY(0)";
          }, index * 100);
        });
      });
    </script>
  </body>
</html>

<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>ANSISS - Professional Gallery</title>
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/swiper/8.4.5/swiper-bundle.min.css"
    />
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        overflow-x: hidden;
      }

      /* Hero Section */
      .hero-section {
        background: linear-gradient(
          135deg,
          rgba(102, 126, 234, 0.9) 0%,
          rgba(118, 75, 162, 0.9) 100%
        );
        padding: 80px 0 60px;
        text-align: center;
        position: relative;
        overflow: hidden;
      }

      .hero-section::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="50" cy="50" r="0.5" fill="rgba(255,255,255,0.03)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        opacity: 0.3;
        animation: float 20s ease-in-out infinite;
      }

      @keyframes float {
        0%,
        100% {
          transform: translateY(0px);
        }
        50% {
          transform: translateY(-20px);
        }
      }

      .hero-content {
        position: relative;
        z-index: 2;
        max-width: 800px;
        margin: 0 auto;
        padding: 0 20px;
      }

      .hero-title {
        font-size: 4rem;
        font-weight: 800;
        color: #fff;
        margin-bottom: 20px;
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        animation: fadeInUp 1s ease-out;
      }

      .hero-subtitle {
        font-size: 1.4rem;
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 40px;
        animation: fadeInUp 1s ease-out 0.2s both;
      }

      .hero-stats {
        display: flex;
        justify-content: center;
        gap: 40px;
        flex-wrap: wrap;
        animation: fadeInUp 1s ease-out 0.4s both;
      }

      .stat-item {
        text-align: center;
        color: #fff;
      }

      .stat-number {
        font-size: 2.5rem;
        font-weight: 700;
        display: block;
        text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
      }

      .stat-label {
        font-size: 0.9rem;
        opacity: 0.8;
        text-transform: uppercase;
        letter-spacing: 1px;
      }

      /* Filter Section */
      .filter-section {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        padding: 30px 0;
        position: sticky;
        top: 0;
        z-index: 100;
        box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
      }

      .filter-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
      }

      .filter-tabs {
        display: flex;
        justify-content: center;
        gap: 10px;
        flex-wrap: wrap;
      }

      .filter-tab {
        background: transparent;
        border: 2px solid #667eea;
        color: #667eea;
        padding: 12px 24px;
        border-radius: 50px;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 1px;
        position: relative;
        overflow: hidden;
      }

      .filter-tab::before {
        content: "";
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(255, 255, 255, 0.4),
          transparent
        );
        transition: left 0.5s;
      }

      .filter-tab:hover::before {
        left: 100%;
      }

      .filter-tab:hover,
      .filter-tab.active {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: #fff;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
      }

      /* Gallery Section */
      .gallery-section {
        padding: 60px 0;
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      }

      .gallery-container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 0 20px;
      }

      /* Swiper Styles */
      .gallery-swiper {
        padding: 20px 0 60px;
      }

      .gallery-slide {
        height: auto;
      }

      .gallery-card {
        position: relative;
        border-radius: 20px;
        overflow: hidden;
        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        background: #fff;
        cursor: pointer;
        height: 350px;
      }

      .gallery-card:hover {
        transform: translateY(-10px) scale(1.02);
        box-shadow: 0 25px 60px rgba(0, 0, 0, 0.25);
      }

      .gallery-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
      }

      .gallery-card:hover .gallery-image {
        transform: scale(1.1);
      }

      .gallery-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          135deg,
          rgba(102, 126, 234, 0.9) 0%,
          rgba(118, 75, 162, 0.9) 100%
        );
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      }

      .gallery-card:hover .gallery-overlay {
        opacity: 1;
      }

      .gallery-info {
        text-align: center;
        color: #fff;
        padding: 20px;
        transform: translateY(20px);
        transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      }

      .gallery-card:hover .gallery-info {
        transform: translateY(0);
      }

      .gallery-title {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 10px;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      }

      .gallery-description {
        font-size: 1rem;
        opacity: 0.9;
        margin-bottom: 20px;
        line-height: 1.4;
      }

      .gallery-category {
        display: inline-block;
        background: rgba(255, 255, 255, 0.2);
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        text-transform: uppercase;
        letter-spacing: 1px;
        margin-bottom: 15px;
      }

      .view-button {
        background: #fff;
        color: #667eea;
        border: none;
        padding: 12px 24px;
        border-radius: 50px;
        cursor: pointer;
        font-weight: 600;
        transition: all 0.3s ease;
        text-transform: uppercase;
        letter-spacing: 1px;
        display: inline-flex;
        align-items: center;
        gap: 8px;
      }

      .view-button:hover {
        background: #f8f9fa;
        transform: scale(1.05);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
      }

      /* Swiper Navigation */
      .swiper-button-next,
      .swiper-button-prev {
        background: rgba(255, 255, 255, 0.9);
        width: 50px;
        height: 50px;
        border-radius: 50%;
        color: #667eea;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
      }

      .swiper-button-next:hover,
      .swiper-button-prev:hover {
        background: #fff;
        transform: scale(1.1);
        box-shadow: 0 6px 25px rgba(0, 0, 0, 0.15);
      }

      .swiper-button-next::after,
      .swiper-button-prev::after {
        font-size: 18px;
        font-weight: 700;
      }

      /* Swiper Pagination */
      .swiper-pagination-bullet {
        background: #667eea;
        opacity: 0.3;
        width: 12px;
        height: 12px;
        transition: all 0.3s ease;
      }

      .swiper-pagination-bullet-active {
        opacity: 1;
        transform: scale(1.2);
        background: linear-gradient(135deg, #667eea, #764ba2);
      }

      /* Lightbox Modal */
      .lightbox-modal {
        display: none;
        position: fixed;
        z-index: 9999;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.95);
        backdrop-filter: blur(10px);
        animation: fadeIn 0.3s ease;
      }

      .lightbox-content {
        position: relative;
        max-width: 90vw;
        max-height: 90vh;
        margin: auto;
        top: 50%;
        transform: translateY(-50%);
        text-align: center;
      }

      .lightbox-image {
        max-width: 100%;
        max-height: 70vh;
        border-radius: 15px;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
        animation: scaleIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      }

      .lightbox-info {
        color: #fff;
        padding: 20px 0;
        animation: fadeInUp 0.4s ease 0.2s both;
      }

      .lightbox-title {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 10px;
      }

      .lightbox-description {
        font-size: 1.1rem;
        opacity: 0.8;
        max-width: 600px;
        margin: 0 auto;
      }

      .lightbox-close {
        position: absolute;
        top: 20px;
        right: 20px;
        color: #fff;
        font-size: 30px;
        cursor: pointer;
        width: 50px;
        height: 50px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
      }

      .lightbox-close:hover {
        background: rgba(255, 255, 255, 0.2);
        transform: scale(1.1);
      }

      .lightbox-nav {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        color: #fff;
        font-size: 24px;
        cursor: pointer;
        width: 60px;
        height: 60px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
      }

      .lightbox-nav:hover {
        background: rgba(255, 255, 255, 0.2);
        transform: translateY(-50%) scale(1.1);
      }

      .lightbox-prev {
        left: 20px;
      }

      .lightbox-next {
        right: 20px;
      }

      /* Animations */
      @keyframes fadeIn {
        from {
          opacity: 0;
        }
        to {
          opacity: 1;
        }
      }

      @keyframes fadeInUp {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      @keyframes scaleIn {
        from {
          opacity: 0;
          transform: scale(0.9);
        }
        to {
          opacity: 1;
          transform: scale(1);
        }
      }

      /* Grid Layout (Alternative View) */
      .gallery-grid {
        display: none;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 30px;
        padding: 20px 0;
      }

      .gallery-grid.active {
        display: grid;
      }

      .gallery-swiper.hidden {
        display: none;
      }

      /* View Toggle */
      .view-toggle {
        display: flex;
        justify-content: center;
        gap: 10px;
        margin-bottom: 30px;
      }

      .toggle-btn {
        background: #fff;
        border: 2px solid #667eea;
        color: #667eea;
        padding: 10px 20px;
        border-radius: 25px;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .toggle-btn.active,
      .toggle-btn:hover {
        background: #667eea;
        color: #fff;
      }

      /* Responsive Design */
      @media (max-width: 768px) {
        .hero-title {
          font-size: 2.5rem;
        }

        .hero-subtitle {
          font-size: 1.1rem;
        }

        .hero-stats {
          gap: 20px;
        }

        .stat-number {
          font-size: 2rem;
        }

        .filter-tabs {
          gap: 8px;
        }

        .filter-tab {
          padding: 10px 20px;
          font-size: 0.9rem;
        }

        .gallery-card {
          height: 280px;
        }

        .gallery-grid {
          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
          gap: 20px;
        }

        .lightbox-nav {
          width: 50px;
          height: 50px;
          font-size: 20px;
        }

        .lightbox-close {
          width: 40px;
          height: 40px;
          font-size: 24px;
        }
      }

      @media (max-width: 480px) {
        .hero-title {
          font-size: 2rem;
        }

        .filter-tabs {
          flex-direction: column;
          align-items: center;
        }

        .filter-tab {
          width: 200px;
        }

        .gallery-grid {
          grid-template-columns: 1fr;
        }

        .view-toggle {
          flex-direction: column;
          align-items: center;
        }
      }
    </style>
  </head>
  <body>
    <!-- Hero Section -->
    <section class="hero-section">
      <div class="hero-content">
        <h1 class="hero-title">ANSISS Gallery</h1>
        <p class="hero-subtitle">
          Discover the moments that define our academic excellence and
          institutional heritage
        </p>
        <div class="hero-stats">
          <div class="stat-item">
            <span class="stat-number">500+</span>
            <span class="stat-label">Events</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">50+</span>
            <span class="stat-label">Years</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">1000+</span>
            <span class="stat-label">Memories</span>
          </div>
        </div>
      </div>
    </section>

    <!-- Filter Section -->
    <section class="filter-section">
      <div class="filter-container">
        <div class="filter-tabs">
          <button class="filter-tab active" data-filter="all">
            All Events
          </button>
          <button class="filter-tab" data-filter="events">Conferences</button>
          <button class="filter-tab" data-filter="seminars">Seminars</button>
          <button class="filter-tab" data-filter="campus">Campus Life</button>
          <button class="filter-tab" data-filter="facilities">
            Facilities
          </button>
        </div>
      </div>
    </section>

    <!-- Gallery Section -->
    <section class="gallery-section">
      <div class="gallery-container">
        <!-- View Toggle -->
        <div class="view-toggle">
          <button class="toggle-btn active" data-view="slider">
            <i class="fas fa-images"></i>
            Slider View
          </button>
          <button class="toggle-btn" data-view="grid">
            <i class="fas fa-th"></i>
            Grid View
          </button>
        </div>

        <!-- Swiper Gallery -->
        <div class="swiper gallery-swiper">
          <div class="swiper-wrapper" id="galleryWrapper">
            <!-- Slides will be dynamically generated -->
          </div>

          <!-- Navigation -->
          <div class="swiper-button-next"></div>
          <div class="swiper-button-prev"></div>

          <!-- Pagination -->
          <div class="swiper-pagination"></div>
        </div>

        <!-- Grid Gallery -->
        <div class="gallery-grid" id="galleryGrid">
          <!-- Grid items will be dynamically generated -->
        </div>
      </div>
    </section>

    <!-- Lightbox Modal -->
    <div id="lightboxModal" class="lightbox-modal">
      <div class="lightbox-content">
        <span class="lightbox-close">&times;</span>
        <img id="lightboxImage" class="lightbox-image" src="" alt="" />
        <div class="lightbox-info">
          <h3 id="lightboxTitle" class="lightbox-title"></h3>
          <p id="lightboxDescription" class="lightbox-description"></p>
        </div>
        <div class="lightbox-nav lightbox-prev">
          <i class="fas fa-chevron-left"></i>
        </div>
        <div class="lightbox-nav lightbox-next">
          <i class="fas fa-chevron-right"></i>
        </div>
      </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/swiper/8.4.5/swiper-bundle.min.js"></script>
    <script>
      // Gallery Data
      const galleryData = [
        {
          id: 1,
          src: "/img/gallery18.jpeg",
          title: "Academic Excellence Summit",
          description:
            "Annual academic conference bringing together scholars and researchers from across the region.",
          category: "events",
        },
        {
          id: 2,
          src: "/img/gallery19.jpeg",
          title: "International Conference",
          description:
            "Global symposium on social studies and contemporary research methodologies.",
          category: "events",
        },
        {
          id: 3,
          src: "/img/gallery11.jpeg",
          title: "Research Methodology Seminar",
          description:
            "Comprehensive workshop on advanced research techniques and academic writing.",
          category: "seminars",
        },
        {
          id: 4,
          src: "/img/gallery12.jpeg",
          title: "Faculty Development Workshop",
          description:
            "Professional development program for faculty members and researchers.",
          category: "seminars",
        },
        {
          id: 5,
          src: "/img/gallery14.jpeg",
          title: "Cultural Integration Program",
          description:
            "Annual cultural celebration showcasing diverse traditions and academic achievements.",
          category: "events",
        },
        {
          id: 6,
          src: "/img/gallery15.jpeg",
          title: "PhD Graduation Ceremony",
          description:
            "Prestigious ceremony celebrating the achievements of our doctoral graduates.",
          category: "events",
        },
        {
          id: 7,
          src: "/img/gallery16.jpeg",
          title: "Campus Life Moments",
          description:
            "Daily life and interactions within our vibrant academic community.",
          category: "campus",
        },
        {
          id: 8,
          src: "/img/picture1.jpg",
          title: "Modern Library Complex",
          description:
            "State-of-the-art library facility with extensive digital and physical collections.",
          category: "facilities",
        },
        {
          id: 9,
          src: "/img/picture2.jpg",
          title: "Computer Science Laboratory",
          description:
            "Advanced computing facility equipped with latest technology for research.",
          category: "facilities",
        },
        {
          id: 10,
          src: "/img/picture3.jpg",
          title: "Administrative Building",
          description:
            "Historic administrative building serving as the heart of institutional operations.",
          category: "campus",
        },
        {
          id: 11,
          src: "/img/picture5.jpg",
          title: "Conference Hall",
          description:
            "Modern conference facility hosting national and international events.",
          category: "facilities",
        },
        {
          id: 12,
          src: "/img/picture6.jpg",
          title: "Botanical Gardens",
          description:
            "Serene green spaces providing a peaceful environment for study and reflection.",
          category: "campus",
        },
        {
          id: 13,
          src: "/img/picture7.jpg",
          title: "Research Innovation Center",
          description:
            "Cutting-edge research facility fostering innovation and academic excellence.",
          category: "facilities",
        },
      ];

      let swiper;
      let currentFilter = "all";
      let currentView = "slider";
      let filteredData = [...galleryData];
      let currentLightboxIndex = 0;

      // Initialize everything
      document.addEventListener("DOMContentLoaded", function () {
        initializeGallery();
        setupEventListeners();
        initializeSwiper();
      });

      function initializeGallery() {
        renderGallery();
      }

      function renderGallery() {
        const wrapper = document.getElementById("galleryWrapper");
        const grid = document.getElementById("galleryGrid");

        // Clear existing content
        wrapper.innerHTML = "";
        grid.innerHTML = "";

        // Filter data based on current filter
        filteredData =
          currentFilter === "all"
            ? [...galleryData]
            : galleryData.filter((item) => item.category === currentFilter);

        // Render slides
        filteredData.forEach((item, index) => {
          const slideHTML = createSlideHTML(item, index);
          const gridHTML = createGridHTML(item, index);

          wrapper.insertAdjacentHTML("beforeend", slideHTML);
          grid.insertAdjacentHTML("beforeend", gridHTML);
        });

        // Update swiper
        if (swiper) {
          swiper.update();
        }
      }

      function createSlideHTML(item, index) {
        return `
          <div class="swiper-slide gallery-slide">
            <div class="gallery-card" onclick="openLightbox(${index})">
              <img src="${item.src}" alt="${item.title}" class="gallery-image" loading="lazy">
              <div class="gallery-overlay">
                <div class="gallery-info">
                  <div class="gallery-category">${item.category}</div>
                  <h3 class="gallery-title">${item.title}</h3>
                  <p class="gallery-description">${item.description}</p>
                  <button class="view-button">
                    <i class="fas fa-eye"></i>
                    View Details
                  </button>
                </div>
              </div>
            </div>
          </div>
        `;
      }

      function createGridHTML(item, index) {
        return `
          <div class="gallery-card" onclick="openLightbox(${index})">
            <img src="${item.src}" alt="${item.title}" class="gallery-image" loading="lazy">
            <div class="gallery-overlay">
              <div class="gallery-info">
                <div class="gallery-category">${item.category}</div>
                <h3 class="gallery-title">${item.title}</h3>
                <p class="gallery-description">${item.description}</p>
                <button class="view-button">
                  <i class="fas fa-eye"></i>
                  View Details
                </button>
              </div>
            </div>
          </div>
        `;
      }

      function initializeSwiper() {
        swiper = new Swiper(".gallery-swiper", {
          slidesPerView: 1,
          spaceBetween: 30,
          loop: true,
          autoplay: {
            delay: 4000,
            disableOnInteraction: false,
          },
          pagination: {
            el: ".swiper-pagination",
            clickable: true,
          },
          navigation: {
            nextEl: ".swiper-button-next",
            prevEl: ".swiper-button-prev",
          },
          breakpoints: {
            640: {
              slidesPerView: 2,
              spaceBetween: 20,
            },
            768: {
              slidesPerView: 2,
              spaceBetween: 30,
            },
            1024: {
              slidesPerView: 3,
              spaceBetween: 30,
            },
            1200: {
              slidesPerView: 4,
              spaceBetween: 30,
            },
          },
          effect: "slide",
          speed: 600,
          grabCursor: true,
        });
      }

      function setupEventListeners() {
        // Filter buttons
        document.querySelectorAll(".filter-tab").forEach((button) => {
          button.addEventListener("click", function () {
            // Update active filter
            document
              .querySelectorAll(".filter-tab")
              .forEach((btn) => btn.classList.remove("active"));
            this.classList.add("active");

            currentFilter = this.dataset.filter;
            renderGallery();

            // Add smooth transition effect
            const gallerySection = document.querySelector(".gallery-section");
            gallerySection.style.opacity = "0.7";
            setTimeout(() => {
              gallerySection.style.opacity = "1";
            }, 300);
          });
        });

        // View toggle buttons
        document.querySelectorAll(".toggle-btn").forEach((button) => {
          button.addEventListener("click", function () {
            document
              .querySelectorAll(".toggle-btn")
              .forEach((btn) => btn.classList.remove("active"));
            this.classList.add("active");

            currentView = this.dataset.view;
            toggleView();
          });
        });

        // Lightbox controls
        document
          .querySelector(".lightbox-close")
          .addEventListener("click", closeLightbox);
        document
          .querySelector(".lightbox-prev")
          .addEventListener("click", prevLightboxImage);
        document
          .querySelector(".lightbox-next")
          .addEventListener("click", nextLightboxImage);

        // Close lightbox on outside click
        document
          .getElementById("lightboxModal")
          .addEventListener("click", function (e) {
            if (e.target === this) {
              closeLightbox();
            }
          });

        // Keyboard navigation for lightbox
        document.addEventListener("keydown", function (e) {
          const modal = document.getElementById("lightboxModal");
          if (modal.style.display === "block") {
            switch (e.key) {
              case "Escape":
                closeLightbox();
                break;
              case "ArrowLeft":
                prevLightboxImage();
                break;
              case "ArrowRight":
                nextLightboxImage();
                break;
            }
          }
        });
      }

      function toggleView() {
        const swiperElement = document.querySelector(".gallery-swiper");
        const gridElement = document.querySelector(".gallery-grid");

        if (currentView === "slider") {
          swiperElement.classList.remove("hidden");
          gridElement.classList.remove("active");
          if (swiper) {
            swiper.update();
          }
        } else {
          swiperElement.classList.add("hidden");
          gridElement.classList.add("active");
        }
      }

      function openLightbox(index) {
        currentLightboxIndex = index;
        const item = filteredData[index];

        const modal = document.getElementById("lightboxModal");
        const image = document.getElementById("lightboxImage");
        const title = document.getElementById("lightboxTitle");
        const description = document.getElementById("lightboxDescription");

        image.src = item.src;
        image.alt = item.title;
        title.textContent = item.title;
        description.textContent = item.description;

        modal.style.display = "block";
        document.body.style.overflow = "hidden";

        // Add entrance animation
        setTimeout(() => {
          modal.style.opacity = "1";
        }, 10);
      }

      function closeLightbox() {
        const modal = document.getElementById("lightboxModal");
        modal.style.display = "none";
        document.body.style.overflow = "auto";
      }

      function prevLightboxImage() {
        currentLightboxIndex =
          (currentLightboxIndex - 1 + filteredData.length) %
          filteredData.length;
        updateLightboxContent();
      }

      function nextLightboxImage() {
        currentLightboxIndex = (currentLightboxIndex + 1) % filteredData.length;
        updateLightboxContent();
      }

      function updateLightboxContent() {
        const item = filteredData[currentLightboxIndex];
        const image = document.getElementById("lightboxImage");
        const title = document.getElementById("lightboxTitle");
        const description = document.getElementById("lightboxDescription");

        // Add fade effect
        image.style.opacity = "0";

        setTimeout(() => {
          image.src = item.src;
          image.alt = item.title;
          title.textContent = item.title;
          description.textContent = item.description;
          image.style.opacity = "1";
        }, 150);
      }

      // Intersection Observer for animations
      const observerOptions = {
        threshold: 0.1,
        rootMargin: "0px 0px -50px 0px",
      };

      const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.style.opacity = "1";
            entry.target.style.transform = "translateY(0)";
          }
        });
      }, observerOptions);

      // Observe gallery cards for scroll animations
      setTimeout(() => {
        document.querySelectorAll(".gallery-card").forEach((card) => {
          card.style.opacity = "0";
          card.style.transform = "translateY(30px)";
          card.style.transition = "all 0.6s cubic-bezier(0.4, 0, 0.2, 1)";
          observer.observe(card);
        });
      }, 100);

      // Smooth scroll enhancement
      document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
        anchor.addEventListener("click", function (e) {
          e.preventDefault();
          const target = document.querySelector(this.getAttribute("href"));
          if (target) {
            target.scrollIntoView({
              behavior: "smooth",
              block: "start",
            });
          }
        });
      });

      // Loading performance optimization
      function lazyLoadImages() {
        const images = document.querySelectorAll('img[loading="lazy"]');

        if ("IntersectionObserver" in window) {
          const imageObserver = new IntersectionObserver(
            (entries, observer) => {
              entries.forEach((entry) => {
                if (entry.isIntersecting) {
                  const img = entry.target;
                  if (img.dataset.src) {
                    img.src = img.dataset.src;
                    img.removeAttribute("data-src");
                  }
                  img.classList.remove("lazy");
                  observer.unobserve(img);
                }
              });
            }
          );

          images.forEach((img) => imageObserver.observe(img));
        } else {
          // Fallback for browsers without IntersectionObserver
          images.forEach((img) => {
            if (img.dataset.src) {
              img.src = img.dataset.src;
            }
          });
        }
      }

      // Initialize lazy loading
      lazyLoadImages();

      // Add parallax effect to hero section
      window.addEventListener("scroll", () => {
        const scrolled = window.pageYOffset;
        const heroSection = document.querySelector(".hero-section");
        if (heroSection) {
          heroSection.style.transform = `translateY(${scrolled * 0.5}px)`;
        }
      });

      // Performance optimization: Debounced resize handler
      function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
          const later = () => {
            clearTimeout(timeout);
            func(...args);
          };
          clearTimeout(timeout);
          timeout = setTimeout(later, wait);
        };
      }

      // Handle window resize
      const handleResize = debounce(() => {
        if (swiper) {
          swiper.update();
        }
      }, 250);

      window.addEventListener("resize", handleResize);

      // Add touch gesture support for mobile
      let touchStartX = 0;
      let touchEndX = 0;

      document.addEventListener("touchstart", (e) => {
        touchStartX = e.changedTouches[0].screenX;
      });

      document.addEventListener("touchend", (e) => {
        touchEndX = e.changedTouches[0].screenX;
        handleSwipeGesture();
      });

      function handleSwipeGesture() {
        const modal = document.getElementById("lightboxModal");
        if (modal.style.display === "block") {
          const swipeThreshold = 50;
          const diff = touchStartX - touchEndX;

          if (Math.abs(diff) > swipeThreshold) {
            if (diff > 0) {
              nextLightboxImage();
            } else {
              prevLightboxImage();
            }
          }
        }
      }

      // Add loading indicator
      function showLoader() {
        const loader = document.createElement("div");
        loader.id = "gallery-loader";
        loader.innerHTML = `
          <div style="
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(102, 126, 234, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            color: white;
            font-size: 18px;
          ">
            <div style="text-align: center;">
              <div style="
                width: 50px;
                height: 50px;
                border: 3px solid rgba(255,255,255,0.3);
                border-radius: 50%;
                border-top: 3px solid white;
                animation: spin 1s linear infinite;
                margin: 0 auto 20px;
              "></div>
              Loading Gallery...
            </div>
          </div>
        `;

        // Add spin animation
        const style = document.createElement("style");
        style.textContent = `
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `;
        document.head.appendChild(style);
        document.body.appendChild(loader);

        // Remove loader after content loads
        setTimeout(() => {
          const loaderElement = document.getElementById("gallery-loader");
          if (loaderElement) {
            loaderElement.remove();
          }
        }, 1000);
      }

      // Show loader on initial load
      window.addEventListener("load", () => {
        setTimeout(showLoader, 100);
      });
    </script>
  </body>
</html>

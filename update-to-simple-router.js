const fs = require('fs');
const path = require('path');

function updateToSimpleRouter() {
    const htmlDir = 'html';
    const files = fs.readdirSync(htmlDir);

    files.forEach(file => {
        if (file.endsWith('.html')) {
            const filePath = path.join(htmlDir, file);
            let content = fs.readFileSync(filePath, 'utf8');
            
            // Replace old router reference with simple router
            const oldRouter = '<script src="/routes/index.js" defer></script>';
            const newRouter = '<script src="/routes/simple-router.js" defer></script>';
            
            if (content.includes(oldRouter)) {
                content = content.replace(oldRouter, newRouter);
                fs.writeFileSync(filePath, content);
                console.log(`Updated: ${file}`);
            }
        }
    });

    console.log('\n✅ All HTML files updated to use simple-router.js!');
    console.log('📁 Now you only need 1 file: /routes/simple-router.js');
}

updateToSimpleRouter();

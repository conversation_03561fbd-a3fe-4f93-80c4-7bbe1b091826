// Simple SPA Router - All in One File
class SimpleRouter {
  constructor() {
    this.routes = new Map();
    this.init();
    this.loadRoutes();
  }

  loadRoutes() {
    // All routes in one place
    const routes = [
      { path: "/", file: "homepage.html", title: "ANSISS - Home" },
      { path: "/about", file: "about.html", title: "About Us - ANSISS" },
      {
        path: "/bocmembers",
        file: "bocmembers.html",
        title: "BOC Members - ANSISS",
      },
      {
        path: "/facultymember",
        file: "facultymember.html",
        title: "Faculty Members - ANSISS",
      },
      {
        path: "/doctoral_fellow",
        file: "doctoral_fellow.html",
        title: "PhD Scholars - ANSISS",
      },
      {
        path: "/director_tenior",
        file: "director_tenior.html",
        title: "Director Tenure - ANSISS",
      },
      {
        path: "/Director_desk",
        file: "Director_desk.html",
        title: "Director Desk - ANSISS",
      },
      {
        path: "/administration",
        file: "administration.html",
        title: "Administration - ANSISS",
      },
      { path: "/campus", file: "campus.html", title: "Campus - ANSISS" },
      {
        path: "/ansissLibrary",
        file: "ansissLibrary.html",
        title: "Library - ANSISS",
      },
      {
        path: "/computerlab",
        file: "computerlab.html",
        title: "Computer Lab - ANSISS",
      },
      {
        path: "/facilites",
        file: "facilites.html",
        title: "Facilities - ANSISS",
      },
      {
        path: "/Phd_Program",
        file: "Phd_Program.html",
        title: "PhD Program - ANSISS",
      },
      {
        path: "/e_Content",
        file: "e_Content.html",
        title: "e-Content - ANSISS",
      },
      {
        path: "/completed_project",
        file: "completed_project.html",
        title: "Completed Projects - ANSISS",
      },
      {
        path: "/OnGoingProject",
        file: "OnGoingProject.html",
        title: "Ongoing Projects - ANSISS",
      },
      { path: "/Workshop", file: "Workshop.html", title: "Workshops - ANSISS" },
      {
        path: "/Dialogue_series",
        file: "Dialogue_series.html",
        title: "Dialogue Series - ANSISS",
      },
      {
        path: "/internal_seminar",
        file: "internal_seminar.html",
        title: "Internal Seminars - ANSISS",
      },
      {
        path: "/workstation",
        file: "workstation.html",
        title: "Work Station - ANSISS",
      },
      {
        path: "/annualReport",
        file: "annualReport.html",
        title: "Annual Report - ANSISS",
      },
      {
        path: "/workingPaper",
        file: "workingPaper.html",
        title: "Working Papers - ANSISS",
      },
      {
        path: "/newsLetter",
        file: "newsLetter.html",
        title: "Newsletters - ANSISS",
      },
      {
        path: "/journal_about",
        file: "journal_about.html",
        title: "Journal - ANSISS",
      },
      {
        path: "/journal_editorial_board",
        file: "journal_editorial_board.html",
        title: "Journal Editorial Board - ANSISS",
      },
      {
        path: "/journal_Subscription",
        file: "journal_Subscription.html",
        title: "Journal Subscription - ANSISS",
      },
      {
        path: "/journal_submitionOfPaper",
        file: "journal_submitionOfPaper.html",
        title: "Journal Paper Submission - ANSISS",
      },
      {
        path: "/journal_JSES_valume",
        file: "journal_JSES_valume.html",
        title: "Journal Indexing - ANSISS",
      },
      {
        path: "/journal_contactUs",
        file: "journal_contactUs.html",
        title: "Journal Contact Us - ANSISS",
      },

      {
        path: "/carrier-view",
        file: "carrier-view.html",
        title: "Career - ANSISS",
      },
      { path: "/gallary", file: "gallary.html", title: "Gallery - ANSISS" },
      { path: "/register", file: "register.html", title: "Register - ANSISS" },
      { path: "/posting", file: "posting.html", title: "Posting - ANSISS" },
      {
        path: "/qualification",
        file: "qualification.html",
        title: "Qualification - ANSISS",
      },
      { path: "/logout", file: "logout.html", title: "Logout - ANSISS" },
      {
        path: "/HallBooking",
        file: "HallBooking.html",
        title: "Hall Booking - ANSISS",
      },
      {
        path: "/politicalfaculty",
        file: "politicalfaculty.html",
        title: "Political Science Faculty - ANSISS",
      },
      {
        path: "/psychologyfaculty",
        file: "psychologyfaculty.html",
        title: "Psychology Faculty - ANSISS",
      },
      {
        path: "/sociologyfaculty",
        file: "sociologyfaculty.html",
        title: "Sociology Faculty - ANSISS",
      },
      {
        path: "/statisticsfaculty",
        file: "statisticsfaculty.html",
        title: "Statistics Faculty - ANSISS",
      },
      {
        path: "/cnvpsfaculty",
        file: "cnvpsfaculty.html",
        title: "CNVPS Faculty - ANSISS",
      },
      {
        path: "/geographyfaculty",
        file: "geographyfaculty.html",
        title: "Geography Faculty - ANSISS",
      },
    ];

    routes.forEach((route) => {
      this.routes.set(route.path, route);
    });
  }

  init() {
    // Handle browser navigation
    window.addEventListener("popstate", () => this.handleRoute());
    document.addEventListener("DOMContentLoaded", () => this.handleRoute());

    // Handle link clicks
    document.addEventListener("click", (e) => {
      const link = e.target.closest("a");
      if (
        link &&
        link.href &&
        link.href.startsWith(window.location.origin) &&
        !link.href.includes("#") &&
        !link.target &&
        !link.download
      ) {
        e.preventDefault();
        const url = new URL(link.href);
        this.navigate(url.pathname);
      }
    });
  }

  navigate(path) {
    history.pushState(null, null, path);
    this.handleRoute();
  }

  async handleRoute() {
    const path = window.location.pathname;
    const route = this.routes.get(path) || this.routes.get("/");

    try {
      this.showLoading();
      const response = await fetch(route.file);
      const html = await response.text();

      const parser = new DOMParser();
      const doc = parser.parseFromString(html, "text/html");

      // Extract only the main content, not the entire page
      const mainContent =
        doc.querySelector("main.main-content") ||
        doc.querySelector("main") ||
        doc.body;

      // Find the main content area in current page
      const currentMain =
        document.querySelector("main.main-content") ||
        document.querySelector("main");

      if (currentMain && mainContent) {
        // Replace only the main content, keeping header/footer
        currentMain.innerHTML = mainContent.innerHTML;
      } else {
        // Fallback: replace entire body if main content not found
        document.body.innerHTML = doc.body.innerHTML;
      }

      document.title = route.title;

      this.hideLoading();
      this.initPageScripts();
    } catch (error) {
      console.error("Error loading page:", error);
      this.hideLoading();
    }
  }

  showLoading() {
    let loader = document.getElementById("router-loader");
    if (!loader) {
      loader = document.createElement("div");
      loader.id = "router-loader";
      loader.innerHTML = `
        <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; 
                   background: rgba(255,255,255,0.9); display: flex; justify-content: center; 
                   align-items: center; z-index: 9999;">
          <div style="text-align: center;">
            <div style="border: 4px solid #f3f3f3; border-top: 4px solid #3498db; 
                       border-radius: 50%; width: 40px; height: 40px; 
                       animation: spin 1s linear infinite; margin: 0 auto 10px;"></div>
            <p>Loading...</p>
          </div>
        </div>
        <style>@keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }</style>
      `;
      document.body.appendChild(loader);
    }
    loader.style.display = "flex";
  }

  hideLoading() {
    const loader = document.getElementById("router-loader");
    if (loader) loader.style.display = "none";
  }

  initPageScripts() {
    // Trigger event for other scripts
    window.dispatchEvent(
      new CustomEvent("pageLoaded", {
        detail: { path: window.location.pathname },
      })
    );
  }
}

// Initialize the router
const router = new SimpleRouter();
window.router = router;

const express = require("express");
const path = require("path");

const app = express();

// Set port from environment variable or default to 3001
const PORT = process.env.PORT || 3001;

// Serve static files from public directory
app.use(express.static(path.join(__dirname, "public")));

// Serve HTML files from a new 'html' directory
app.use(express.static(path.join(__dirname, "html")));

// Serve client-side routing files from routes directory
app.use("/routes", express.static(path.join(__dirname, "routes")));

// Fallback to serve index.html for any route
app.get("*", (req, res) => {
  res.sendFile(path.join(__dirname, "html", "index.html"));
});

app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});

module.exports = app;

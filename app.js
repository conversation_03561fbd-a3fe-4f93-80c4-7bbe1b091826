const express = require('express');
const path = require('path');

const app = express();

// Serve static files from public directory
app.use(express.static(path.join(__dirname, "public")));

// Serve HTML files from a new 'html' directory
app.use(express.static(path.join(__dirname, "html")));

// Fallback to serve index.html for any route
app.get("*", (req, res) => {
  res.sendFile(path.join(__dirname, "html", "index.html"));
});

const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});

module.exports = app;

<%- include('partials/header'); %>





    <meta name='robots' content='max-image-preview:large' />




    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            document.querySelector('.navbar-toggler').addEventListener('click', function () {

                var navbarNav = document.getElementById('bs-example-navbar-collapse-1');
                if (navbarNav.classList.contains('show')) {
                    navbarNav.classList.remove('show');
                } else {
                    navbarNav.classList.add('show');
                }
            });
        });
    </script>
    <style>
        .mt-5 {
            margin-top: 80px;
        }

        .mainContent {
            background: #fff;
            padding: 20px 20px;
            box-shadow: 0 0 7px 0 rgb(0 0 0 / 10%), 0 8px 7px 0 rgb(0 0 0 / 10%);
            border-radius: 15px;
            margin-bottom: 40px;
        }

        .mainContent .elementor-shortcode h3 {
            font-size: 30px;
            font-weight: 700;
            color: #03ad4f;
            margin-top: 0 !important;
        }

        #wpsm_panel-title {
            border: none !important;
        }

        #wpsm_accordion_118 .wpsm_panel-default {
            border: 1px solid transparent !important;
            border-radius: 15px !important;
        }

        #wpsm_accordion_118 .wpsm_panel-body {
            border: 1px solid #e8e8e8 !important;
            border-bottom-left-radius: 15px;
            border-bottom-right-radius: 15px;
        }

        th {
            font-size: 14px !important;
            background-color: #03ad28;
            text-align: left;
            color: #fff;
            font-weight: 600;
            border-color: #03ad2d !important;
        }

        td {
            font-size: 14px !important;
            font-weight: 400;
            border-color: #03ad3c !important;
        }

        .mainContent h2.elementor-heading-title {

            font-size: 24px;
            margin-bottom: 0;
            /*font-weight: 600;*/
            color: #03ad44;
            color: #ffffff;
            padding: 10px;
            background: green;
            text-align: center;
            border-top-left-radius: 10px;
            border-top-right-radius: 10px;

        }

        .mainContent .elementor-widget-container p,
        .mainContent .elementor-widget-container {
            font-size: 16px;
            font-weight: normal;
            line-height: 24px;
            color: #4c4c4c;
            margin-bottom: 30px;
        }

        .mainContent .elementor-widget-container img.attachment-large.size-large {
            border-radius: 10px;
            border: 1px solid #ccc;
        }

        .mainContent .elementor-column.elementor-col-33 .elementor-widget:not(:last-child),
        .mainContent .elementor-widget-container {
            margin-bottom: 0;
        }

        .mainContent .elementor-column.elementor-col-33 .elementor-widget-wrap.elementor-element-populated {
            /*background: #ad0303;*/
            border-radius: 12px;
            padding: 0;
        }

        .mainContent .elementor-column.elementor-col-33 .elementor-widget-wrap.elementor-element-populated h2.elementor-heading-title {
            color: #ffffff;
            padding: 10px;
            background: #07c65d;
            text-align: center;
            border-top-left-radius: 10px;
            border-top-right-radius: 10px;
        }

        .mainContent ul#menu-iqac.menu {
            background: #7f7f7f;
            padding: 10px 10px;
            list-style: none;
            margin: 0;
            border-bottom-left-radius: 10px;
            border-bottom-right-radius: 10px;
        }

        .mainContent ul#menu-iqac.menu a {
            padding: 6px 10px;
            color: #fff;
            /*font-weight: 600;*/
            display: block;
            margin-bottom: 5px;
        }

        .elementor img {
            margin: 6px 10px 0px;
        }

        .elementor .elementor-widget:not(.elementor-widget-text-editor):not(.elementor-widget-theme-post-content) figure {
            /* margin: 0; */
            width: 30%;
        }
    </style>

    <article class="container mt-5" id="post-1145" class="post-1145 page type-page status-publish hentry entry">

        <header class="entry-header alignwide">
            <h1 class="entry-title">Faculty</h1>
        </header><!-- .entry-header -->

        <div class="entry-content mainContent">
            <div data-elementor-type="wp-page" data-elementor-id="1145" class="elementor elementor-1145">
                <section
                    class="elementor-section elementor-top-section elementor-element elementor-element-69c4755 elementor-section-boxed elementor-section-height-default elementor-section-height-default"
                    data-id="69c4755" data-element_type="section">
                    <div class="elementor-container elementor-column-gap-default">
                        <div class="elementor-column elementor-col-33 elementor-top-column elementor-element elementor-element-890bc10"
                            data-id="890bc10" data-element_type="column">
                            <div class="elementor-widget-wrap elementor-element-populated">
                                <div class="elementor-element elementor-element-e30f090 elementor-widget elementor-widget-heading"
                                    data-id="e30f090" data-element_type="widget" data-widget_type="heading.default">
                                    <div class="elementor-widget-container">
                                        <style>
                                            /*! elementor - v3.15.0 - 09-08-2023 */
                                            .elementor-heading-title {
                                                padding: 0;
                                                margin: 0;
                                                line-height: 1
                                            }

                                            .elementor-widget-heading .elementor-heading-title[class*=elementor-size-]>a {
                                                color: inherit;
                                                font-size: inherit;
                                                line-height: inherit
                                            }

                                            .elementor-widget-heading .elementor-heading-title.elementor-size-small {
                                                font-size: 15px
                                            }

                                            .elementor-widget-heading .elementor-heading-title.elementor-size-medium {
                                                font-size: 19px
                                            }

                                            .elementor-widget-heading .elementor-heading-title.elementor-size-large {
                                                font-size: 29px
                                            }

                                            .elementor-widget-heading .elementor-heading-title.elementor-size-xl {
                                                font-size: 39px
                                            }

                                            .elementor-widget-heading .elementor-heading-title.elementor-size-xxl {
                                                font-size: 59px
                                            }
                                        </style>
                                        <h2 class="elementor-heading-title elementor-size-default">Faculty

                                        </h2>
                                    </div>
                                </div>
                                <div class="elementor-element elementor-element-59a87d3 elementor-widget elementor-widget-shortcode"
                                data-id="59a87d3" data-element_type="widget" data-widget_type="shortcode.default">
                                <div class="elementor-widget-container">
                                    <div class="elementor-shortcode">
                                        <div class="menu-faculty-container">
                                            <ul id="menu-faculty" class="menu">
                                                <li id="menu-item-10175"
                                                    class="menu-item menu-item-type-post_type menu-item-object-page menu-item-10175">
                                                    <a href="/facultymember"> DIVISION OF ECONOMICS AND AGRICULTURAL ECONOMICS</a>
                                                </li>
                                                <li id="menu-item-10171"
                                                class="menu-item menu-item-type-post_type menu-item-object-page menu-item-10171">
                                                <a href="/sociologyfaculty">DIVISION OF SOCIOLOGY AND SOCIAL
                                                    ANTHROPOLOGY</a></li>
                                                    <li id="menu-item-10192"
                                                    class="menu-item menu-item-type-post_type menu-item-object-page menu-item-10192">
                                                    <a href="/psychologyfaculty"> DIVISION OF SOCIAL PSYCHOLOGY</a>
                                                </li>
                                                <li id="menu-item-10172"
                                                class="menu-item menu-item-type-post_type menu-item-object-page menu-item-10172">
                                                <a href="/geographyfaculty"> CENTRE FOR SOCIAL GEOGRAPHY</a>
                                            </li>
                                            <li id="menu-item-10172"
                                            class="menu-item menu-item-type-post_type menu-item-object-page menu-item-10172">
                                            <a href="/cnvpsfaculty"> CENTRE FOR NON-VIOLENCE AND PEACE
                                             </a></li>
                                                <li id="menu-item-10192"
                                                    class="menu-item menu-item-type-post_type menu-item-object-page menu-item-10192">
                                                    <a href="/politicalfaculty"> DIVISION OF POLITICAL SCIENCE AND
                                                        PUBLIC ADMINISTRATION</a></li>
                                               
                                              
                                                <!-- <li id="menu-item-10172"
                                                    class="menu-item menu-item-type-post_type menu-item-object-page menu-item-10172">
                                                    <a href="/statisticsfaculty.ejs"> DIVISION OF STATISTICS</a>
                                                </li> -->
                                              
                                               
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                        <div class="elementor-column elementor-col-66 elementor-top-column elementor-element elementor-element-259d10a"
                            data-id="259d10a" data-element_type="column">
                            <div class="elementor-widget-wrap elementor-element-populated">
                                <div class="elementor-element elementor-element-cae3369 elementor-position-right elementor-vertical-align-top elementor-widget elementor-widget-image-box"
                                    data-id="cae3369" data-element_type="widget" data-widget_type="image-box.default">
                                    <div class="elementor-widget-container">

                                        <style>
                                            /*! elementor - v3.15.0 - 09-08-2023 */
                                            .elementor-widget-image-box .elementor-image-box-content {
                                                width: 100%
                                            }

                                            @media (min-width:768px) {

                                                .elementor-widget-image-box.elementor-position-left .elementor-image-box-wrapper,
                                                .elementor-widget-image-box.elementor-position-right .elementor-image-box-wrapper {
                                                    display: flex
                                                }

                                                .elementor-widget-image-box.elementor-position-right .elementor-image-box-wrapper {
                                                    text-align: right;
                                                    flex-direction: row-reverse
                                                }

                                                .elementor-widget-image-box.elementor-position-left .elementor-image-box-wrapper {
                                                    text-align: left;
                                                    flex-direction: row
                                                }

                                                .elementor-widget-image-box.elementor-position-top .elementor-image-box-img {
                                                    margin: auto
                                                }

                                                .elementor-widget-image-box.elementor-vertical-align-top .elementor-image-box-wrapper {
                                                    align-items: flex-start
                                                }

                                                .elementor-widget-image-box.elementor-vertical-align-middle .elementor-image-box-wrapper {
                                                    align-items: center
                                                }

                                                .elementor-widget-image-box.elementor-vertical-align-bottom .elementor-image-box-wrapper {
                                                    align-items: flex-end
                                                }
                                            }

                                            @media (max-width:767px) {
                                                .elementor-widget-image-box .elementor-image-box-img {
                                                    margin-left: auto !important;
                                                    margin-right: auto !important;
                                                    margin-bottom: 15px
                                                }
                                            }

                                            .elementor-widget-image-box .elementor-image-box-img {
                                                display: inline-block
                                            }

                                            .elementor-widget-image-box .elementor-image-box-title a {
                                                color: inherit
                                            }

                                            .elementor-widget-image-box .elementor-image-box-wrapper {
                                                text-align: center
                                            }

                                            .elementor-widget-image-box .elementor-image-box-description {
                                                margin: 0
                                            }
                                        </style>
                                        <div class="elementor-widget-container">
                                            <h2 class="elementor-heading-title elementor-size-default">DIVISION OF
                                                POLITICAL SCIENCE AND PUBLIC ADMINISTRATION</h2>
                                        </div>


                                        <div class="elementor-image-box-wrapper">
                                            <figure class="elementor-image-box-img"><a href="http://google.com"><img
                                                        fetchpriority="high" decoding="async" width="693" height="670"
                                                        src="" class="attachment-large size-large wp-image-1195" alt=""
                                                        style="width:100%;height:96.68%;max-width:693px" srcset=""
                                                        sizes="(max-width: 693px) 100vw, 693px" /></a></figure>
                                            <div class="elementor-image-box-content">
                                                <h3 class="elementor-image-box-title"><a href="http://google.com"><a
                                                            href="">...<br><span
                                                                style="font-size: 1.6rem;color: #233c6a;">Professor & Registrar</span></a></a></h3>
                                                <p class="elementor-image-box-description">
                                                <p>
                                                <p>
                                                    </b></p><br> <b> <a style="color:blue" href=""> View CV</a></b></p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <section
                                    class="elementor-section elementor-top-section elementor-element elementor-element-f4237df elementor-section-boxed elementor-section-height-default elementor-section-height-default"
                                    data-id="f4237df" data-element_type="section">
                                    <div class="elementor-container elementor-column-gap-default">
                                        <div class="elementor-column elementor-col-50 elementor-top-column elementor-element elementor-element-3611001"
                                            data-id="3611001" data-element_type="column">
                                            <div class="elementor-widget-wrap">
                                            </div>
                                        </div>
                                        <div class="elementor-column elementor-col-50 elementor-top-column elementor-element elementor-element-5c6046f"
                                            data-id="5c6046f" data-element_type="column">
                                            <div class="elementor-widget-wrap">
                                            </div>
                                        </div>
                                    </div>
                                </section>
                            </div>
                        </div><!-- .entry-content -->
                    </div>
    </article><!-- #post-1145 -->
    <%- include('partials/footer'); %>
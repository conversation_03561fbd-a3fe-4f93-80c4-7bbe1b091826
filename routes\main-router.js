// Main Router Class
class MainRouter {
    constructor() {
        this.routes = new Map();
        this.currentPage = '';
        this.basePath = '/html/';
        this.init();
    }

    init() {
        // Load route configuration
        this.loadRoutes();
        
        // Handle browser back/forward buttons
        window.addEventListener('popstate', (e) => {
            this.loadPage(window.location.pathname, false);
        });

        // Handle initial page load
        document.addEventListener('DOMContentLoaded', () => {
            this.loadPage(window.location.pathname, false);
        });

        // Intercept all link clicks
        document.addEventListener('click', (e) => {
            const link = e.target.closest('a');
            if (link && this.isInternalLink(link.href)) {
                e.preventDefault();
                const path = new URL(link.href).pathname;
                this.navigate(path);
            }
        });
    }

    loadRoutes() {
        // Load main routes
        if (window.routeConfig) {
            Object.values(window.routeConfig).forEach(route => {
                this.routes.set(route.path, route);
            });
        }

        // Load admin routes
        if (window.adminRoutes) {
            Object.values(window.adminRoutes).forEach(route => {
                this.routes.set(route.path, route);
            });
        }
    }

    isInternalLink(href) {
        try {
            const url = new URL(href);
            return url.origin === window.location.origin;
        } catch {
            return false;
        }
    }

    navigate(path) {
        this.loadPage(path, true);
    }

    async loadPage(path, updateHistory = true) {
        try {
            // Normalize path
            if (path === '/' || path === '') {
                path = '/';
            }

            // Find the route
            const route = this.routes.get(path);
            
            if (!route) {
                // Default to homepage if route not found
                const homeRoute = this.routes.get('/');
                if (homeRoute) {
                    await this.renderPage(homeRoute, updateHistory, '/');
                } else {
                    this.show404();
                }
                return;
            }

            await this.renderPage(route, updateHistory, path);

        } catch (error) {
            console.error('Error loading page:', error);
            this.showError('Failed to load page. Please try again.');
        }
    }

    async renderPage(route, updateHistory, path) {
        // Show loading indicator
        this.showLoading();

        try {
            // Fetch the HTML content
            const response = await fetch(route.file);
            if (!response.ok) {
                throw new Error(`Failed to load ${route.file}`);
            }

            const html = await response.text();
            
            // Parse the HTML and extract the body content
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');
            
            // Replace the current page content
            document.body.innerHTML = doc.body.innerHTML;
            
            // Update the page title
            document.title = route.title || 'ANSISS';

            // Update browser history
            if (updateHistory && path !== window.location.pathname) {
                history.pushState({ path }, '', path);
            }

            // Hide loading indicator
            this.hideLoading();

            // Reinitialize any scripts that need to run on the new page
            this.initPageScripts();

            this.currentPage = path;

        } catch (error) {
            this.hideLoading();
            throw error;
        }
    }

    showLoading() {
        // Create or show loading indicator
        let loader = document.getElementById('spa-loader');
        if (!loader) {
            loader = document.createElement('div');
            loader.id = 'spa-loader';
            loader.innerHTML = `
                <div style="
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(255,255,255,0.9);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    z-index: 9999;
                ">
                    <div style="text-align: center;">
                        <div style="
                            border: 4px solid #f3f3f3;
                            border-top: 4px solid #3498db;
                            border-radius: 50%;
                            width: 50px;
                            height: 50px;
                            animation: spin 1s linear infinite;
                            margin: 0 auto 15px;
                        "></div>
                        <p style="color: #666; font-size: 16px;">Loading...</p>
                    </div>
                </div>
                <style>
                    @keyframes spin {
                        0% { transform: rotate(0deg); }
                        100% { transform: rotate(360deg); }
                    }
                </style>
            `;
            document.body.appendChild(loader);
        }
        loader.style.display = 'flex';
    }

    hideLoading() {
        const loader = document.getElementById('spa-loader');
        if (loader) {
            loader.style.display = 'none';
        }
    }

    showError(message) {
        alert(message); // Simple error handling - you can improve this
    }

    show404() {
        document.body.innerHTML = `
            <div style="
                display: flex;
                justify-content: center;
                align-items: center;
                height: 100vh;
                text-align: center;
                background: #f8f9fa;
            ">
                <div>
                    <h1 style="font-size: 4rem; color: #e74c3c; margin-bottom: 20px;">404</h1>
                    <h2 style="color: #2c3e50; margin-bottom: 20px;">Page Not Found</h2>
                    <p style="color: #7f8c8d; margin-bottom: 30px;">The page you're looking for doesn't exist.</p>
                    <a href="/" style="
                        background: #3498db;
                        color: white;
                        padding: 12px 24px;
                        text-decoration: none;
                        border-radius: 5px;
                        display: inline-block;
                    ">Go Home</a>
                </div>
            </div>
        `;
    }

    initPageScripts() {
        // Reinitialize any JavaScript that needs to run on page load
        // For example, carousel, dropdowns, etc.
        
        // Trigger custom event for other scripts to listen to
        window.dispatchEvent(new CustomEvent('pageLoaded', { 
            detail: { path: this.currentPage } 
        }));
    }

    // Method to add routes dynamically
    addRoute(path, file, title) {
        this.routes.set(path, { path, file, title });
    }

    // Method to get current route
    getCurrentRoute() {
        return this.routes.get(this.currentPage);
    }

    // Method to get all routes
    getAllRoutes() {
        return Array.from(this.routes.values());
    }
}

// Initialize the router
const router = new MainRouter();

// Make router globally available
window.router = router;

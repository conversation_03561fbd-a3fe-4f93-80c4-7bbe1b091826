<title>Management &#8211; </title>

    <meta name='robots' content='max-image-preview:large' />

    <script>

        $(document).ready(function () {
            $('.dropdown-toggle').append('<span class="caret"></span>');

            $(".dropdown-menu li ").addClass("sub-dropdown");
            $(".dropdown-menu li ul").addClass("sub-dropdown-menu");
            $(".dropdown-menu li ul").removeClass("dropdown-menu");
            $(".sub-dropdown-menu li").removeClass("sub-dropdown");
            $(".sub-dropdown-menu li a").removeClass("dropdown-item");

        });

    </script>

    <style>
        .mt-5 {
            margin-top: 80px;
        }

        .mainContent {
            background: #fff;
            padding: 20px 20px;
            box-shadow: 0 0 7px 0 rgb(0 0 0 / 10%), 0 8px 7px 0 rgb(0 0 0 / 10%);
            border-radius: 15px;
            margin-bottom: 40px;
        }

        .mainContent .elementor-shortcode h3 {
            font-size: 30px;
            font-weight: 700;
            color: #ad0303;
            margin-top: 0 !important;
        }

        #wpsm_panel-title {
            border: none !important;
        }

        #wpsm_accordion_118 .wpsm_panel-default {
            border: 1px solid transparent !important;
            border-radius: 15px !important;
        }

        #wpsm_accordion_118 .wpsm_panel-body {
            border: 1px solid #e8e8e8 !important;
            border-bottom-left-radius: 15px;
            border-bottom-right-radius: 15px;
        }

        th {
            font-size: 14px !important;
            background-color: #ad0303;
            text-align: left;
            color: #fff;
            font-weight: 600;
            border-color: #ad0303 !important;
        }

        td {
            font-size: 14px !important;
            font-weight: 400;
            border-color: #ad0303 !important;
        }

        .mainContent h2.elementor-heading-title {

            font-size: 24px;
            margin-bottom: 0;
            color: #ad0303;
            color: #ffffff;
            padding: 10px;
            background: #233c6a;
            text-align: center;
            border-top-left-radius: 10px;
            border-top-right-radius: 10px;

        }

        .mainContent .elementor-widget-container p,
        .mainContent .elementor-widget-container {
            font-size: 16px;
            font-weight: normal;
            line-height: 24px;
            color: #4c4c4c;
            margin-bottom: 30px;
        }

        .mainContent .elementor-widget-container img.attachment-large.size-large {
            border-radius: 10px;
            border: 1px solid #ccc;
        }

        .mainContent .elementor-column.elementor-col-33 .elementor-widget:not(:last-child),
        .mainContent .elementor-widget-container {
            margin-bottom: 0;
        }

        .mainContent .elementor-column.elementor-col-33 .elementor-widget-wrap.elementor-element-populated {

            border-radius: 12px;
            padding: 0;
        }

        .mainContent .elementor-column.elementor-col-33 .elementor-widget-wrap.elementor-element-populated h2.elementor-heading-title {
            color: #ffffff;
            padding: 10px;
            background: #c60707;
            text-align: center;
            border-top-left-radius: 10px;
            border-top-right-radius: 10px;
        }

        .mainContent ul#menu-iqac.menu {
            background: #7f7f7f;
            padding: 10px 10px;
            list-style: none;
            margin: 0;
            border-bottom-left-radius: 10px;
            border-bottom-right-radius: 10px;
        }

        .mainContent ul#menu-iqac.menu a {
            padding: 6px 10px;
            color: #fff;
            display: block;
            margin-bottom: 5px;
        }

        .elementor img {
            margin: 6px 10px 0px;
        }

        .elementor .elementor-widget:not(.elementor-widget-text-editor):not(.elementor-widget-theme-post-content) figure {
            width: 30%;
        }
    </style>

    <article class="container mt-5" id="post-363" class="post-363 page type-page status-publish hentry entry">

        <header class="entry-header alignwide">
            <h1 class="entry-title">Members of the Board of Control</h1>
        </header>

        <div class="entry-content mainContent">
            <div data-elementor-type="wp-page" data-elementor-id="363" class="elementor elementor-363">
                <section
                    class="elementor-section elementor-top-section elementor-element elementor-element-898faee elementor-section-boxed elementor-section-height-default elementor-section-height-default"
                    data-id="898faee" data-element_type="section">
                    <div class="elementor-container elementor-column-gap-default">
                        <div class="elementor-column elementor-col-33 elementor-top-column elementor-element elementor-element-ea1d380"
                            data-id="ea1d380" data-element_type="column">
                            <div class="elementor-widget-wrap elementor-element-populated">
                                <div class="elementor-element elementor-element-7a35992 elementor-widget elementor-widget-heading"
                                    data-id="7a35992" data-element_type="widget" data-widget_type="heading.default">
                                    <div class="elementor-widget-container">
                                        <style>
                                            .elementor-heading-title {
                                                padding: 0;
                                                margin: 0;
                                                line-height: 1
                                            }

                                            .elementor-widget-heading .elementor-heading-title[class*=elementor-size-]>a {
                                                color: inherit;
                                                font-size: inherit;
                                                line-height: inherit
                                            }

                                            .elementor-widget-heading .elementor-heading-title.elementor-size-small {
                                                font-size: 15px
                                            }

                                            .elementor-widget-heading .elementor-heading-title.elementor-size-medium {
                                                font-size: 19px
                                            }

                                            .elementor-widget-heading .elementor-heading-title.elementor-size-large {
                                                font-size: 29px
                                            }

                                            .elementor-widget-heading .elementor-heading-title.elementor-size-xl {
                                                font-size: 39px
                                            }

                                            .elementor-widget-heading .elementor-heading-title.elementor-size-xxl {
                                                font-size: 59px
                                            }
                                        </style>
                                        <h2 class="elementor-heading-title elementor-size-default">Board of Governors
                                        </h2>
                                    </div>
                                </div>
                                <div class="elementor-element elementor-element-cedc9af elementor-widget elementor-widget-shortcode"
                                    data-id="cedc9af" data-element_type="widget" data-widget_type="shortcode.default">
                                    <div class="elementor-widget-container">
                                        <div class="elementor-shortcode">
                                            <div class="menu-about-us-container">
                                                <ul id="menu-about-us" class="menu">
                                                    <li id="menu-item-10782"
                                                        class="menu-item menu-item-type-custom menu-item-object-custom menu-item-10782">
                                                        <a href=""></a>
                                                    </li>

                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="elementor-column elementor-col-66 elementor-top-column elementor-element elementor-element-db76705"
                            data-id="db76705" data-element_type="column">
                            <div class="elementor-widget-wrap elementor-element-populated">
                                <div class="elementor-element elementor-element-b3ad22c elementor-widget elementor-widget-heading"
                                    data-id="b3ad22c" data-element_type="widget" data-widget_type="heading.default">
                                    <div class="elementor-widget-container">
                                        <h2 class="elementor-heading-title elementor-size-default">Members of the Board
                                            of Control</h2>
                                    </div>
                                </div>
                                <div class="elementor-element elementor-element-602987f elementor-widget elementor-widget-text-editor"
                                    data-id="602987f" data-element_type="widget" data-widget_type="text-editor.default">
                                    <div class="elementor-widget-container">
                                        <style>
                                            .elementor-widget-text-editor.elementor-drop-cap-view-stacked .elementor-drop-cap {
                                                background-color: #69727d;
                                                color: #fff
                                            }

                                            .elementor-widget-text-editor.elementor-drop-cap-view-framed .elementor-drop-cap {
                                                color: #69727d;
                                                border: 3px solid;
                                                background-color: transparent
                                            }

                                            .elementor-widget-text-editor:not(.elementor-drop-cap-view-default) .elementor-drop-cap {
                                                margin-top: 8px
                                            }

                                            .elementor-widget-text-editor:not(.elementor-drop-cap-view-default) .elementor-drop-cap-letter {
                                                width: 1em;
                                                height: 1em
                                            }

                                            .elementor-widget-text-editor .elementor-drop-cap {
                                                float: left;
                                                text-align: center;
                                                line-height: 1;
                                                font-size: 50px
                                            }

                                            .elementor-widget-text-editor .elementor-drop-cap-letter {
                                                display: inline-block
                                            }
                                        </style>
                                        <h3 style="text-align: center;"> </h3>
                                    </div>
                                </div>
                                <div class="elementor-element elementor-element-cf74d2a elementor-widget elementor-widget-heading"
                                    data-id="cf74d2a" data-element_type="widget" data-widget_type="heading.default">
                                    <div class="elementor-widget-container">
                                        <h2 class="elementor-heading-title elementor-size-default">Chairman</h2>
                                    </div>
                                </div>
                                <div class="elementor-element elementor-element-4ed5703 elementor-position-left elementor-vertical-align-top elementor-widget elementor-widget-image-box"
                                    data-id="4ed5703" data-element_type="widget" data-widget_type="image-box.default">
                                    <div class="elementor-widget-container">
                                        <style>
                                            .elementor-widget-image-box .elementor-image-box-content {
                                                width: 100%
                                            }

                                            @media (min-width:768px) {

                                                .elementor-widget-image-box.elementor-position-left .elementor-image-box-wrapper,
                                                .elementor-widget-image-box.elementor-position-right .elementor-image-box-wrapper {
                                                    display: flex
                                                }

                                                .elementor-widget-image-box.elementor-position-right .elementor-image-box-wrapper {
                                                    text-align: right;
                                                    flex-direction: row-reverse
                                                }

                                                .elementor-widget-image-box.elementor-position-left .elementor-image-box-wrapper {
                                                    text-align: left;
                                                    flex-direction: row
                                                }

                                                .elementor-widget-image-box.elementor-position-top .elementor-image-box-img {
                                                    margin: auto
                                                }

                                                .elementor-widget-image-box.elementor-vertical-align-top .elementor-image-box-wrapper {
                                                    align-items: flex-start
                                                }

                                                .elementor-widget-image-box.elementor-vertical-align-middle .elementor-image-box-wrapper {
                                                    align-items: center
                                                }

                                                .elementor-widget-image-box.elementor-vertical-align-bottom .elementor-image-box-wrapper {
                                                    align-items: flex-end
                                                }
                                            }

                                            @media (max-width:767px) {
                                                .elementor-widget-image-box .elementor-image-box-img {
                                                    margin-left: auto !important;
                                                    margin-right: auto !important;
                                                    margin-bottom: 15px
                                                }
                                            }

                                            .elementor-widget-image-box .elementor-image-box-img {
                                                display: inline-block
                                            }

                                            .elementor-widget-image-box .elementor-image-box-title a {
                                                color: inherit
                                            }

                                            .elementor-widget-image-box .elementor-image-box-wrapper {
                                                text-align: center
                                            }

                                            .elementor-widget-image-box .elementor-image-box-description {
                                                margin: 0
                                            }
                                        </style>
                                        <div class="elementor-image-box-wrapper">
                                            <figure class="elementor-image-box-img"><img fetchpriority="high"
                                                    decoding="async" width="195" height="259" src="/img/director.jpg"
                                                    class="attachment-full size-full wp-image-7685" alt=""
                                                    style="width:100%;height:132.82%;max-width:195px" /></figure>
                                            <div class="elementor-image-box-content">
                                                <p class="elementor-image-box-description">
                                                <p>Department of Education, Govt. of Bihar, Patna<br>
                                                </p>
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="elementor-element elementor-element-80018ab elementor-widget elementor-widget-heading"
                                    data-id="80018ab" data-element_type="widget" data-widget_type="heading.default">
                                    <div class="elementor-widget-container">
                                        <h2 class="elementor-heading-title elementor-size-default">Member ICSSR Nominee
                                        </h2>
                                    </div>
                                </div>
                                <div class="elementor-element elementor-element-ec2fb95 elementor-position-left elementor-vertical-align-top elementor-widget elementor-widget-image-box"
                                    data-id="ec2fb95" data-element_type="widget" data-widget_type="image-box.default">
                                    <div class="elementor-widget-container">
                                        <div class="elementor-image-box-wrapper">
                                            <figure class="elementor-image-box-img"><img decoding="async" width="300"
                                                    height="417" src="" class="attachment-full size-full wp-image-367"
                                                    alt="" style="width:100%;height:139%;max-width:300px"
                                                    sizes="(max-width: 300px) 100vw, 300px" /></figure>
                                            <div class="elementor-image-box-content">
                                                <h3 class="elementor-image-box-title">To Be Nominated</h3>
                                                <p class="elementor-image-box-description"></p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="elementor-element elementor-element-2b0738d elementor-widget elementor-widget-heading"
                                    data-id="2b0738d" data-element_type="widget" data-widget_type="heading.default">
                                    <div class="elementor-widget-container">
                                        <h2 class="elementor-heading-title elementor-size-default">Director, Member
                                            Secretary</h2>
                                    </div>
                                </div>
                                <div class="elementor-element elementor-element-dbaf19a elementor-position-left elementor-vertical-align-top elementor-widget elementor-widget-image-box"
                                    data-id="dbaf19a" data-element_type="widget" data-widget_type="image-box.default">
                                    <div class="elementor-widget-container">
                                        <div class="elementor-image-box-wrapper">
                                            <figure class="elementor-image-box-img"><img decoding="async" width="150"
                                                    height="131" src="/img/director.jpg"
                                                    class="attachment-thumbnail size-thumbnail wp-image-115" alt=""
                                                    style="width:100%;height:59.82%;max-width:219px" /></figure>
                                            <div class="elementor-image-box-content">
                                                <h3 class="elementor-image-box-title">Dr. S. Siddharth</h3>
                                                <p class="elementor-image-box-description">Institute for Social and
                                                    Economic Change<br>
                                                    Director, A N Sinha Institute of Social Studies, Patna</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="elementor-element elementor-element-ad583d4 elementor-widget elementor-widget-heading"
                                    data-id="ad583d4" data-element_type="widget" data-widget_type="heading.default">
                                    <div class="elementor-widget-container">
                                        <h2 class="elementor-heading-title elementor-size-default">Members</h2>
                                    </div>
                                </div>
                                <div class="elementor-element elementor-element-c4a1cc5 elementor-position-left elementor-vertical-align-top elementor-widget elementor-widget-image-box"
                                    data-id="c4a1cc5" data-element_type="widget" data-widget_type="image-box.default">
                                    <div class="elementor-widget-container">
                                        <div class="elementor-image-box-wrapper">
                                            <figure class="elementor-image-box-img"><img loading="lazy" decoding="async"
                                                    width="150" height="150" src=""
                                                    class="attachment-thumbnail size-thumbnail wp-image-443" alt=""
                                                    style="width:100%;height:110%;max-width:250px" /></figure>
                                            <div class="elementor-image-box-content">
                                                <h3 class="elementor-image-box-title"></h3>
                                                <p class="elementor-image-box-description"><br>
                                                    Additional Chief Secretary </p>Department of Education Government of
                                                Bihar
                                            </div>
                                        </div>
                                    </div>
                                </div>

                </section>
                <section
                    class="elementor-section elementor-top-section elementor-element elementor-element-ee05b89 elementor-section-boxed elementor-section-height-default elementor-section-height-default"
                    data-id="ee05b89" data-element_type="section">
                    <div class="elementor-container elementor-column-gap-default">
                        <div class="elementor-column elementor-col-100 elementor-top-column elementor-element elementor-element-6e0acd5"
                            data-id="6e0acd5" data-element_type="column">
                            <div class="elementor-widget-wrap">
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </div>

    </article>
    
.elementor-widget-heading .elementor-heading-title {
    color: var( --e-global-color-primary );
    font-family: var( --e-global-typography-primary-font-family ), Sans-serif;
    font-weight: var( --e-global-typography-primary-font-weight );
}

.elementor-widget-image .widget-image-caption {
    color: var( --e-global-color-text );
    font-family: var( --e-global-typography-text-font-family ), Sans-serif;
    font-weight: var( --e-global-typography-text-font-weight );
}

.elementor-widget-text-editor {
    color: var( --e-global-color-text );
    font-family: var( --e-global-typography-text-font-family ), Sans-serif;
    font-weight: var( --e-global-typography-text-font-weight );
}

.elementor-widget-text-editor.elementor-drop-cap-view-stacked .elementor-drop-cap {
    background-color: var( --e-global-color-primary );
}

.elementor-widget-text-editor.elementor-drop-cap-view-framed .elementor-drop-cap, .elementor-widget-text-editor.elementor-drop-cap-view-default .elementor-drop-cap {
    color: var( --e-global-color-primary );
    border-color: var( --e-global-color-primary );
}

.elementor-widget-button .elementor-button {
    font-family: var( --e-global-typography-accent-font-family ), Sans-serif;
    font-weight: var( --e-global-typography-accent-font-weight );
    background-color: var( --e-global-color-accent );
}

.elementor-widget-divider {
    --divider-color: var( --e-global-color-secondary );
}

.elementor-widget-divider .elementor-divider__text {
    color: var( --e-global-color-secondary );
    font-family: var( --e-global-typography-secondary-font-family ), Sans-serif;
    font-weight: var( --e-global-typography-secondary-font-weight );
}

.elementor-widget-divider.elementor-view-stacked .elementor-icon {
    background-color: var( --e-global-color-secondary );
}

.elementor-widget-divider.elementor-view-framed .elementor-icon, .elementor-widget-divider.elementor-view-default .elementor-icon {
    color: var( --e-global-color-secondary );
    border-color: var( --e-global-color-secondary );
}

.elementor-widget-divider.elementor-view-framed .elementor-icon, .elementor-widget-divider.elementor-view-default .elementor-icon svg {
    fill: var( --e-global-color-secondary );
}

.elementor-widget-image-box .elementor-image-box-title {
    color: var( --e-global-color-primary );
    font-family: var( --e-global-typography-primary-font-family ), Sans-serif;
    font-weight: var( --e-global-typography-primary-font-weight );
}

.elementor-widget-image-box .elementor-image-box-description {
    color: var( --e-global-color-text );
    font-family: var( --e-global-typography-text-font-family ), Sans-serif;
    font-weight: var( --e-global-typography-text-font-weight );
}

.elementor-widget-icon.elementor-view-stacked .elementor-icon {
    background-color: var( --e-global-color-primary );
}

.elementor-widget-icon.elementor-view-framed .elementor-icon, .elementor-widget-icon.elementor-view-default .elementor-icon {
    color: var( --e-global-color-primary );
    border-color: var( --e-global-color-primary );
}

.elementor-widget-icon.elementor-view-framed .elementor-icon, .elementor-widget-icon.elementor-view-default .elementor-icon svg {
    fill: var( --e-global-color-primary );
}

.elementor-widget-icon-box.elementor-view-stacked .elementor-icon {
    background-color: var( --e-global-color-primary );
}

.elementor-widget-icon-box.elementor-view-framed .elementor-icon, .elementor-widget-icon-box.elementor-view-default .elementor-icon {
    fill: var( --e-global-color-primary );
    color: var( --e-global-color-primary );
    border-color: var( --e-global-color-primary );
}

.elementor-widget-icon-box .elementor-icon-box-title {
    color: var( --e-global-color-primary );
}

.elementor-widget-icon-box .elementor-icon-box-title, .elementor-widget-icon-box .elementor-icon-box-title a {
    font-family: var( --e-global-typography-primary-font-family ), Sans-serif;
    font-weight: var( --e-global-typography-primary-font-weight );
}

.elementor-widget-icon-box .elementor-icon-box-description {
    color: var( --e-global-color-text );
    font-family: var( --e-global-typography-text-font-family ), Sans-serif;
    font-weight: var( --e-global-typography-text-font-weight );
}

.elementor-widget-star-rating .elementor-star-rating__title {
    color: var( --e-global-color-text );
    font-family: var( --e-global-typography-text-font-family ), Sans-serif;
    font-weight: var( --e-global-typography-text-font-weight );
}

.elementor-widget-image-gallery .gallery-item .gallery-caption {
    font-family: var( --e-global-typography-accent-font-family ), Sans-serif;
    font-weight: var( --e-global-typography-accent-font-weight );
}

.elementor-widget-icon-list .elementor-icon-list-item:not(:last-child):after {
    border-color: var( --e-global-color-text );
}

.elementor-widget-icon-list .elementor-icon-list-icon i {
    color: var( --e-global-color-primary );
}

.elementor-widget-icon-list .elementor-icon-list-icon svg {
    fill: var( --e-global-color-primary );
}

.elementor-widget-icon-list .elementor-icon-list-item > .elementor-icon-list-text, .elementor-widget-icon-list .elementor-icon-list-item > a {
    font-family: var( --e-global-typography-text-font-family ), Sans-serif;
    font-weight: var( --e-global-typography-text-font-weight );
}

.elementor-widget-icon-list .elementor-icon-list-text {
    color: var( --e-global-color-secondary );
}

.elementor-widget-counter .elementor-counter-number-wrapper {
    color: var( --e-global-color-primary );
    font-family: var( --e-global-typography-primary-font-family ), Sans-serif;
    font-weight: var( --e-global-typography-primary-font-weight );
}

.elementor-widget-counter .elementor-counter-title {
    color: var( --e-global-color-secondary );
    font-family: var( --e-global-typography-secondary-font-family ), Sans-serif;
    font-weight: var( --e-global-typography-secondary-font-weight );
}

.elementor-widget-progress .elementor-progress-wrapper .elementor-progress-bar {
    background-color: var( --e-global-color-primary );
}

.elementor-widget-progress .elementor-title {
    color: var( --e-global-color-primary );
    font-family: var( --e-global-typography-text-font-family ), Sans-serif;
    font-weight: var( --e-global-typography-text-font-weight );
}

.elementor-widget-testimonial .elementor-testimonial-content {
    color: var( --e-global-color-text );
    font-family: var( --e-global-typography-text-font-family ), Sans-serif;
    font-weight: var( --e-global-typography-text-font-weight );
}

.elementor-widget-testimonial .elementor-testimonial-name {
    color: var( --e-global-color-primary );
    font-family: var( --e-global-typography-primary-font-family ), Sans-serif;
    font-weight: var( --e-global-typography-primary-font-weight );
}

.elementor-widget-testimonial .elementor-testimonial-job {
    color: var( --e-global-color-secondary );
    font-family: var( --e-global-typography-secondary-font-family ), Sans-serif;
    font-weight: var( --e-global-typography-secondary-font-weight );
}

.elementor-widget-tabs .elementor-tab-title, .elementor-widget-tabs .elementor-tab-title a {
    color: var( --e-global-color-primary );
}

.elementor-widget-tabs .elementor-tab-title.elementor-active, .elementor-widget-tabs .elementor-tab-title.elementor-active a {
    color: var( --e-global-color-accent );
}

.elementor-widget-tabs .elementor-tab-title {
    font-family: var( --e-global-typography-primary-font-family ), Sans-serif;
    font-weight: var( --e-global-typography-primary-font-weight );
}

.elementor-widget-tabs .elementor-tab-content {
    color: var( --e-global-color-text );
    font-family: var( --e-global-typography-text-font-family ), Sans-serif;
    font-weight: var( --e-global-typography-text-font-weight );
}

.elementor-widget-accordion .elementor-accordion-icon, .elementor-widget-accordion .elementor-accordion-title {
    color: var( --e-global-color-primary );
}

.elementor-widget-accordion .elementor-accordion-icon svg {
    fill: var( --e-global-color-primary );
}

.elementor-widget-accordion .elementor-active .elementor-accordion-icon, .elementor-widget-accordion .elementor-active .elementor-accordion-title {
    color: var( --e-global-color-accent );
}

.elementor-widget-accordion .elementor-active .elementor-accordion-icon svg {
    fill: var( --e-global-color-accent );
}

.elementor-widget-accordion .elementor-accordion-title {
    font-family: var( --e-global-typography-primary-font-family ), Sans-serif;
    font-weight: var( --e-global-typography-primary-font-weight );
}

.elementor-widget-accordion .elementor-tab-content {
    color: var( --e-global-color-text );
    font-family: var( --e-global-typography-text-font-family ), Sans-serif;
    font-weight: var( --e-global-typography-text-font-weight );
}

.elementor-widget-toggle .elementor-toggle-title, .elementor-widget-toggle .elementor-toggle-icon {
    color: var( --e-global-color-primary );
}

.elementor-widget-toggle .elementor-toggle-icon svg {
    fill: var( --e-global-color-primary );
}

.elementor-widget-toggle .elementor-tab-title.elementor-active a, .elementor-widget-toggle .elementor-tab-title.elementor-active .elementor-toggle-icon {
    color: var( --e-global-color-accent );
}

.elementor-widget-toggle .elementor-toggle-title {
    font-family: var( --e-global-typography-primary-font-family ), Sans-serif;
    font-weight: var( --e-global-typography-primary-font-weight );
}

.elementor-widget-toggle .elementor-tab-content {
    color: var( --e-global-color-text );
    font-family: var( --e-global-typography-text-font-family ), Sans-serif;
    font-weight: var( --e-global-typography-text-font-weight );
}

.elementor-widget-alert .elementor-alert-title {
    font-family: var( --e-global-typography-primary-font-family ), Sans-serif;
    font-weight: var( --e-global-typography-primary-font-weight );
}

.elementor-widget-alert .elementor-alert-description {
    font-family: var( --e-global-typography-text-font-family ), Sans-serif;
    font-weight: var( --e-global-typography-text-font-weight );
}

.elementor-widget-text-path {
    font-family: var( --e-global-typography-text-font-family ), Sans-serif;
    font-weight: var( --e-global-typography-text-font-weight );
}

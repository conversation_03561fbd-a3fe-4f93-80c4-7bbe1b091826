@import "https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&amp;display=swap";
@import "https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&amp;display=swap";
:root {
  --fontFamily: "Montserrat", sans-serif;
  --fontFamily2: "Roboto", sans-serif;
  --mainColor: #00aa55;
  --secondColor: #00aa5530;
  --whiteColor: #ffffff;
  --blackColor: #1f2428;
  --paragraphColor: #666666;
  --card-title-fontSize: 22px;
  --fontSize: 16px;
  --transition: 0.5s;
  --boxShadow: rgba(80, 79, 79, 0.1) 0px 0px 16px;
}
body {
  margin: 0;
  padding: 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  overflow: auto;
  overflow-y: scroll;
      top: 0 !important;
}
.h1,
.h2,
.h3,
.h4,
.h5,
.h6,
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: "Roboto", sans-serif;
  color:#FF00BF;
}
a {
  color: #0144a6;
  text-decoration: none;
  outline: 0 !important;
}
img {
  max-width: 100%;
  height: auto;
}
p {
font-family: "Roboto", sans-serif;
    color: #555;
    font-size: 16;
    margin-bottom: 2px;
  
padding: 0px;
width: 100%;

}
.owl-carousel .owl-dots.disabled, .owl-carousel .owl-nav.disabled {
    display:block;
}
.top-header .row{
    margin-left: -12px;
    margin-right: -11px;
    padding-left: 0px;
    padding-right: 0px;
    background: #233c6a;
}
.top-menus ul {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative; 
  list-style: none; 
  margin:0;
  background: #233c6a;
}
.top-menus ul li a{
    color:#ffffff;
    font-size: 16px;
    letter-spacing: 0.5px;
}
.top-menus ul li i{
    color:#ffffff;
    font-size: 16px;
   
}
.post-thumbnail .wp-post-image{
    margin-left:0px !important;
    border: 1px solid #ccc;
}

.mainContent{
        overflow: hidden;
        border-top-left-radius: 0px !important; 
    border-bottom-left-radius: 15px ;
    border-bottom-right-radius: 15px ;
}
.entry-footer{
        margin: 30px 111px !important;
        
}
.alignwide h1{
    font-size: 36px;
    color: #ad0303;
    margin-left: 21px;

}
h2.elementor-heading-title.elementor-size-default{
    font-size:32px;
    text-align:left;
}


@media (min-width: 992px) {
.navbar-expand-lg .navbar-nav .nav-link {
    padding-right: 20px;
    padding-left: 20px;
    color:#f96126;
}
}

.dropdown-item:focus, .dropdown-item:hover {
    color: #1e2125;
   
}
.dropdown-menu {
    position: absolute;
    z-index: 1000;
    display: none;
    min-width: 20rem;
    padding: 0.5rem 0;
    margin: 0;
    font-size: 1rem;
    color: #212529;
    text-align: left;
    list-style: none;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid rgb(255 120 67 / 32%);
    border-radius: 0.25rem;
}


.social-icons ul{
display: flex;
  justify-content: space-between;
  align-items: center;
}
.social-icons ul li i {
    color: #ffffff;
    font-size: 16px;
    margin-right: 20px;
}

.logo-section {
    background-image: url(../imageNew/bg.jpg);
    background-position: center left;
    background-size: cover;
    width: 100%;
    padding: 20px 0;
    opacity: 1;
    display: flex;
    align-items: center; 
    justify-content: center; 
    min-height: 170px; 
    position: relative; 
}

.logo-text {
    text-align: center;
    color: #fff; 
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5); 
    position: relative;
    z-index: 2; 
}


.logo-section::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.3); 
    z-index: 1;
}

@media (max-width: 768px) {
    .logo-section {
        min-height: 120px;
        padding: 15px 0;
    }

    .logo-text h3 {
        font-size: 1.2rem; 
    }

    .logo-text h2 {
        font-size: 1rem; 
    }
}

@media (max-width: 576px) {
    .logo-section {
        min-height: 100px; 
        padding: 10px 0; 
    }

    .logo-text h3 {
        font-size: 1rem; 
    }

    .logo-text h2 {
        font-size: 0.9rem; 
    }

    .logo-text {
        padding: 0 10px; 
    }
}
.logo img {
    display: flex;
    margin: 0 auto !important;
}
.logo-right img {
    width: 140px;
    height: 140px;
    border-radius: 100%;
    background-size: cover;
   
}.logo-text h3{
    color: white;
    font-family: 'Times New Roman', Times, serif;
    font-size: 39px;
    font-weight: 700;
    text-align: center;
    text-shadow: rgb(19, 19, 18) 3px 0 10px;
}
.logo-text h2{
    color: white;
    font-family: 'Times New Roman', Times, serif;
    font-size: 49px;
    font-weight: 700;
    text-align: center;
    text-shadow: rgb(19, 19, 18) 3px 0 10px;
}
.logo-area {
    display: flex;
  justify-content: space-between;
  align-items: center;
}
.navMenu {
    padding: 4px 0;
    background-color: beige;
    border-bottom: 1px solid #d3d3d3;
}
.navMenu .navbar-nav li a {
    font-weight: 400;
    padding: 0px 18px;
    color:red;
    
}
.navbar-expand-lg .navbar-nav .nav-link {
    padding-right: 20px;
    padding-left: 20px;
     color: #f96126; 
    
    font-size: 16px;
}
.navigation a:hover{
    text-decoration: none !important;
    text-decoration-style: none !important;
}


.navbar-expand-sm .navbar-nav .nav-link{
      padding-right: 15px;
    padding-left: 15px;
    color: #558a00;
    font-size: 16px;
}
.navbar-expand-sm .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
    align-items: center;
    justify-content: space-between;
}
.btn {
    display: inline-block;
    font-weight: 400;
    line-height: 1.5;
    color: #212529;
    text-align: center;
    text-decoration: none;
    vertical-align: middle;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    background-color: transparent;
    border: 1px solid transparent;
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    border-radius: 0;
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
.btn-primary {
    color: #fff;
    background-color: #558a00;
    border-color: #558a00;
}
.form-control {
    display: block;
    width: 100%;
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #212529;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ced4da;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border-radius: 0;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.single-education-card.bg-1 {
    background-image: url(../images/academic-4.jpg);
}
.single-education-card {
    background-size: cover;
    background-position: center center;
    background-repeat: no-repeat;
    position: relative;
    padding: 50px 30px;
    margin-bottom: 30px;
    z-index: 1;
}

.single-education-card::before {
    position: absolute;
    content: "";
    background: rgb(76 108 24 / 74%);
    height: 100%;
    width: 100%;
    top: 0;
    left: 0;
    z-index: -1;
}
.single-education-card .edication-content {
    text-align: center;
    position: relative;
}
.single-education-card .edication-content .icon i {
    font-size: 50px;
    margin-bottom: 10px;
    color: #fff;
}
.single-education-card .edication-content h3 {
    font-size: 22px;
    color: #fff;
    margin-bottom: 15px;
}
.read-more-btn.white-color {
    color: #fff;
}
.read-more-btn i {
    position: relative;
    top: 3px;
    padding-left: 7px;
}
.single-education-card::after {
    position: absolute;
    content: "";
    background: #b33300;
    display: inline-block;
    height: 100%;
    width: 0;
    top: 0;
    right: 0;
    z-index: -1;
    transition: all ease .5s
}

.single-education-card:hover::after {
    width: 100%;
    border-radius: 0;
    left: 0;
    right: auto
}
.single-education-card.bg-2 {
    background-image: url(../images/academic-5.jpg);
}
.single-education-card.bg-3 {
    background-image: url(../images/academic-6.jpg);
}
.single-education-card.bg-4 {
    background-image: url(../images/academic-7.jpg);
}
.about-section{
    float: left;
    width: 100%;
}
.single-study img{
    border-radius:10px;
}

.about-text{
  padding: 40px 0;
    background-color: #f0f1ef;
}
.abt_univ_wrap {
        padding: 10px;
    width: 100%;
}
.about-heading1 {
    float: left;
    width: 100%;
    text-align: left;
   
}


.about-heading1 h5 {
  color:#5500ff;
  text-transform: capitalize;
  margin: 3px 0 11px;
    position: relative;
    font-weight:500;
}
.about-heading1 h3 {
    text-transform: capitalize;
    font-weight: normal;
    position: relative;
    padding-bottom: 15px;
}
.about-heading1 h3:before {
    content: "";
    left: 0;
    bottom: 0;
    height: 2px;
    width: 25px;
    background-color: #666666;
    position: absolute;
}
.about-heading1 h3::after {
    content: "";
    left: 35px;
    bottom: 1px;
    height: 1px;
    width: 50px;
    background-color: #bbbbbb;
    position: absolute;
}
.about-description {
    float: left;
    width: 100%;
    text-align: left;
    padding: 0 0 0;
}
.about-description span {
    font-size: 18px;
    color: #333;
    margin: -7px 0 21px;
    display: block;
}
.about-description p{
    margin-bottom: 20px;
    font-size: 16px;
    line-height: 26px;
    text-align: justify;
}
.about-description .btn-3 {
    background: transparent none repeat scroll 0 0;
    border: 1px solid #FF00BF;
    color:#FF00BF;
    border-radius: 0;
    display: inline-block;
    font-size: 16px;
    font-weight: bold;
    padding: 8px 25px;
    line-height: normal;
    text-transform: uppercase;

}
.about-description .btn-3:hover {
    color: #fff;
    background-color: #233c6a;
    border: 1px solid #233c6a;
}
.about-photo {
    float: left;
    width: 100%;
}
figure {
    float: left;
    width: 100%;
    position: relative;
    overflow: hidden;
    margin: 0;
}
.about-photo figure:before {
    content: "";
    left: 5%;
    right: 5%;
    top: 5%;
    bottom: 5%;
    position: absolute;
    background-color: transparent;
    opacity: 1;
    border: 2px solid #b33300;
}

.about-breadcrumb-area {
   background-image: url(../images/about-banner.jpg);
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover; 
    position: relative;
    background-attachment: fixed;
    transition: background 0.3s, border 0.3s, border-radius 0.3s, box-shadow 0.3s;
    z-index: 1;
  padding: 110px 0 110px 0;
}

.about-breadcrumb-areas {
   background-image: url(../images/about-banner.jpg);
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover; 
    position: relative;
    transition: background 0.3s, border 0.3s, border-radius 0.3s, box-shadow 0.3s;
    z-index: 1;
  padding: 110px 0 110px 0;
}

.about-breadcrumb-areas ::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #000;
    opacity: 0.3;
    z-index: -1;
}
.about-breadcrumb-areas ::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 0px;
    background-color: #ffffff;
    z-index: -1;
}
.about-breadcrumb-area ::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #000;
    opacity: 0.3;
    z-index: -1;
}
.about-breadcrumb-area ::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 0px;
    background-color: #ffffff;
    z-index: -1;
}
.about-us {
    text-align: center;
    color: #fff;
}
.about-us h2{
    font-size: 40px;
    font-weight: 600;
    text-transform: uppercase;
}

.study-area {
    background-image: url(../images/study-bg.jpg);
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;
    height: 445px;
    position: relative;
    z-index: 1;
    padding: 50px 0 0px 0;
}


.study-area::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #000;
    opacity: 0.9;
    z-index: -1;
}
.study-area::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 0px;
    background-color: #ffffff;
    z-index: -1;
}
.study-area .section-title {
    margin-left: 0;
    text-align: left;
}
.director-para h4{
  font-size: 20px;
    color: #ff5613;  
}
.director-para h2 {
font-size: 35px;
    color: #fff;
    font-weight: 600;
}
.director-para p {
   font-size: 16px;
   color: #fff;
   line-height: 27px;
}

.section-title {
    max-width: 100%;
    margin: 0px auto 50px;
    position: relative;
}
.section-title h2 {
  color:#ffffff;
    font-size: 45px;
    text-align:center;
    margin-bottom: 15px;
    position: relative;
}
.study-area .section-title p {
    margin-left: 0;
    color:#fff;
}

.single-study {
    margin-top:30px;
}
.single-study i {
    font-size: 40px;
    line-height: 1;
    margin-bottom: 20px;
    display: inline-block;
    width: 80px;
    height: 80px;
    background-color: #4c6c18;
    border-radius: 50%;
    text-align: center;
    color: #fff;
    -webkit-transition: var(--transition);
    transition: var(--transition);
}
.single-study h3 {
    font-size: 22px;
    margin-bottom: 14px;
}
.single-study h3 a {
    color: #000;
    -webkit-transition: var(--transition);
    transition: var(--transition);
}
.read-more {
    font-size: 14px;
    color: #4c6c18;
    font-weight: 600;
}
.read-more span, .read-more i {
    position: relative;
    top: 3px;
}
.study-area .owl-theme .owl-nav {
    margin-top: 0 !important;
    position: absolute;
    right: 7px;
    top: -95px;
    -webkit-transform: translateY(-30px);
    transform: translateY(-30px);
}
.study-area .owl-theme .owl-nav .owl-prev, .study-area .owl-theme .owl-nav .owl-next {
    margin: 0 5px;
}
.study-area .owl-theme .owl-nav .owl-prev i, .study-area .owl-theme .owl-nav .owl-next i {
    font-size: 20px;
    color: #ffffff;
    border: 1px solid #a5a5a5;
    width: 50px;
    height: 40px;
    line-height: 36px;
    display: inline-block;
    -webkit-transition: var(--transition);
    transition: var(--transition);
}
.study-area .owl-theme .owl-nav .owl-next {
    right: -50px;
    left: auto;
}


.single-study .read-more i {
    position: relative;
    top: 2px;
    left: 10px;
    font-size: 15px;
    background-color: transparent;
    color: #4c6c18;
    margin: 0px;
    height: 0;
    width: 15px;
    line-height: 15px;
}
.single-study .read-more:hover i {
    color: #fff !important; 
}

.campus-img {
    margin-top: 0;
    position: relative;
   
}

.sliderImg img{
    height: 100%;
  object-fit: cover;
}

.campus-content {
    padding: 87px;
    padding-left: 26px;
    position: relative;
    width: 100%;
    padding-right: 148px;
    height: 480px;
}
.campus-content::before {
    content: "";
    position: absolute;
    top: 0;
    left: -10px;
    width: 100%;
    height: 100%;
    background-image: url(../images/slider-side-img.png);
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;
    z-index: -1;
}

.campus-content span {
    color: #ffffff;
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 10px;
    display: inline-block;
}
.section-title h2::after {
    content: "";
    left: 528px;
    top: 63px;
    height: 2px;
    width: 80px;
    background-color: #ffffff;
    position: absolute;
}
.campus-content h2 {
    font-size: 40px;
    color: #ffffff;
    margin-bottom: 20px;
}
.readMore {
    color: #fff;
    background-color: #4c6c18;
    padding: 10px 25px;
}
.readMore a:hover {
    color: #ffffff;
    background-color: #ec4300;
}
.readMore span {
      color: #ffffff;
    font-size: 15px;
    font-weight: 500;
    margin-left: 10px;
}
.marquee-area {
    overflow: hidden;
}
.ticker {
    display: flex;
    padding-right:0px;
    padding-left:0;
    width: 100%;
    height: 50px;
}
.title h5 {
    font-size: 17px;
    font-weight: 600;
    margin: 17px 0;
    color: #ffffff;
}
.title {
        width: 16%;
    text-align: center;
    background: orangered;
    position: relative;
}
.news {
    width: 100%;
    background: #233c6a;
    padding: 0px;
}
.news marquee .click-button{
       text-decoration: none;
    border: 1px solid #ccc;
    padding: 8px 16px;
    background: #b33300;
    border-radius: 7px;
    color: #ffffff;
    font-size: 15px;
    text-align: center;
    margin-right: 10px;
    }
    
.news marquee {
    font-size: 16px;
    margin-top: 10px;
}

marquee p {
    margin-bottom: 0;
    color: #ffffff;
}



.director-img img {
    border: 2px solid #ffffff;
    height: 150px;
    border-radius: 5px;
    margin-top: 10px;
    width: 40%;
    max-width: 500px ;
    

}
.director-img h5 {
    margin-top:15px;
}
.academy-library {
    padding: 40px 0;
    background-color: #f6f8f3;
}
.owl-carousel .owl-nav button.owl-next, .owl-carousel .owl-nav button.owl-prev, .owl-carousel button.owl-dot {
    background: #233c6a !important;
    color: #fff;
    border: none;
    padding: 7px 12px!important;
}


.library-sec img{
  object-fit: cover;
    border-radius: 5px;  
    height: 252px;
    width: 400px;
    margin-top:30px;
    position: relative;
}
.library-sec h4 {
    position: absolute;
    top: 140px;
    left: 33px;
    color: #fff;
    font-size: 26px;
}
.library-button {
    background-color: #ffffff;
    border-bottom-right-radius: 0;
    padding: 10px 13px 10px 60px;
    position: absolute;
    top: 183px;
    right: 204px;
    line-height: 24px;
    border-top-left-radius: 22px;
    border-bottom-left-radius: 22px;
    border-top-right-radius: 20px; 
}
.library-button .btn-icon{
        background: #233c6a;
    color: #fff;
    position: absolute;
    left: 0;
    top: 0px;
    height: 44px;
    width: 44px;
    text-align: center;
    border-radius: 50%;
    font-size: 20px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
}
.academic {
    position:relative;
}

.academic h3:before {
    content: "";
    left: 10px;
    top: 42px;
    height: 2px;
    width: 25px;
    background-color: #666666;
    position: absolute;
}
.academic h3::after {
    content: "";
    left: 42px;
    top: 42px;
    height: 1px;
    width: 50px;
    background-color: #bbbbbb;
    position: absolute;
}
.events-content {
    position: relative;
    border: 1px px solid;
    border: 1px solid #b33300;
    padding: 30px;
    border-radius: 10px;
    height: 340px;
    overflow: hidden;
}

.events-content h3:before {
    content: "";
    left: 10px;
    top: 42px;
    height: 2px;
    width: 25px;
    background-color: none;
    position: absolute;
}
.events-content h3::after {
    content: "";
    left: 42px;
    top: 42px;
    height: 1px;
    width: 50px;
    background-color: none;
    position: absolute;
}
.events-content marquee{
    height: 245px;
    overflow: hidden;
}

.events-area {
    background-color: #fff;
    padding:20px 0;
}
.events-content .events-list {
    padding: 0;
    margin: 0;
    margin-top:32px;
    list-style-type: none;
}
.events-content .events-list li {
       position: relative;
    padding-left: 197px;
    margin-bottom: 25px;
}
.events-content .events-list li .events-date {

    display: inline-block;
    text-align: center;
    position: absolute;
    top: 0;
    left: 0;
    -webkit-transition: var(--transition);
    transition: var(--transition);
}
.events-content .events-list li .events-date img {
    border-radius: 8px;
    margin-bottom: 0;
}

.event-details{
 display: flex;
 margin-top: 20px;
}
.events-date img {
    width: 200px!important;
    height: 110px;
    object-fit:contain;
    max-width:150px;
}
.events-content .events-list li h3 {
    font-size: 24px;
    margin-bottom: 5px;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}
.events-content .events-list li h3 a {
    color: #000000;
}
.events-content .events-list li p {
    font-size: 15px;
    margin-bottom: 5px;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
.events-content a {
    -webkit-transition: var(--transition);
    transition: var(--transition);
    text-decoration: none;
    color: #0000FF;
    font-size: 16px;
    text-shadow: 1px 1px 2px #91afb795;
}
.read-moreS i {
        position: relative;
    top: 1px;
    font-size: 16px; 
}
.events-timer {
    background-color: #233c6a;
    text-align: center;
    padding: 30px 30px;
    border-radius: 10px;
}
.events-timer span {
    color: #fff;
    display: block;
    margin-bottom: 10px;
    font-size: 15px;
    
}
.events-timer .event-img {
    margin-bottom: 20px;
}
.default-btn {
    color: #fff;
    font-weight: 600;
    font-size: 16px;
    background-color: #e18c00;
    padding: 10px;
    border-radius: 5px;
}
.events-timer #timer div {
    display: inline-block;
	background-color: #fff;
	color: #000;
	width: 60px;
	line-height:30px;
	height: 45px;
	padding-top: 8px;
	font-size: 20px;
	font-weight: 600;
	text-align: center;
	margin-right: 10px;
    margin-bottom: 40px;
}
.fact-counter-area {
    position: relative;
    display: block;
    background-attachment: fixed;
    background-position: center top;
    padding: 30px 0;
    z-index: 1;
}
.fact-counter-area::before {
    background: rgb(34,193,195);
background: linear-gradient(0deg, rgba(34,193,195,1) 16%, rgba(45,70,253,1) 100%);
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    content: "";
    z-index: -1;
}
.box-background {
    background: #fff;
    padding: 20px;
    border-radius: 10px;
    height: 340px;
}
.signleBox h4 {
    font-size: 20px;
    color: #FF00BF;
    font-weight: 600;
    margin-bottom: 20px;
    border-bottom: 1px solid #acacac;
    align-items: center;
    display: flex;
    justify-content: space-between;
    padding-bottom: 6px;
}
.signleBox h4 span a{
    font-size: 15px;
    margin-left: 20px;
    font-weight: 500;
    color: #233c6a;
}
.news_evnet_container .overflow_y {
    max-height: 248px;
    overflow-y: auto !important;
    position: relative;
    bottom: 0 !important;
    right: 0;
}
.news_evnet_container .single_news_event {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding: 10px;
    margin-bottom: 15px;
    background: #6eaf031c;
    margin-right: 10px;
    border-radius: 10px;
}
.news_event_content a p{
    font-size: 13px;
    color: #000;
    line-height: 18px;
    margin-bottom: 7px;
}
.single_news_event .news_event_content h5 {
   font-size: 14px;
    font-weight: 600;
    margin-bottom: 5px;
    color: #181818;
}
.single_news_event .news_event_content span  {
    font-size: 14px;
    font-weight: 400;
    font-style: italic;
    color: #232323;
}
.overflow_y::-webkit-scrollbar {
    width: 6px;
    
}

.overflow_y::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 20px
}

.overflow_y::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 20px
}

.overflow_y::-webkit-scrollbar-thumb:hover {
    background: #555
}
.counter-area {
    overflow: hidden;
    background-color: #f8fafb;
}
.single-counter.bg-style {
background-color: #efeef2;
    border: 1px solid #cbcbcb;
    position: relative;
    padding-top: 28px;
    height: 135px;
    padding-bottom: 0px;
    padding-left: 14px;
    padding-right: 10px;
    text-align: left;
    margin-bottom: 0px;
    margin-top: 40px;
    border-radius:5px;
}
.single-counter.bg-style i {
    position: absolute;
    background-color: #578d00;
    top: 38px;
    left: 20px;
    width: 70px;
    height: 70px;
    line-height: 70px;
    font-size: 40px;
    text-align: center;
    color: #fff;
    display: inline-block;
    border-radius: 50%;
    margin-bottom: 15px;
}

.board-president {
    text-align: center;
}

.single-content-para {
       border: 1px solid #e3e3e3;
    background-color: #efeef2;
    padding: 15px; 
    
}
.counter-bg{
    margin-bottom:40px;
    display:flex;
    justify-content:space-between;
}

.member-row .single-content-para {
    height:295px;
    margin-bottom:20px;
}
.single-content-para img {
    position: relative;
    
    width: 100px;
    height: 100px;
    display: inline-block;
    border-radius: 50%;
    border: 2px solid #ed9400;
}
.single-counter.bg-style h2 {
      color: #0a9643;
    font-size: 16px;
    font-weight: 400;
    margin-bottom: 8px;
    margin-left: 20px;
}
.count-title h4 a{
      font-size: 15px;
    color: #5292be;
    padding: 7px 10px;
    border-radius: 5px;
    margin-left: 10px;
}
.blog-area {
    padding:40px 0;
}

.count-titles {
    text-align:center;
}

.count-titles h2{
    color: #ed9400 !important;
    font-size: 20px !important;
    font-weight: 400 !important;
    margin-top: 10px;
}

.count-titles h4{
    margin: 0;
    font-size: 18px;
}

.count-titles p{
    font-size: 15px;
    margin: 0;
}
.single-counters.bg-styles {
    background-color: #efeef2;
    border: 1px solid #cbcbcb;
    position: relative;
    padding-top: 17px;
    height: 167px;
    padding-bottom: 20px;
    padding-left: 114px;
    padding-right: 10px;
    text-align: left;
    margin-bottom: 30px;
}

.section-titles h2 {
  color:#000;
    font-size: 30px;
    margin-bottom: 15px;
    position: relative;
}
.campus-life {
    width: 270px;
    margin: 0 auto !important;   
}
.president-directors {
    width: 470px;
    margin: 0 auto !important;   
    position: relative;
    padding-bottom: 40px;
}

.section-titless h2:before {
    content: "";
    left: 150px;
    top: 45px;
    height: 2px;
    width: 25px;
    background-color: #666666;
    position: absolute;
}
.section-titless h2::after {
    content: "";
    left: 185px;
    top: 45px;
    height: 1px;
    width: 50px;
    background-color: #bbbbbb;
    position: absolute;
}
.section-titles h2:before {
    content: "";
    left: 45px;
    top: 45px;
    height: 2px;
    width: 25px;
    background-color: #666666;
    position: absolute;
}
.section-titles h2::after {
    content: "";
    left: 80px;
    top: 45px;
    height: 1px;
    width: 50px;
    background-color: #bbbbbb;
    position: absolute;
}
.blog-content {
    background-color: #2dc04b;
    padding:10px;
    text-align:center;
    height:130px;
}
.blog-content h3 a{
        color: #fff;
    font-weight: 500;
    font-size: 22px;
}
.blog-content p a  {
    color: #233c6a;
    font-size: 16px;
    font-weight: 500;
}
.faculty-section, .faculty-details-section{
    padding:30px 0;
    background:#fff5f1;
}
.faculty-details-section .director{
    font-size: 26px;
    color: #b33300;
}
.faculty-details-section .director-para{
    font-size: 15px;
    text-align: justify;
}
.faculty-section h1, .faculty-details-section h1{
    font-size: 34px;
    margin-bottom: 23px;
    font-weight: 600;
}
.faculty-section .faculty-box,
.faculty-details-section .faculty-box
{
    background:#fff;
    box-shadow: 5px 5px 20px 0 rgba(0,0,0,0.15);
    margin-bottom:40px;
    padding-bottom: 40px;
}
.faculty-details-section .faculty-box{
    padding-bottom: 0px;
}
.faculty-section .rajasekhar{
    padding-bottom: 0px;
    
}
.faculty-details-section .academic-qualification h3{
    font-size: 22px;
    color: #558a00;
}
.faculty-details-section .academic-qualification .qualification {
        padding: 0px 32px 32px;
}
.faculty-details-section .qualification .professor {
    margin-top: 32px;
    margin-bottom: 12px;
    font-size: 16px;
}
.faculty-details-section .academic-qualification ul{
    margin:0px;
    padding:0px;
    margin-left: 23px;
}
.faculty-details-section .academic-qualification ul li{
    line-height: 34px;
    font-size: 15px;
    position: relative;
    list-style:none;
}
.faculty-details-section .academic-qualification ul li .bullet-style::before{
    content: '';
    position: absolute;
    top: 12px;
    left: -22px;
    width: 10px;
    height: 10px;
    background: linear-gradient(68deg, #b33300, #f17a4b);
    border-radius: 3px;

}

.faculty-section .faculty-box .para-style,
.faculty-details-section .faculty-box .para-style{
      padding: 0px 32px 32px;
}
.faculty-section .faculty-left a{
    text-decoration:none;
}
.faculty-section .faculty-left,
.faculty-details-section .faculty-left{
   padding: 32px 0 0 32px;
}
.faculty-section .faculty-left a h3{
    display:inline-block;
    color:#b33300;
    margin-bottom: 25px;
    font-size: 26px;
}
.faculty-section .faculty-left h4{
    color: #467001;
    margin-bottom: 9px;
    font-size: 22px;


}
.faculty-section .faculty-left p{
    font-size: 15px;
    text-align: justify;
}
.faculty-details-section .faculiy-image .proff-image{
      width: 270px;
    height: 270px;
    object-fit: cover;
    margin-left: 54px;
    margin-top: 40px;
    border-radius: 50%;

}

.faculty-section .faculiy-image img{
    width: 270px;
    height: 270px;
    object-fit: cover;
    margin-left: 59px;
    margin-top: 37px;
    border-radius: 50%;

}
.form-detail-section {
    overflow:hidden;
    background:#fff9f7;
}
.form-detail-section form{
       border: 1px solid #ccc;
    width: 55%;
    text-align: center;
    padding: 25px 35px;
    margin: 60px auto;
    border-radius: 6px;
    background: #ffffff;
}
.form-detail-section form input[type=submit]{
    padding: 8px 40px;
    border-radius: 25px;
    border: 1px solid #578d00;
    margin: 24px 0 0 0;
    background: #558a00;
    color: #fff;
    font-size: 16px;
    transition: ease-in-out .5s;
    }
.form-detail-section form input[type=submit]:hover{

    transform: translateY(-5px);
}    
.form-detail-section form h3{
    font-size: 24px;
    margin: 8px 0px 30px 0px;
}
.form-detail-section form .form-style{
    margin-bottom:17px;
    
}
.form-detail-section form .form-dropdown .form-select{
    color:#555555;
}

.footer-area.black-bg-color {
    background-image: linear-gradient(to top, #00c6fb 0%, #005bea 100%);
}
.single-footer-widget{
       padding-top: 40px;
    padding-bottom: 20px;
}

.single-footer-widget .logo {
    margin-bottom: 20px;
    display: inline-block;
}

.single-footer-widget p {
    margin-bottom: 20px;
    color: #fff;
    font-weight: 400;
    font-size: 16px;
    letter-spacing: 0.3px;
}
.single-footer-widget .social-icon {
    line-height: 1;
    padding: 0;
    margin: 0;
    list-style-type: none;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.single-footer-widget .social-icon li {
    display: inline-block;
    margin-right: 5px;
}
.single-footer-widget.bg-f9f5f1 .social-icon li span {
    color: #fff;
    font-weight: 600;
}
.single-footer-widget .social-icon li a {
        width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    -webkit-transition: var(--transition);
    transition: var(--transition);
    display: inline-block;
    background-color: #d38500;
    color: #fff;
    position: relative;
    z-index: 1;
    border-radius: 30px;
}
.single-footer-widget h3 {
    font-size: 24px;
    margin-bottom: 25px;
    position: relative;
    margin-top: -5px;
    color: #fff;
}
.single-footer-widget .import-link li {
    margin-bottom: 16px;
}
.single-footer-widget .import-link li a {
    color: #fff;
}
.copy-right-area {
    background-color: #3c00d3;
    padding-top: 15px;
    padding-bottom: 10px;
    text-align: center;

}
.copy-right-area p {
    color: #fff;
}
.copy-right-area p i {
    position: relative;
    top: 2px;
}
.copy-right-area p a {
    color: #fff;
    font-weight: 600;
}
.single-footer-widget .import-link {
    padding: 0;
    margin: 0;
    list-style-type: none;
}
.single-footer-widget .address {
    padding: 0;
    margin: 0;
    list-style-type: none;
}
.single-footer-widget .address li {
    position: relative;
    margin-bottom: 11px;
    padding-left: 35px;
    color: #fff;
}
.single-footer-widget .address li i {
    position: absolute;
    left: 0;
    top: 0;
    font-size: 20px;
    -webkit-transition: var(--transition);
    transition: var(--transition);
    display: inline-block;
    color: #fff;
}
.single-footer-widget .address li span {
    display: block;
    margin-bottom: 5px;
    font-size: 17px;
}
.single-footer-widget .address li a {
    display: block;
    color: #fff;
}


   
   
   .VIpgJd-ZVi9od-l4eHX-hSRGPd, .VIpgJd-ZVi9od-l4eHX-hSRGPd:link, .VIpgJd-ZVi9od-l4eHX-hSRGPd:visited, .VIpgJd-ZVi9od-l4eHX-hSRGPd:hover, .VIpgJd-ZVi9od-l4eHX-hSRGPd:active {
    font-size: 12px;
    font-weight: bold;
    color: #444;
    text-decoration: none;
    display: none;
}

.goog-te-banner-frame {
  display: none;
}

.goog-logo-link {
  display: none;
}

.para-style{
    margin:35px 0;
}
.para-style p{
    text-align:justify;
    font-size:15px;
}

.goog-te-gadget {
  font-size: 11px;
  color: #0f141800 !important;
  white-space: nowrap;
}

#google_translate_element .skiptranslate.goog-te-gadget {
    display: flex;
}

.goog-te-gadget .goog-te-combo {
       margin: 0;
    font-size: 12px;
    
    border-radius: 5px;
    color: #233c6a;
    border: 1px solid #c47c04;
}
.VIpgJd-ZVi9od-ORHb-OEVmcd {
    display: none;  
}
.news_evnet_container marquee{
       height: 245px;
    overflow: hidden;
}
.elementor-icon i, .elementor-icon svg {
    width: 1em;
    height: 1em;
    position: relative;
    display: block;
    font-size: 22px;
    margin-top: 5px;

}
.elementor-icon i:before, .elementor-icon svg:before{
        color: #ad0303;
}
.elementor-widget:not(:last-child) {
    margin-bottom: 0px!important;
    }



.icon-img img{
    width:70px!important;
    height:72px!important;
    
  
}

.icon-img{
     display: flex;
    justify-content: space-around;
    
}




.mainContent .elementor-column.elementor-col-33 .elementor-widget-wrap.elementor-element-populated h2.elementor-heading-title{
    color: #ffffff;
    padding: 10px;
    background:#233c6a!important;
    text-align: center;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
}


.elementor-shortcode ul {
    list-style-type: square!important;
    color: red;
    font-family: "Times New Roman", Times, serif;
    border: 1px solid #28236a;
    padding: 30px;
    margin-top: -15px;
    border-radius: 10px;
    line-height: 28px;
}

.mainContent .elementor-widget-container p, .mainContent .elementor-widget-container{
    margin:5px 5px;
}


.elementor-image-box-content h3{
 color: #ad0303!important;
}
.elementor-image-box-img img{
    border-radius:10px!important;
}
.elementor-image-box-img img,
.elementor-image-box-img a img{
    width: 100%;
    overflow: hidden;
    max-width: 120px !important;
    border: 1px solid #ad0303 !important;
    background-size: cover;
    
}

.elementor-widget-image-box.elementor-position-right .elementor-image-box-wrapper{
    text-align:left !important;
}


.elementor-widget-image-gallery .elementor-image-gallery .gallery-item a img{
    max-width: 190px !important;
    height: 194px !important;
   
    border: 2px solid #000;
    border-radius: 7px;
    padding:1px;
    
}

.elementor-image-gallery figure figcaption {
    font-size: 12px;
    width: 95% !important;
    margin: auto !important;
    
}

.elementor-accordion .elementor-tab-title{
    background: #233c6a;
    color:#fff;
}

.wptb-preview-table .wptb-text-container p{
    text-align:left ;
}

.main-table .abstract{
    color:#165fe6 !important;
}


.elementor-widget-container table ul li a{
    color:#165fe6;
}

.elementor-widget-container p a{
    color:#165fe6;
} 
.elementor-widget-container b a{
    color:#165fe6;
}


.elementor-widget-container h4{
   color:#EF6262;
}

.wptb-preview-table .wptb-row .wptb-cell .wptb-text-container p a strong{
    color:#0551df;
}

.wptb-preview-table .wptb-row .wptb-ph-element div p a{
    color:#0551df !important;
}


.entry-footer a{
    display:none;
}


.elementor-image-box-wrapper{
    border:1px ridge #2203ad!important;
    padding:20px;
    border-radius:30px!important;
}


.elementor-button-content-wrapper span{
    background-color:#233c6a!important;
    padding:20px;
    border-radius:10px;
}



.main-img img{
    width: 100%!important;
    height: 239px!important;
    border-radius: 10px;
}

.elementor-widget-container {
    text-align: left !important;
}



.wptb-table-container table td{
    vertical-align: baseline!important;
}


 h1.entry-title{
    display: none!important;
}


.comments-area{
    display:none;
}

#post-8629 .mainContent li{
    margin-bottom: 8px;
}
.about-heading-bg{
    background-color: #5500ff;
    width: 5vw;
    height: 2px;
}
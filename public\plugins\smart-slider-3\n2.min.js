!function(t) {
    var i = t;
    i._N2 = i._N2 || {
        _r: [],
        _d: [],
        r: function() {
            this._r.push(arguments)
        },
        d: function() {
            this._d.push(arguments)
        }
    };
    var n, s, r = t.document, o = r.documentElement, h = t.setTimeout, a = t.clearTimeout, u = i._N2, c = (t.requestAnimationFrame,
    function(t, i=null, n=null, s=null) {
        const o = r.createElement(t);
        return i && ("string" == typeof i ? g(o, i) : b(o, i)),
        n && v(o, n),
        s && m(o, s),
        o
    }
    ), f = Object.assign, l = function(t, i, n) {
        t.setAttribute(i, n)
    }, v = function(t, i) {
        for (var n in i)
            l(t, n, i[n])
    }, d = function(t, i) {
        return t.dataset[i]
    }, p = function(t, i, n) {
        t.dataset[i] = n
    }, m = function(t, i) {
        for (let n in i)
            p(t, n, i[n])
    }, g = function(t, i) {
        t.classList.add(i)
    }, b = function(t, i) {
        i.forEach((function(i) {
            t.classList.add(i)
        }
        ))
    }, w = function(t, i) {
        return t.dispatchEvent(i)
    }, y = function(t, i, n) {
        return n = f({
            bubbles: !0,
            cancelable: !0
        }, n),
        w(t, new Event(i,n))
    }, M = function(t, i, n, s) {
        return s = s || {},
        t.addEventListener(i, n, s),
        t.removeEventListener.bind(t, i, n, s)
    }, x = function(t) {
        if ("complete" === r.readyState || "interactive" === r.readyState)
            t();
        else if (Document && Document.prototype && Document.prototype.addEventListener && Document.prototype.addEventListener !== r.addEventListener) {
            const i = ()=>{
                t(),
                t = ()=>{}
            }
            ;
            r.addEventListener("DOMContentLoaded", i),
            r.addEventListener("readystatechange", (()=>{
                "complete" !== r.readyState && "interactive" !== r.readyState || i()
            }
            )),
            Document.prototype.addEventListener.call(r, "DOMContentLoaded", i)
        } else
            r.addEventListener("DOMContentLoaded", t)
    }, _ = navigator.userAgent.indexOf("+http://www.google.com/bot.html") > -1 ? function(t) {
        t()
    }
    : i.requestIdleCallback || function(t) {
        return h(t, 1)
    }
    ;
    i.cancelIdleCallback;
    x((function() {
        n = r.body
    }
    )),
    function() {
        "use strict";
        var t = {}
          , n = {};
        i.n2Slow = navigator.userAgent.indexOf("Chrome-Lighthouse") > -1 && navigator.userAgent.indexOf("Android") > -1;
        var r = []
          , o = !1;
        function a() {
            var t = performance.now()
              , n = r;
            r = [];
            for (var s = n.length - 1; s >= 0 && (n.pop().call(),
            !(performance.now() - t > 7)); s--)
                ;
            !i.n2Slow && n.length && (i.n2Slow = !0),
            n.unshift.apply(n, r),
            (r = n).length ? _(a, {
                timeout: 2e3
            }) : o = !1
        }
        function f(t) {
            r.unshift(t),
            o || (o = !0,
            _(a, {
                timeout: 2e3
            }))
        }
        var l = new Date
          , v = function() {
            if (i.jQuery) {
                var $ = i.jQuery;
                u.d("$", (function() {
                    return $
                }
                ))
            } else {
                if (h(v, 20),
                (new Date).getTime() - l.getTime() > 1e3)
                    c("script").src = u._jQueryFallback
            }
        };
        function d(i) {
            return !(!i || t[i] !== s) && (t[i] = new Promise((function(t) {
                n[i] = t
            }
            )),
            "$" === i && v(),
            !0)
        }
        function p(i, s, r) {
            var o = [];
            if (d(i) || n[i]) {
                if ("function" == typeof s ? (r = s,
                s = []) : "string" == typeof s && (s = [s]),
                (s = s || []).length)
                    for (var h = 0; h < s.length; h++)
                        d(s[h]),
                        o.push(t[s[h]]);
                Promise.all(o).then(function(t) {
                    u[i] = "function" != typeof r || r.call(u),
                    t()
                }
                .bind(this, n[i])),
                delete n[i]
            }
        }
        function m(i, n) {
            var r = [];
            if (n === s ? (n = i,
            i = []) : "string" == typeof i && (i = [i]),
            i = i || [])
                for (var o = 0; o < i.length; o++)
                    d(i[o]),
                    r.push(t[i[o]]);
            Promise.all(r).then((function() {
                n.call(u)
            }
            ))
        }
        i.N2DISABLESCHEDULER ? (u.d = p,
        u.r = m) : (u.d = function(t, i, n) {
            f(p.bind(this, t, i, n))
        }
        ,
        u.r = function(t, i) {
            f(m.bind(this, t, i))
        }
        );
        for (var g = 0; g < this._N2._d.length; g++)
            u.d.apply(this, this._N2._d[g]);
        for (var b = 0; b < this._N2._r.length; b++)
            u.r.apply(this, this._N2._r[b])
    }
    .call(i),
    i.NextendThrottle = function(t, i) {
        var n, s;
        return i || (i = 250),
        function() {
            var r = this
              , o = +new Date
              , u = arguments;
            n && o < n + i ? (a(s),
            s = h((function() {
                n = o,
                t.apply(r, u)
            }
            ), i)) : (n = o,
            t.apply(r, u))
        }
    }
    ,
    i.NextendDeBounce = function(t, i, n) {
        var s;
        return function() {
            var r = this
              , o = arguments
              , u = function() {
                s = null,
                n || t.apply(r, o)
            }
              , c = n && !s;
            a(s),
            s = h(u, i),
            c && t.apply(r, o)
        }
    }
    ,
    u.r("nextend-frontend", (function() {
        if (x((function() {
            u.d("documentReady")
        }
        )),
        "complete" === r.readyState)
            u.d("windowLoad");
        else {
            let s;
            const o = navigator.userAgent;
            o.indexOf("Safari") > 0 && -1 === o.indexOf("Chrome") && (s = setInterval((function() {
                "interactive" !== r.readyState && "complete" !== r.readyState || (u.d("windowLoad"),
                clearInterval(s))
            }
            ), 2e3)),
            t = "load",
            n = function() {
                u.d("windowLoad"),
                clearInterval(s)
            }
            ,
            i.addEventListener(t, n, {
                once: !0
            })
        }
        var t, n
    }
    )),
    i.ResizeObserver || (i.ResizeObserver = function() {
        "use strict";
        var t = function() {
            if ("undefined" != typeof Map)
                return Map;
            function t(t, i) {
                var n = -1;
                return t.some((function(t, s) {
                    return t[0] === i && (n = s,
                    !0)
                }
                )),
                n
            }
            return function() {
                function i() {
                    this.__entries__ = []
                }
                return Object.defineProperty(i.prototype, "size", {
                    get: function() {
                        return this.__entries__.length
                    },
                    enumerable: !0,
                    configurable: !0
                }),
                i.prototype.get = function(i) {
                    var n = t(this.__entries__, i)
                      , s = this.__entries__[n];
                    return s && s[1]
                }
                ,
                i.prototype.set = function(i, n) {
                    var s = t(this.__entries__, i);
                    ~s ? this.__entries__[s][1] = n : this.__entries__.push([i, n])
                }
                ,
                i.prototype.delete = function(i) {
                    var n = this.__entries__
                      , s = t(n, i);
                    ~s && n.splice(s, 1)
                }
                ,
                i.prototype.has = function(i) {
                    return !!~t(this.__entries__, i)
                }
                ,
                i.prototype.clear = function() {
                    this.__entries__.splice(0)
                }
                ,
                i.prototype.forEach = function(t, i) {
                    void 0 === i && (i = null);
                    for (var n = 0, s = this.__entries__; n < s.length; n++) {
                        var r = s[n];
                        t.call(i, r[1], r[0])
                    }
                }
                ,
                i
            }()
        }()
          , n = void 0 !== i && void 0 !== r && i.document === r
          , s = "undefined" != typeof global && global.Math === Math ? global : "undefined" != typeof self && self.Math === Math ? self : void 0 !== i && i.Math === Math ? i : Function("return this")()
          , o = "function" == typeof requestAnimationFrame ? requestAnimationFrame.bind(s) : function(t) {
            return h((function() {
                return t(Date.now())
            }
            ), 1e3 / 60)
        }
          , a = 2;
        function u(t, i) {
            var n = !1
              , s = !1
              , r = 0;
            function u() {
                n && (n = !1,
                t()),
                s && f()
            }
            function c() {
                o(u)
            }
            function f() {
                var t = Date.now();
                if (n) {
                    if (t - r < a)
                        return;
                    s = !0
                } else
                    n = !0,
                    s = !1,
                    h(c, i);
                r = t
            }
            return f
        }
        var c = 20
          , f = ["top", "right", "bottom", "left", "width", "height", "size", "weight"]
          , l = "undefined" != typeof MutationObserver
          , v = function() {
            function t() {
                this.connected_ = !1,
                this.mutationEventsAdded_ = !1,
                this.mutationsObserver_ = null,
                this.observers_ = [],
                this.onTransitionEnd_ = this.onTransitionEnd_.bind(this),
                this.refresh = u(this.refresh.bind(this), c)
            }
            return t.prototype.addObserver = function(t) {
                ~this.observers_.indexOf(t) || this.observers_.push(t),
                this.connected_ || this.connect_()
            }
            ,
            t.prototype.removeObserver = function(t) {
                var i = this.observers_
                  , n = i.indexOf(t);
                ~n && i.splice(n, 1),
                !i.length && this.connected_ && this.disconnect_()
            }
            ,
            t.prototype.refresh = function() {
                this.updateObservers_() && this.refresh()
            }
            ,
            t.prototype.updateObservers_ = function() {
                var t = this.observers_.filter((function(t) {
                    return t.gatherActive(),
                    t.hasActive()
                }
                ));
                return t.forEach((function(t) {
                    return t.broadcastActive()
                }
                )),
                t.length > 0
            }
            ,
            t.prototype.connect_ = function() {
                n && !this.connected_ && (r.addEventListener("transitionend", this.onTransitionEnd_),
                i.addEventListener("resize", this.refresh),
                l ? (this.mutationsObserver_ = new MutationObserver(this.refresh),
                this.mutationsObserver_.observe(r, {
                    attributes: !0,
                    childList: !0,
                    characterData: !0,
                    subtree: !0
                })) : (r.addEventListener("DOMSubtreeModified", this.refresh),
                this.mutationEventsAdded_ = !0),
                this.connected_ = !0)
            }
            ,
            t.prototype.disconnect_ = function() {
                n && this.connected_ && (r.removeEventListener("transitionend", this.onTransitionEnd_),
                i.removeEventListener("resize", this.refresh),
                this.mutationsObserver_ && this.mutationsObserver_.disconnect(),
                this.mutationEventsAdded_ && r.removeEventListener("DOMSubtreeModified", this.refresh),
                this.mutationsObserver_ = null,
                this.mutationEventsAdded_ = !1,
                this.connected_ = !1)
            }
            ,
            t.prototype.onTransitionEnd_ = function(t) {
                var i = t.propertyName
                  , n = void 0 === i ? "" : i;
                f.some((function(t) {
                    return !!~n.indexOf(t)
                }
                )) && this.refresh()
            }
            ,
            t.getInstance = function() {
                return this.instance_ || (this.instance_ = new t),
                this.instance_
            }
            ,
            t.instance_ = null,
            t
        }()
          , d = function(t, i) {
            for (var n = 0, s = Object.keys(i); n < s.length; n++) {
                var r = s[n];
                Object.defineProperty(t, r, {
                    value: i[r],
                    enumerable: !1,
                    writable: !1,
                    configurable: !0
                })
            }
            return t
        }
          , p = function(t) {
            return t && t.ownerDocument && t.ownerDocument.defaultView || s
        }
          , m = k(0, 0, 0, 0);
        function g(t) {
            return parseFloat(t) || 0
        }
        function b(t) {
            for (var i = [], n = 1; n < arguments.length; n++)
                i[n - 1] = arguments[n];
            return i.reduce((function(i, n) {
                return i + g(t["border-" + n + "-width"])
            }
            ), 0)
        }
        function w(t) {
            for (var i = {}, n = 0, s = ["top", "right", "bottom", "left"]; n < s.length; n++) {
                var r = s[n]
                  , o = t["padding-" + r];
                i[r] = g(o)
            }
            return i
        }
        function y(t) {
            var i = t.getBBox();
            return k(0, 0, i.width, i.height)
        }
        function M(t) {
            var i = t.clientWidth
              , n = t.clientHeight;
            if (!i && !n)
                return m;
            var s = p(t).getComputedStyle(t)
              , r = w(s)
              , o = r.left + r.right
              , h = r.top + r.bottom
              , a = g(s.width)
              , u = g(s.height);
            if ("border-box" === s.boxSizing && (Math.round(a + o) !== i && (a -= b(s, "left", "right") + o),
            Math.round(u + h) !== n && (u -= b(s, "top", "bottom") + h)),
            !_(t)) {
                var c = Math.round(a + o) - i
                  , f = Math.round(u + h) - n;
                1 !== Math.abs(c) && (a -= c),
                1 !== Math.abs(f) && (u -= f)
            }
            return k(r.left, r.top, a, u)
        }
        var x = "undefined" != typeof SVGGraphicsElement ? function(t) {
            return t instanceof p(t).SVGGraphicsElement
        }
        : function(t) {
            return t instanceof p(t).SVGElement && "function" == typeof t.getBBox
        }
        ;
        function _(t) {
            return t === p(t).document.documentElement
        }
        function O(t) {
            return n ? x(t) ? y(t) : M(t) : m
        }
        function P(t) {
            var i = t.x
              , n = t.y
              , s = t.width
              , r = t.height
              , o = "undefined" != typeof DOMRectReadOnly ? DOMRectReadOnly : Object
              , h = Object.create(o.prototype);
            return d(h, {
                x: i,
                y: n,
                width: s,
                height: r,
                top: n,
                right: i + s,
                bottom: r + n,
                left: i
            }),
            h
        }
        function k(t, i, n, s) {
            return {
                x: t,
                y: i,
                width: n,
                height: s
            }
        }
        var S = function() {
            function t(t) {
                this.broadcastWidth = 0,
                this.broadcastHeight = 0,
                this.contentRect_ = k(0, 0, 0, 0),
                this.target = t
            }
            return t.prototype.isActive = function() {
                var t = O(this.target);
                return this.contentRect_ = t,
                t.width !== this.broadcastWidth || t.height !== this.broadcastHeight
            }
            ,
            t.prototype.broadcastRect = function() {
                var t = this.contentRect_;
                return this.broadcastWidth = t.width,
                this.broadcastHeight = t.height,
                t
            }
            ,
            t
        }()
          , E = function() {
            function t(t, i) {
                var n = P(i);
                d(this, {
                    target: t,
                    contentRect: n
                })
            }
            return t
        }()
          , A = function() {
            function i(i, n, s) {
                if (this.activeObservations_ = [],
                this.observations_ = new t,
                "function" != typeof i)
                    throw new TypeError("The callback provided as parameter 1 is not a function.");
                this.callback_ = i,
                this.controller_ = n,
                this.callbackCtx_ = s
            }
            return i.prototype.observe = function(t) {
                if (!arguments.length)
                    throw new TypeError("1 argument required, but only 0 present.");
                if ("undefined" != typeof Element && Element instanceof Object) {
                    if (!(t instanceof p(t).Element))
                        throw new TypeError('parameter 1 is not of type "Element".');
                    var i = this.observations_;
                    i.has(t) || (i.set(t, new S(t)),
                    this.controller_.addObserver(this),
                    this.controller_.refresh())
                }
            }
            ,
            i.prototype.unobserve = function(t) {
                if (!arguments.length)
                    throw new TypeError("1 argument required, but only 0 present.");
                if ("undefined" != typeof Element && Element instanceof Object) {
                    if (!(t instanceof p(t).Element))
                        throw new TypeError('parameter 1 is not of type "Element".');
                    var i = this.observations_;
                    i.has(t) && (i.delete(t),
                    i.size || this.controller_.removeObserver(this))
                }
            }
            ,
            i.prototype.disconnect = function() {
                this.clearActive(),
                this.observations_.clear(),
                this.controller_.removeObserver(this)
            }
            ,
            i.prototype.gatherActive = function() {
                var t = this;
                this.clearActive(),
                this.observations_.forEach((function(i) {
                    i.isActive() && t.activeObservations_.push(i)
                }
                ))
            }
            ,
            i.prototype.broadcastActive = function() {
                if (this.hasActive()) {
                    var t = this.callbackCtx_
                      , i = this.activeObservations_.map((function(t) {
                        return new E(t.target,t.broadcastRect())
                    }
                    ));
                    this.callback_.call(t, i, t),
                    this.clearActive()
                }
            }
            ,
            i.prototype.clearActive = function() {
                this.activeObservations_.splice(0)
            }
            ,
            i.prototype.hasActive = function() {
                return this.activeObservations_.length > 0
            }
            ,
            i
        }()
          , C = "undefined" != typeof WeakMap ? new WeakMap : new t
          , D = function() {
            function t(i) {
                if (!(this instanceof t))
                    throw new TypeError("Cannot call a class as a function.");
                if (!arguments.length)
                    throw new TypeError("1 argument required, but only 0 present.");
                var n = v.getInstance()
                  , s = new A(i,n,this);
                C.set(this, s)
            }
            return t
        }();
        return ["observe", "unobserve", "disconnect"].forEach((function(t) {
            D.prototype[t] = function() {
                var i;
                return (i = C.get(this))[t].apply(i, arguments)
            }
        }
        )),
        void 0 !== s.ResizeObserver ? s.ResizeObserver : D
    }()),
    u.StringHelper = {
        capitalize: function(t) {
            return t.charAt(0).toUpperCase() + t.slice(1)
        }
    },
    function() {
        var t = function(t) {
            return Symbol.iterator in Object(t)
        }
          , i = {
            add: function(t) {
                requestAnimationFrame(t)
            },
            remove: function() {}
        };
        u.t = u.t || i;
        var n, r = new class {
            constructor() {
                this.i = !1,
                this.o = new Set,
                this.h = this.u.bind(this)
            }
            add(t) {
                this.o.add(t),
                this.i || (this.i = !0,
                u.t.add(this.h))
            }
            u() {
                for (let t of this.o)
                    t.render ? t.render() : t();
                this.o.clear(),
                this.i = !1,
                u.t.remove(this.h)
            }
        }
        , o = {};
        n = /Safari/i.test(navigator.userAgent) && !/Chrom[ei]/i.test(navigator.userAgent) ? function(t, i, n, s) {
            var r = "";
            return s && (t || i || n) ? "translate3d(" + (t || 0) + "px, " + (i || 0) + "px, " + (n || 0) + "px) " : ((t || i) && (r += "translate(" + (t || 0) + "px, " + (i || 0) + "px) "),
            n && (r += "translateZ(" + n + "px) "),
            r)
        }
        : function(t, i, n, s) {
            return t || i || n ? "translate3d(" + (t || 0) + "px, " + (i || 0) + "px, " + (n || 0) + "px) " : ""
        }
        ;
        class h {
            constructor(i, n) {
                t(i) ? this.l = Array.from(i) : this.l = [i],
                this.v = f({}, n),
                this.p = new Set
            }
            get scale() {
                return this.scaleX
            }
            set scale(t) {
                this.scaleX = t,
                this.scaleY = t
            }
            render() {
                for (let i of this.p) {
                    var t = this.v[i];
                    switch (i) {
                    case "transform":
                        t = this.m();
                        break;
                    case "filter":
                        t = this.g();
                        break;
                    case "n2AutoAlpha":
                        0 === this.opacity ? this.M || (this.M = !0,
                        this._("data-force-hidden", "")) : this.M && (this.M = !1,
                        this.O("data-force-hidden"));
                        continue;
                    case "opacity":
                        this.v.n2AutoAlpha && (0 === t ? this.M || (this.M = !0,
                        this._("data-force-hidden", "")) : this.M && (this.M = !1,
                        this.O("data-force-hidden")));
                        break;
                    case "width":
                    case "height":
                    case "perspective":
                        "number" == typeof t && (t += "px")
                    }
                    for (let n of this.l)
                        n.style.setProperty(i, t)
                }
                this.p.clear()
            }
            m() {
                let {xP: t, yP: i, x: r, y: o, z: h, xAbs: a, yAbs: u, xPAbs: c, yPAbs: f, parallaxX: l, parallaxY: v, parallaxRotationX: d, parallaxRotationY: p, layerRotation: m, rotationZ: g, rotationY: b, rotationX: w, scaleX: y, scaleY: M, scaleZ: x, skewX: _, skewY: O, transformPerspective: P, force3D: k} = this.v
                  , S = "";
                return P && (S += "perspective(" + P + "px) "),
                (t || i) && (S += "translate(" + (t || 0) + "%, " + (i || 0) + "%) "),
                (a || u) && (S += "translate(" + (a || 0) + "px, " + (u || 0) + "px) "),
                (c || f) && (S += "translate(" + (c || 0) + "%, " + (f || 0) + "%) "),
                S += n(r, o, h, k),
                _ && (S += "skewX(" + _ + "deg) "),
                O && (S += "skewY(" + O + "deg) "),
                m && (S += "rotate(" + m + "deg) "),
                g && (S += "rotate(" + g + "deg) "),
                b && (S += "rotateY(" + b + "deg) "),
                w && (S += "rotateX(" + w + "deg) "),
                y === s && (y = 1),
                M === s && (M = 1),
                1 === y && 1 === M || (S += "scale(" + y + ", " + M + ") "),
                x === s && (x = 1),
                1 !== x && (S += "scaleZ(" + x + ") "),
                S || "translate3d(0, 0, 0)"
            }
            g() {
                let {n2blur: t} = this.v
                  , i = "";
                return t > .1 && (i = "blur(" + t + "px) "),
                i || "none"
            }
            _(t, i) {
                for (let n of this.l)
                    (n.relatedLayer || n).setAttribute(t, i)
            }
            O(t) {
                for (let i of this.l)
                    (i.relatedLayer || i).removeAttribute(t)
            }
            setValues(t) {
                for (var i in t)
                    this[i] = t[i]
            }
        }
        class a {
            constructor(t, i) {
                this.P = [];
                for (var n = 0; n < t.length; n++)
                    t[n] && this.P.push(b.k(t[n], i))
            }
            setValues(t) {
                for (var i = 0; i < this.P.length; i++)
                    this.P[i].setValues(t)
            }
        }
        var c = {}
          , l = function(t) {
            Object.defineProperty(a.prototype, t, {
                get: function() {
                    return this.P[0][t]
                },
                set: function(i) {
                    i instanceof Function && (i = i());
                    for (var n = 0; n < this.P.length; n++)
                        this.P[n][t] = i
                }
            })
        }
          , v = function(t, i, n) {
            Object.getOwnPropertyDescriptor(h.prototype, t) || (i === s && (i = ""),
            n === s && (n = t),
            c[n] === s && (c[n] = []),
            c[n].push(t),
            o[t] = i,
            Object.defineProperty(h.prototype, t, {
                get: function() {
                    return this.v[t] === s && (this.v[t] = o[t]),
                    this.v[t]
                },
                set: function(i) {
                    i instanceof Function && (i = i()),
                    this.v[t] !== i && (this.v[t] = i,
                    this.p.add(n),
                    r.add(this))
                }
            }),
            l(t))
        }
          , d = function(t, i) {
            Object.defineProperty(h.prototype, t, {
                get: function() {
                    return this[i]
                },
                set: function(t) {
                    this[i] = t
                }
            }),
            l(t)
        };
        v("property"),
        v("display"),
        v("z-index", 1),
        v("overflow", "visible"),
        v("overflow-x", "visible"),
        v("backface-visibility", "visible"),
        v("transform-origin", "50% 50% 0"),
        v("opacity", 1),
        v("width", 0),
        v("height", 0),
        v("justify-content"),
        v("background"),
        v("color"),
        v("will-change", ""),
        v("stroke-dasharray", ""),
        v("visibility"),
        v("perspective", 0),
        v("transform-style"),
        v("cursor", ""),
        v("top"),
        v("right"),
        v("bottom"),
        v("left");
        var p = "transform";
        v("force3D", 0, p),
        v("transformPerspective", 0, p),
        v("xP", 0, p),
        v("yP", 0, p),
        v("x", 0, p),
        v("y", 0, p),
        v("z", 0, p),
        v("xAbs", 0, p),
        v("yAbs", 0, p),
        v("xPAbs", 0, p),
        v("yPAbs", 0, p),
        v("scaleX", 1, p),
        v("scaleY", 1, p),
        v("scaleZ", 1, p),
        v("rotationZ", 0, p),
        v("rotationX", 0, p),
        v("rotationY", 0, p),
        v("skewX", 0, p),
        v("skewY", 0, p),
        v("layerRotation", 0, p),
        v("n2blur", 0, "filter"),
        v("n2AutoAlpha", 0),
        d("zIndex", "z-index"),
        d("backfaceVisibility", "backface-visibility"),
        d("transformOrigin", "transform-origin"),
        d("justifyContent", "justify-content"),
        d("transformStyle", "transform-style"),
        d("overflowX", "overflow-x"),
        d("clipPath", "clip-path");
        var m = {
            0: function(t) {
                this.classList.add(t)
            },
            1: function(t) {
                this.classList.remove(t)
            }
        };
        class g {
            constructor(i) {
                t(i) ? this.l = Array.from(i) : this.l = [i],
                this.S = []
            }
            addClass(t) {
                this.S.push([0, t]),
                r.add(this)
            }
            removeClass(t) {
                this.S.push([1, t]),
                r.add(this)
            }
            render() {
                for (var t = 0; t < this.S.length; t++) {
                    var i = this.S[t];
                    for (let t of this.l)
                        m[i[0]].apply(t, i.splice(1))
                }
                this.S = []
            }
        }
        var b = u.MW = {
            A: v,
            k: function(t, i) {
                return t.smw || (t.smw = new h(t,i)),
                t.smw
            },
            C: function(t, i) {
                for (var n = [], s = 0; s < t.length; s++)
                    n.push(b.k(t[s], i));
                return n
            },
            D: function(t, i) {
                return new a(t,i)
            },
            R: function(t, i) {
                t.forEach((function(t) {
                    t.setValues(i)
                }
                ))
            },
            T: function(t, i) {
                i.forEach((function(i) {
                    c[i] && c[i].forEach((function(i) {
                        t[i] = o[i]
                    }
                    ))
                }
                ))
            },
            j: function(t, i) {
                r.add((function() {
                    t.className.add(i)
                }
                ))
            },
            F: function(t, i) {
                r.add((function() {
                    t.className.remove(i)
                }
                ))
            },
            L: function(t) {
                r.add(t)
            },
            N: function(t) {
                return t.umw || (t.umw = new g(t)),
                t.umw
            },
            flush() {
                r.u()
            }
        }
    }(),
    customElements.define("ss3-force-full-width", class extends HTMLElement {
        connectedCallback() {
            if (this.isConnected) {
                n = n || r.body,
                this.I = "rtl" === o.getAttribute("dir"),
                this._width = 0,
                this._offset = Number.MAX_SAFE_INTEGER,
                this.X = u.MW.k(this, {
                    x: Number.MAX_SAFE_INTEGER,
                    opacity: 0
                });
                var t = d(this, "overflowX");
                t && "none" !== t && r.querySelectorAll(t).forEach((function(t) {
                    u.MW.k(t).overflowX = "hidden"
                }
                ));
                var s = n;
                if (i.ssForceFullHorizontalSelector)
                    s = i.ssForceFullHorizontalSelector;
                else {
                    var h = d(this, "horizontalSelector");
                    if (h && "body" !== h)
                        try {
                            s = this.closest(h)
                        } catch (t) {}
                }
                if (s === n)
                    for (var a = [".elementor-section-stretched", '.siteorigin-panels-stretch[data-stretch-type="full-stretched"]', '.siteorigin-panels-stretch[data-stretch-type="full-stretched-padded"]', ".themify_builder_row.fullwidth", '.vce-row[data-vce-stretch-content="true"]'], c = 0; c < a.length; c++) {
                        var f = this.closest(a[c]);
                        if (f) {
                            s = f;
                            break
                        }
                    }
                this.fullWidthTo = s,
                this.resizeObserver = new ResizeObserver(this.doResize.bind(this)),
                this.resizeObserver.observe(this.parentNode),
                i.addEventListener("resize", this.doResize.bind(this))
            }
        }
        doResize() {
            var t = 0
              , s = 0;
            if (this.fullWidthTo) {
                var r = this.fullWidthTo.getBoundingClientRect();
                s = r.width,
                t = this.I ? -s + r.right : r.left
            }
            var o, h, a = s > 0 ? s : n.clientWidth, u = i.getComputedStyle(this.parentNode);
            o = this.I ? a - this.parentNode.getBoundingClientRect().right - parseInt(u.getPropertyValue("padding-right")) - parseInt(u.getPropertyValue("border-right-width")) + t : -this.parentNode.getBoundingClientRect().left - parseInt(u.getPropertyValue("padding-left")) - parseInt(u.getPropertyValue("border-left-width")) + t,
            o += h = o % 1,
            a -= Math.floor(h),
            (this._width - a <= 0 || this._width - a > 1 || this._offset - o < -1 || this._offset - o >= 0) && (this._offset !== o && (this.X.x = o,
            this._offset = o,
            0 !== o && this.classList.add("n2-ss-no-bga-fixed")),
            this._width !== a && (this.X.width = a,
            this._width = a)),
            this.setVisible && this.setVisible()
        }
        setVisible() {
            this.X.opacity = 1,
            delete this.setVisible
        }
    }
    ),
    customElements.define("ss3-loader", class extends HTMLElement {
        connectedCallback() {
            this.B = []
        }
        set display(t) {
            this.V !== t && (this.V = t,
            this.style.display = t)
        }
        show() {
            this.display = "grid"
        }
        addPromise(t) {
            this.B.push(t),
            this.syncStyle(),
            t.finally(this.removePromise.bind(this, t))
        }
        removePromise(t) {
            var i = this.B.indexOf(t);
            i > -1 && (this.B.splice(i, 1),
            this.syncStyle())
        }
        syncStyle() {
            this.B.length ? this.Y || (this.Y = h(this.show.bind(this), 100)) : (this.Y && (a(this.Y),
            delete this.Y),
            this.display = "")
        }
    }
    );
    var O, P = "MacIntel" === navigator.platform && navigator.maxTouchPoints > 1;
    i.n2const = {
        devicePixelRatio: i.devicePixelRatio || 1,
        isFirefox: /Firefox/i.test(navigator.userAgent),
        isIOS: /iPad|iPhone|iPod/.test(navigator.platform) || P,
        isMobile: /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini|Silk/i.test(navigator.userAgent) || P,
        isPhone: /Android/i.test(navigator.userAgent) && /mobile/i.test(navigator.userAgent) || /webOS|iPhone|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent),
        isSamsungBrowser: navigator.userAgent.match(/SamsungBrowser/i),
        isBot: /bot|googlebot|crawler|spider|robot|crawling|Google Search Console/i.test(navigator.userAgent),
        isLighthouse: navigator.userAgent.indexOf("Chrome-Lighthouse") > -1,
        lightboxMobileNewTab: 1,
        isVideoAutoplayAllowed: function() {
            var t = !!(navigator.platform.match(/(Win|Mac)/) || !/Mobi/.test(navigator.userAgent) || "playsInline"in c("video") || "webkit-playsinline"in c("video") || navigator.userAgent.match(/Chrom(e|ium)\/([0-9]+)\./) && parseInt(navigator.userAgent.match(/Chrom(e|ium)\/([0-9]+)\./)[2]) >= 53 || navigator.userAgent.match(/Android.*(Firefox|Edge|Opera)/));
            return i.n2const.isVideoAutoplayAllowed = function() {
                return t
            }
            ,
            t
        },
        isWaybackMachine: function() {
            var t = void 0 !== i.__wm;
            return i.n2const.isWaybackMachine = function() {
                return t
            }
            ,
            t
        },
        setLocation: function(t) {
            "function" == typeof i.zajax_goto ? i.zajax_goto(t) : i.location = t
        },
        isParentSameOrigin: function() {
            try {
                return parent.document,
                !0
            } catch (t) {}
            return !1
        },
        activeElementBlur: function() {
            r.activeElement && r.activeElement.blur()
        },
        getScrollbarSize: function() {
            var t, i, s, r = c("div", t, i, s);
            r.style.visibility = "hidden",
            r.style.overflow = "scroll",
            n.appendChild(r);
            var o = r.offsetHeight - r.clientHeight;
            return n.removeChild(r),
            n2const.getScrollbarSize = function() {
                return o
            }
            ,
            o
        },
        fonts: new Promise((function(t) {
            if ("fonts"in r) {
                r.fonts.ready.then(t);
                const i = navigator.userAgent;
                i.indexOf("Safari") > 0 && -1 === i.indexOf("Chrome") && u.r("windowLoad", t)
            } else
                u.r("windowLoad", t)
        }
        ))
    },
    i.n2const.isTablet = !i.n2const.isPhone && (/Android|iPad|tablet|Silk/i.test(navigator.userAgent) || P),
    i.n2const.rtl = (i.n2const.isRTL = function() {
        return i.n2const.rtl.isRtl
    }
    ,
    "rtl" === o.getAttribute("dir") ? {
        isRtl: !0,
        marginLeft: "marginRight",
        marginRight: "marginLeft",
        "margin-left": "margin-right",
        "margin-right": "margin-left",
        left: "right",
        right: "left",
        modifier: -1
    } : {
        isRtl: !1,
        marginLeft: "marginLeft",
        marginRight: "marginRight",
        "margin-left": "margin-left",
        "margin-right": "margin-right",
        left: "left",
        right: "right",
        modifier: 1
    }),
    u._triggerResize = (O = null,
    function() {
        O && a(O),
        O = h((function() {
            y(i, "resize"),
            O = null
        }
        ), 100)
    }
    ),
    u._shouldPreventClick = !1,
    u._preventClick = function() {
        u._shouldPreventClick || (u._shouldPreventClick = !0,
        h((function() {
            u._shouldPreventClick = !1
        }
        ), 300))
    }
    ,
    u.d("ImagesLoaded", (function() {
        return function(t) {
            var i, n = [];
            i = "IMG" === t.tagName ? [t] : t.querySelectorAll("img");
            for (var s = 0; s < i.length; s++) {
                var r = i[s];
                r.loading = "eager",
                r.complete ? r.naturalWidth || n.push(new Promise(function(t) {
                    h(t, 16)
                }
                .bind(r))) : n.push(new Promise(function(t, i) {
                    this.addEventListener("load", (function() {
                        t()
                    }
                    )),
                    this.addEventListener("error", (function() {
                        i()
                    }
                    ))
                }
                .bind(r)))
            }
            return Promise.all(n)
        }
    }
    )),
    u.d("UniversalPointer", (function() {
        var t = !!i.PointerEvent
          , n = !!i.TouchEvent;
        function r(t, i) {
            this.el = t,
            this.handler = i,
            this.preventMouse = !1,
            this.timeouts = [],
            this.localListeners = [],
            this.globalListeners = []
        }
        function c(i, s, h) {
            var a = this.context = new r(i,s);
            a.addGlobalEventListener("click", (function(e) {
                a.preventMouse || a.click(e)
            }
            )),
            t ? a.addGlobalEventListener("pointerdown", (function(t) {
                t.isPrimary && (a.startComplexInteraction(t.currentTarget),
                a.addLocalEventListener(o, "pointerup", (function(i) {
                    i.isPrimary && t.pointerId === i.pointerId && (Math.abs(i.clientX - t.clientX) < 10 && Math.abs(i.clientY - t.clientY) < 10 ? a.click(i) : a.clear(),
                    a.endComplexInteraction())
                }
                )))
            }
            )) : n && a.addGlobalEventListener("touchstart", (function(t) {
                a.clearTimeouts(),
                a.startComplexInteraction(t.currentTarget),
                a.addLocalEventListener(o, "touchend", (function(i) {
                    Math.abs(i.changedTouches[0].clientX - t.changedTouches[0].clientX) < 10 && Math.abs(i.changedTouches[0].clientY - t.changedTouches[0].clientY) < 10 ? a.click(i) : a.clear(),
                    a.endComplexInteraction()
                }
                ), {
                    passive: !0
                })
            }
            ), {
                passive: !0
            })
        }
        function l(t, i, n) {
            this.el = t,
            this.handler = i,
            this.leaveOnSecond = n,
            this.preventMouse = !1,
            this.isActive = !1,
            this.timeouts = [],
            this.localListeners = [],
            this.globalListeners = []
        }
        function v(i, s, r) {
            r = f({
                leaveOnSecond: !1
            }, r);
            var a = this.context = new l(i,s,r.leaveOnSecond);
            t ? (a.addGlobalEventListener("pointerenter", (function(e) {
                e.isPrimary && (a.clearTimeouts(),
                a.enter(e) && "mouse" !== e.pointerType && (a.addLocalEventListener(o, "pointerdown", (function(e) {
                    e.isPrimary && a.testLeave(e.target)
                }
                )),
                a.addTimeout(h((function() {
                    a.leave()
                }
                ), 5e3))))
            }
            )),
            a.addGlobalEventListener("pointerleave", (function(e) {
                e.isPrimary && "mouse" === e.pointerType && a.leave()
            }
            ))) : (a.addGlobalEventListener("mouseenter", (function(e) {
                a.preventMouse || a.enter(e)
            }
            )),
            a.addGlobalEventListener("mouseleave", (function() {
                a.preventMouse || a.leave()
            }
            )),
            n && a.addGlobalEventListener("touchstart", (function(e) {
                a.preventMouse = !0,
                a.clearTimeouts(),
                a.enter(e) && (a.addLocalEventListener(o, "touchstart", (function(e) {
                    a.testLeave(e.target)
                }
                )),
                a.addTimeout(h((function() {
                    a.leave(),
                    a.preventMouse = !1
                }
                ), 5e3)))
            }
            ), {
                passive: !0
            }))
        }
        r.prototype.addTimeout = function(t) {
            this.timeouts.push(t)
        }
        ,
        r.prototype.clearTimeouts = function() {
            for (var t = 0; t < this.timeouts.length; t++)
                a(this.timeouts[t]);
            this.timeouts = []
        }
        ,
        r.prototype.click = function(e) {
            this.currentTarget !== s && (e = {
                currentTarget: this.currentTarget,
                target: this.el
            }),
            this.handler.call(this.el, e),
            this.clear()
        }
        ,
        r.prototype.clear = function() {
            for (var t = 0; t < this.localListeners.length; t++)
                this.localListeners[t][0].removeEventListener(this.localListeners[t][1], this.localListeners[t][2], this.localListeners[t][3])
        }
        ,
        r.prototype.addGlobalEventListener = function(t, i, n) {
            this.globalListeners.push(M(this.el, t, i, n))
        }
        ,
        r.prototype.addLocalEventListener = function(t, i, n, s) {
            this.localListeners.push([t, i, n, s]),
            t.addEventListener(i, n, s)
        }
        ,
        r.prototype.remove = function() {
            this.clear(),
            this.clearTimeouts();
            for (var t = 0; t < this.globalListeners.length; t++)
                this.globalListeners[t]();
            delete this.globalListeners
        }
        ,
        r.prototype.startComplexInteraction = function(t) {
            this.clearTimeouts(),
            this.preventMouse = !0,
            this.currentTarget = t
        }
        ,
        r.prototype.endComplexInteraction = function() {
            delete this.currentTarget,
            this.addTimeout(h(function() {
                this.preventMouse = !1
            }
            .bind(this), 1e3))
        }
        ,
        c.prototype.remove = function() {
            this.context.remove(),
            delete this.context
        }
        ,
        u.UniversalClick = c,
        l.prototype.enter = function(e) {
            return this.leaveOnSecond && this.isActive ? (this.leave(),
            !1) : (this.handler.apply(this.el, arguments),
            this.isActive = !0,
            !0)
        }
        ,
        l.prototype.leave = function() {
            this.clearTimeouts();
            for (var t = 0; t < this.localListeners.length; t++)
                this.localListeners[t][0].removeEventListener(this.localListeners[t][1], this.localListeners[t][2], this.localListeners[t][3]);
            var i;
            this.isActive = !1,
            i = this.el,
            w(i, new Event("universalleave",{
                bubbles: !1,
                cancelable: !1
            }))
        }
        ,
        l.prototype.testLeave = function(t) {
            !this.el === t && this.el.contains(t) && this.leave()
        }
        ,
        l.prototype.addTimeout = function(t) {
            this.timeouts.push(t)
        }
        ,
        l.prototype.clearTimeouts = function() {
            for (var t = 0; t < this.timeouts.length; t++)
                a(this.timeouts[t]);
            this.timeouts = []
        }
        ,
        l.prototype.addGlobalEventListener = function(t, i, n) {
            this.globalListeners.push(M(this.el, t, i, n))
        }
        ,
        l.prototype.remove = function() {
            this.isActive && this.leave(),
            this.clearTimeouts();
            for (var t = 0; t < this.globalListeners.length; t++)
                this.globalListeners[t]();
            delete this.globalListeners
        }
        ,
        l.prototype.addLocalEventListener = function(t, i, n, s) {
            this.localListeners.push([t, i, n, s]),
            t.addEventListener(i, n, s)
        }
        ,
        v.prototype.remove = function() {
            this.context.remove(),
            delete this.context
        }
        ,
        u.UniversalEnter = v
    }
    )),
    u.d("EventBurrito", (function() {
        var t = function() {
            return !0
        }
          , o = !1;
        return function(a, u) {
            var c = {
                preventDefault: !0,
                preventScroll: !1,
                mouse: !0,
                axis: "x",
                start: t,
                move: t,
                end: t,
                click: t
            };
            f(c, u);
            var l, v, d, p, m = {
                pointerEvents: !(n2const.isIOS && i.TouchEvent || !i.PointerEvent && !i.PointerEventsPolyfill)
            }, g = {}, b = {}, w = [], x = !0, _ = m.pointerEvents ? 1 : 0, O = [["touchstart", "touchmove", "touchend", "touchcancel"], ["pointerdown", "pointermove", "pointerup", "pointercancel", "pointerleave"], ["mousedown", "mousemove", "mouseup", "", "mouseleave"]], P = [function(e) {
                return e.touches && e.touches.length > 1 || e.scale && 1 !== e.scale
            }
            , function(e) {
                return !e.isPrimary || e.buttons && 1 !== e.buttons || !c.mouse && "touch" !== e.pointerType && "pen" !== e.pointerType
            }
            , function(e) {
                return e.buttons && 1 !== e.buttons
            }
            ], k = function(t, i) {
                var n = t.tagName;
                return "INPUT" === n || "TEXTAREA" === n || "SELECT" === n || "BUTTON" === n || "VIDEO" === n || t.classList.contains("n2_container_scrollable") || t.closest(".n2_container_scrollable")
            };
            function S(t) {
                t.preventDefault ? t.preventDefault() : t.returnValue = !1
            }
            function E(t) {
                b = {
                    x: (p ? t.clientX : t.touches[0].clientX) - g.x,
                    y: (p ? t.clientY : t.touches[0].clientY) - g.y,
                    time: Date.now()
                }
            }
            function A(e, t) {
                if ((e.isPrimary === s || e.isPrimary) && !o && (l = [],
                x = !0,
                !P[p = t](e) && !k(e.target)))
                    if (("mousedown" === e.type || "pointerdown" === e.type && "mouse" === e.pointerType) && e.target.closest(".n2-ss-text")) {
                        var i = M(e.target, "click", (function(t) {
                            i(),
                            Math.abs(e.clientX - t.clientX) < 10 && Math.abs(e.clientY - t.clientY) < 10 && y(e.target, "n2click")
                        }
                        ));
                        h(i, 2e3)
                    } else
                        o = !0,
                        0 !== p && l.push(M(r, O[p][1], C, {
                            passive: !1,
                            capture: !0
                        })),
                        l.push(M(r, O[p][2], R, {
                            passive: !1,
                            capture: !0
                        })),
                        l.push(M(r, O[p][3], R, {
                            passive: !1,
                            capture: !0
                        })),
                        l.push(M(n, O[p][4], D, {
                            passive: !1,
                            capture: !0
                        })),
                        g = {
                            x: p ? e.clientX : e.touches[0].clientX,
                            y: p ? e.clientY : e.touches[0].clientY,
                            time: Date.now()
                        },
                        v = s,
                        d = !1,
                        b = {
                            x: 0,
                            y: 0
                        },
                        c.start(e, g),
                        C(e)
            }
            function C(t) {
                (t.isPrimary === s || t.isPrimary) && ("x" === c.axis && (!c.preventScroll && v || P[p](t)) || k(t.target) || (E(t),
                (Math.abs(b.x) > 10 || Math.abs(b.y) > 10) && (x = !1),
                v === s && 2 !== p && (v = Math.abs(b.x) < Math.abs(b.y) && !c.preventScroll) || c.move(t, g, b, d) && c.preventDefault && S(t)))
            }
            function D(t) {
                t.target === t.currentTarget && R(t)
            }
            function R(t) {
                if (t.isPrimary === s || t.isPrimary) {
                    p && E(t),
                    !x && t.target && t.target.blur && t.target.blur();
                    for (var i = l.length - 1; i >= 0; i--)
                        l[i]();
                    l = null,
                    n2const.isFirefox && (d = !1),
                    c.end(t, g, b, d),
                    d = !1,
                    o = !1
                }
            }
            return w.push(M(r, "scroll", (function() {
                i.nextendScrollFocus !== s && i.nextendScrollFocus || (d = !0)
            }
            ))),
            1 === _ && (a.style.touchAction = "y" === c.axis ? "pan-x" : "pan-y"),
            w.push(M(a, O[_][0], (function(e) {
                A(e, _)
            }
            ), {
                passive: !1,
                capture: !0
            })),
            0 === _ && w.push(M(a, O[0][1], (function(e) {
                C(e)
            }
            ), {
                passive: !1,
                capture: !0
            })),
            w.push(M(a, "dragstart", S)),
            c.mouse && 0 === _ && w.push(M(a, O[2][0], (function(e) {
                A(e, 2)
            }
            ))),
            w.push(M(a, "click", (function(t) {
                x ? c.click(t) : S(t)
            }
            ))),
            {
                supportsPointerEvents: m.pointerEvents,
                getClicksAllowed: function() {
                    return x
                },
                kill: function() {
                    for (var t = w.length - 1; t >= 0; t--)
                        w[t]()
                }
            }
        }
    }
    )),
    function() {
        var t, n = !1, s = -1, r = new Set, o = new Set, a = i.requestAnimationFrame || (t = 0,
        function(i) {
            var n = (new Date).getTime()
              , s = Math.max(0, 16 - (n - t));
            return t = n + s,
            h((function() {
                i(n + s)
            }
            ), s)
        }
        );
        function c(t) {
            s = t,
            n && (s = t,
            a(f))
        }
        function f(t) {
            if (n && 0 === r.size && 0 === o.size)
                s = -1,
                n = !1;
            else if (-1 !== s) {
                var i = (t - s) / 1e3;
                0 !== i && (r.forEach((function(t) {
                    t(i)
                }
                )),
                o.forEach((function(t) {
                    t()
                }
                )))
            }
            !function(t) {
                n && (s = t,
                a(f))
            }(t)
        }
        u.t = {
            addP: function(t) {
                r.add(t),
                n || (n = !0,
                a(c))
            },
            removeP: function(t) {
                r.delete(t)
            },
            add: function(t) {
                o.add(t),
                n || (n = !0,
                a(c))
            },
            remove: function(t) {
                o.delete(t)
            }
        }
    }(),
    u.d("Animation", (function() {
        function t(t) {
            this._tickCallback = null,
            this._progress = 0,
            this._delayTimeout = !1,
            this._delay = 0,
            this._duration = 4,
            this._timeScale = 1,
            this._isPlaying = !1,
            this._startTime = 0,
            this._eventCallbacks = {},
            this._immediateRender = !0,
            this._timeline = null,
            this._isCompleted = !1,
            this._isStarted = !1,
            this._isReversed = !1,
            this.toParams = t,
            this.initParameters()
        }
        return t.prototype.initParameters = function() {
            this.parseParameters(this.toParams),
            "object" != typeof this.toParams && this.paused(!1)
        }
        ,
        t.prototype.parseParameters = function(t) {
            t && (t.delay && (this.delay(t.delay),
            delete t.delay),
            t.duration !== s && (this.duration(t.duration),
            delete t.duration),
            t.onComplete && (this.eventCallback("onComplete", t.onComplete),
            delete t.onComplete),
            t.onStart && (this.eventCallback("onStart", t.onStart),
            delete t.onStart),
            t.onUpdate && (this.eventCallback("onUpdate", t.onUpdate),
            delete t.onUpdate),
            t.immediateRender && (this._immediateRender = t.immediateRender,
            delete t.immediateRender),
            t.paused && (this.paused(!0),
            delete t.paused))
        }
        ,
        t.prototype.setTimeline = function(t) {
            this._timeline = t
        }
        ,
        t.prototype._tick = function(t) {
            var i = this._progress;
            this._isReversed ? (this._progress -= t / this._duration * this._timeScale,
            1 != i && this._isStarted ? this._progress <= 0 ? (this._progress = 0,
            this._isPlaying = !1,
            u.t.removeP(this.getTickCallback()),
            this._onUpdate(),
            this._onReverseComplete()) : this._onUpdate() : this._onReverseStart()) : (this._progress += t / this._duration * this._timeScale,
            0 != i && this._isStarted ? this._progress >= 1 ? (this._progress = 1,
            this._isPlaying = !1,
            u.t.removeP(this.getTickCallback()),
            this._onUpdate(),
            this._onComplete()) : this._onUpdate() : this._onStart())
        }
        ,
        t.prototype._onStart = function() {
            this._isStarted = !0,
            this._isCompleted = !1,
            this._dispatch("onStart"),
            this._onUpdate()
        }
        ,
        t.prototype._onUpdate = function() {
            this._dispatch("onUpdate")
        }
        ,
        t.prototype._onComplete = function() {
            this._isCompleted = !0,
            this._onUpdate(),
            this._dispatch("onComplete")
        }
        ,
        t.prototype._onReverseComplete = function() {
            this._isCompleted = !0,
            this._isReversed = !1,
            this._onUpdate(),
            this._dispatch("onReverseComplete")
        }
        ,
        t.prototype._onReverseStart = function() {
            this._isStarted = !0,
            this._isCompleted = !1,
            this._dispatch("onReverseStart"),
            this._onUpdate()
        }
        ,
        t.prototype.getTickCallback = function() {
            if (!this._tickCallback) {
                var t = this;
                this._tickCallback = function() {
                    t._tick.apply(t, arguments)
                }
            }
            return this._tickCallback
        }
        ,
        t.prototype._clearDelayTimeout = function() {
            this._delayTimeout && (a(this._delayTimeout),
            this._delayTimeout = !1)
        }
        ,
        t.prototype._timeToProgress = function(t) {
            return t / this._duration * this._timeScale
        }
        ,
        t.prototype.delay = function() {
            if (arguments.length > 0) {
                var t = parseFloat(arguments[0]);
                return !isNaN(t) && t != 1 / 0 && t || (t = 0),
                this._delay = Math.max(0, t),
                this
            }
            return this._delay
        }
        ,
        t.prototype.duration = function() {
            if (arguments.length > 0) {
                var t = parseFloat(arguments[0]);
                return !isNaN(t) && t != 1 / 0 && t || (t = 0),
                this._duration = Math.max(0, t),
                this
            }
            return this._duration
        }
        ,
        t.prototype.eventCallback = function(t) {
            return arguments.length > 3 ? this._eventCallbacks[t] = [arguments[1], arguments[2], arguments[3]] : arguments.length > 2 ? this._eventCallbacks[t] = [arguments[1], arguments[2], this] : arguments.length > 1 && (this._eventCallbacks[t] = [arguments[1], [], this]),
            this._eventCallbacks[t]
        }
        ,
        t.prototype.pause = function() {
            return this._isPlaying = !1,
            u.t.removeP(this.getTickCallback()),
            arguments.length > 0 && null != arguments[0] && this.progress(this._timeToProgress(arguments[0])),
            this
        }
        ,
        t.prototype.paused = function() {
            return arguments.length > 0 ? (arguments[0] ? this._isPlaying && this.pause() : this._isPlaying || this.play(),
            this) : !this._isPlaying
        }
        ,
        t.prototype.play = function() {
            var t = !0;
            arguments.length > 0 && null != arguments[0] && (t = !1,
            this._progress = this._timeToProgress(arguments[0])),
            this._play(t)
        }
        ,
        t.prototype._play = function(t) {
            if (this._progress < 1)
                if (0 == this._progress && t && this._delay > 0) {
                    if (!this._delayTimeout) {
                        var i = this;
                        this._delayTimeout = h((function() {
                            i.__play.apply(i, arguments)
                        }
                        ), 1e3 * this._delay)
                    }
                } else
                    this.__play();
            else
                this._isCompleted || (this._isReversed ? this._onReverseComplete() : this._onComplete())
        }
        ,
        t.prototype.__play = function() {
            this._clearDelayTimeout(),
            this._isPlaying || (u.t.addP(this.getTickCallback()),
            this._isPlaying = !0)
        }
        ,
        t.prototype.progress = function() {
            if (arguments.length > 0) {
                var t = parseFloat(arguments[0]);
                return isNaN(t) && (t = 0),
                t = Math.min(1, Math.max(0, t)),
                this._progress = t,
                this._isPlaying || (this._isStarted || this._onStart(),
                this._onUpdate()),
                this
            }
            return this._progress
        }
        ,
        t.prototype.reverse = function() {
            this._isReversed = !0,
            0 != this.progress() && this.play()
        }
        ,
        t.prototype.restart = function() {
            return arguments.length > 0 && arguments[0] ? (this.pause(0),
            this.play(),
            this) : (this.play(0),
            this)
        }
        ,
        t.prototype.seek = function(t) {
            null != t && (this._progress = this._timeToProgress(arguments[0]),
            this._isPlaying || this._onUpdate())
        }
        ,
        t.prototype.startTime = function() {
            if (arguments.length > 0) {
                var t = parseFloat(arguments[0]);
                return isNaN(t) && (t = 0),
                this._startTime = Math.max(0, t),
                this
            }
            return this._startTime
        }
        ,
        t.prototype.timeScale = function() {
            if (arguments.length > 0) {
                var t = parseFloat(arguments[0]);
                return isNaN(t) && (t = 1),
                t = Math.max(.01, t),
                this._timeScale != t && (this._timeScale = t),
                this
            }
            return this._timeScale
        }
        ,
        t.prototype._dispatch = function(t) {
            "object" == typeof this._eventCallbacks[t] && this._eventCallbacks[t][0].apply(this._eventCallbacks[t][2], this._eventCallbacks[t][1])
        }
        ,
        t.prototype.totalDuration = function() {
            if (arguments.length > 0) {
                var t = parseFloat(arguments[0]);
                return isNaN(t) && (t = 0),
                t = Math.max(0, t),
                this.timeScale(this._duration / t),
                this
            }
            return this._duration * this._timeScale
        }
        ,
        t.prototype.reset = function() {
            this._isCompleted = !1,
            this._isStarted = !1,
            this.progress(0)
        }
        ,
        t
    }
    )),
    u.d("Tween", (function() {
        var t = 2
          , i = 3
          , n = function(t) {
            return Symbol.iterator in Object(t)
        };
        function r(s, r) {
            var o, h;
            switch (this.ease = "linear",
            this._tweenContainer = null,
            this._setContainer = null,
            this._roundProps = {},
            arguments.length) {
            case 4:
                o = arguments[2],
                h = arguments[3],
                this._mode = t;
                break;
            default:
                this._mode = i,
                o = {},
                h = arguments[2]
            }
            if (n(s) || (s = [s]),
            this._target = s,
            this.fromParams = o,
            u.Animation.call(this, h),
            this.parseParameters({
                duration: r
            }),
            this._mode === t && this._immediateRender) {
                for (var a in null === this._tweenContainer && this._makeTweenContainer(this.fromParams, this.toParams),
                this._tweenContainer) {
                    var c = this._tweenContainer[a];
                    this._target.forEach((function(t) {
                        t[a] = c.unit ? c.startValue + c.unit : c.startValue
                    }
                    ))
                }
                for (var a in this._setContainer) {
                    c = this._setContainer[a];
                    this._target.forEach((function(t) {
                        t[a] = c.unit ? c.endValue + c.unit : c.endValue
                    }
                    ))
                }
            }
        }
        function o(t) {
            var i = [t, ""];
            t = String(t);
            var n = parseFloat(t);
            return isNaN(n) || (i[0] = n,
            i[1] = t.match(/[\d.\-\+]*\s*(.*)/)[1] || ""),
            i
        }
        function h(t, i, n, r) {
            n === s && (n = t[0][i]),
            r === s && (r = t[0][i]),
            n = o(n);
            var h = 0;
            return "" !== (r = o(r))[1] && n[1] !== r[1] && (n[0] = 0,
            n[1] = r[1]),
            "number" == typeof n[0] && "number" == typeof r[0] && (h = r[0] - n[0]),
            {
                startValue: n[0],
                endValue: r[0],
                unit: r[1],
                range: h
            }
        }
        return r.prototype = Object.create(u.Animation.prototype),
        r.prototype.constructor = r,
        r.prototype.initParameters = function() {
            this.parseParameters(this.fromParams),
            u.Animation.prototype.initParameters.apply(this, arguments)
        }
        ,
        r.prototype.parseParameters = function(t) {
            t && (t.ease && (this.ease = t.ease,
            delete t.ease),
            u.Animation.prototype.parseParameters.apply(this, arguments))
        }
        ,
        r.prototype._onStart = function() {
            for (var t in null === this._tweenContainer && this._makeTweenContainer(this.fromParams, this.toParams),
            this._setContainer) {
                var i = this._setContainer[t];
                this._target.forEach((function(n) {
                    n[t] = i.unit ? i.endValue + i.unit : i.endValue
                }
                ))
            }
            u.Animation.prototype._onStart.call(this)
        }
        ,
        r.prototype._onUpdate = function() {
            for (var t in this._tweenContainer) {
                var i = this._tweenContainer[t]
                  , n = u.Easings[this.ease] ? u.Easings[this.ease](this._progress) : this._progress
                  , s = i.startValue + i.range * n;
                this._roundProps[t] && (s = Math.round((10 * s | 0) / 10)),
                this._target.forEach((function(n) {
                    n[t] = i.unit ? s + i.unit : s
                }
                ))
            }
            u.Animation.prototype._onUpdate.call(this)
        }
        ,
        r.prototype.initRoundProps = function(t) {
            for (var i = t.split(","), n = 0; n < i.length; n++)
                this._roundProps[i[n]] = !0
        }
        ,
        r.prototype._makeTweenContainer = function(t, i) {
            for (var n in t.snap !== s && (this.initRoundProps(t.snap),
            delete t.snap),
            this._setContainer = {},
            this._tweenContainer = {},
            i.snap !== s && (this.initRoundProps(i.snap),
            delete i.snap),
            i) {
                var r = h(this._target, n, t[n], i[n]);
                0 == r.range ? this._setContainer[n] = r : this._tweenContainer[n] = r
            }
        }
        ,
        r.to = function(t, i, n) {
            var o = new r(t,i,n);
            return n.paused !== s && n.paused || o.play(),
            o
        }
        ,
        r.fromTo = function(t, i, n, o) {
            var h = new r(t,i,n,o);
            return o.paused !== s && o.paused || h.play(),
            h
        }
        ,
        u.W = r,
        r
    }
    )),
    u.d("Timeline", (function() {
        function t(t) {
            this.originalParams = f({}, t),
            this._tweens = [],
            u.Animation.call(this, t),
            this._duration = 0
        }
        return t.prototype = Object.create(u.Animation.prototype),
        t.prototype.constructor = t,
        t.prototype._onUpdate = function() {
            if (this.tweensContainer)
                for (var t = 0; t < this.tweensContainer.length; t++) {
                    var i = this.tweensContainer[t]
                      , n = Math.min(1, (this._progress - i.startProgress) / (i.endProgress - i.startProgress));
                    i.tween._isCompleted && n <= i.endProgress && i.tween.reset(),
                    !i.tween._isStarted && n >= 0 && 0 == i.tween.progress() && i.tween._onStart(),
                    i.tween._isStarted && (1 != n || i.tween._isCompleted ? n >= 0 && n < 1 ? i.tween.progress(n) : n < 0 && 0 != i.tween.progress() && i.tween.progress(0) : (i.tween.progress(n),
                    i.tween._onComplete()))
                }
            u.Animation.prototype._onUpdate.call(this)
        }
        ,
        t.prototype.addTween = function(t) {
            t.pause(),
            t.setTimeline(this);
            var i = 0;
            i = arguments.length > 1 ? this._parsePosition(arguments[1]) : this._parsePosition();
            var n = t.delay();
            n > 0 && (i += n,
            t.delay(0)),
            t.startTime(i),
            this._tweens.push(t);
            var s = t.totalDuration() + i;
            s > this._duration && (this._duration = s),
            this.makeCache()
        }
        ,
        t.prototype.clear = function() {
            this.paused() || this.pause(),
            t.call(this, this.originalParams)
        }
        ,
        t.prototype.add = function(t, i) {
            this.addTween(t, i)
        }
        ,
        t.prototype.set = function(t, i, n) {
            this.addTween(u.W.to(t, .05, i), n)
        }
        ,
        t.prototype.to = function(t, i, n, s) {
            n.paused = !0,
            this.addTween(u.W.to(t, i, n), s)
        }
        ,
        t.prototype.fromTo = function(t, i, n, s, r) {
            s.paused = !0,
            this.addTween(u.W.fromTo(t, i, n, s), r)
        }
        ,
        t.prototype._play = function() {
            if (0 == this._progress)
                for (var t = 0; t < this._tweens.length; t++)
                    this._tweens[t].pause(0);
            u.Animation.prototype._play.apply(this, arguments)
        }
        ,
        t.prototype._parsePosition = function() {
            var t = "+=0";
            arguments.length > 0 && arguments[0] !== s && !isNaN(arguments[0]) && (t = arguments[0]);
            var i = 0;
            switch (typeof t) {
            case "string":
                switch (t.substr(0, 2)) {
                case "+=":
                    i = this.duration() + parseFloat(t.substr(2));
                    break;
                case "-=":
                    i = this.duration() - parseFloat(t.substr(2))
                }
                break;
            default:
                i = parseFloat(t)
            }
            return Math.max(0, i)
        }
        ,
        t.prototype.makeCache = function() {
            var t = this.totalDuration();
            this.tweensContainer = [];
            for (var i = 0; i < this._tweens.length; i++) {
                var n = this._tweens[i]
                  , s = n.startTime() / t
                  , r = (n.startTime() + n.totalDuration()) / t;
                this.tweensContainer.push({
                    tween: n,
                    startProgress: s,
                    endProgress: r,
                    range: r - s
                })
            }
        }
        ,
        u.q = t,
        t
    }
    )),
    u.d("Easings", (function() {
        var t = {
            Sine: function(t) {
                return 1 - Math.cos(t * Math.PI / 2)
            },
            Circ: function(t) {
                return 1 - Math.sqrt(1 - t * t)
            },
            Elastic: function(t) {
                return 0 === t || 1 === t ? t : -Math.pow(2, 8 * (t - 1)) * Math.sin((80 * (t - 1) - 7.5) * Math.PI / 15)
            },
            Back: function(t) {
                return t * t * (3 * t - 2)
            },
            Bounce: function(t) {
                for (var i, n = 4; t < ((i = Math.pow(2, --n)) - 1) / 11; )
                    ;
                return 1 / Math.pow(4, 3 - n) - 7.5625 * Math.pow((3 * i - 2) / 22 - t, 2)
            }
        };
        ["Quad", "Cubic", "Quart", "Quint", "Expo"].forEach((function(i, n) {
            t[i] = function(t) {
                return Math.pow(t, n + 2)
            }
        }
        ));
        var i = {};
        for (var n in t)
            !function(t, n) {
                i["easeIn" + t] = n,
                i["easeOut" + t] = function(t) {
                    return 1 - n(1 - t)
                }
                ,
                i["easeInOut" + t] = function(t) {
                    return t < .5 ? n(2 * t) / 2 : 1 - n(-2 * t + 2) / 2
                }
            }(n, t[n]);
        return i
    }
    )),
    u.d("nextend-frontend"),
    u.d("n2")
}(window);

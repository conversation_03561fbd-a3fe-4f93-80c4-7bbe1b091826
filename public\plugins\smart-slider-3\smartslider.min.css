.n2-in-fullscreen * {
    animation-name: initial
}

.n2-in-fullscreen [data-uk-scrollspy*=uk-animation-]:not([data-uk-scrollspy*=target]) {
    opacity: 1
}

ss3-fullpage {
    display: block
}

ss3-fullpage[data-based-on=real] {
    opacity: 0
}

ss3-force-full-width {
    position: relative;
    display: block;
    opacity: 0;
    width: 100vw;
    transform: translateX(-100vw)
}

.n2-section-smartslider {
    position: relative;
    width: 100%;
    outline: 0;
    --widget-offset: 0px
}

.n2-section-smartslider--hidden {
    display: none
}

.n2-ss-align {
    position: relative;
    z-index: 0;
    overflow: hidden
}

.n2-ss-align.n2-ss-align-visible {
    overflow: visible
}

.n2-ss-slider {
    display: grid;
    grid-template-columns: 100%;
    grid-template-rows: 1fr auto;
    -webkit-font-smoothing: antialiased;
    font-size: 16px;
    line-height: 1;
    user-select: none;
    --ss-fs: flex-start;
    --ss-fe: flex-end;
    --ss-r: row;
    --ss-rr: row-reverse
}

.n2-ss-slider [data-force-pointer],.n2-ss-slider [data-force-pointer] * {
    cursor: pointer!important
}

.n2-ss-slider [data-force-pointer=zoom-in],.n2-ss-slider [data-force-pointer=zoom-in] * {
    cursor: zoom-in!important
}

.n2-ss-slider .n2-ss-text {
    user-select: text;
    cursor: initial
}

.n2-ss-slider-has-no-slide {
    height: 0!important;
    opacity: 0!important;
    overflow: hidden!important
}

.n2-ss-slider,.n2-ss-slider .n2_ss__touch_element {
    -webkit-tap-highlight-color: transparent;
    -webkit-tap-highlight-color: transparent
}

.n2-ss-slider>*,.n2-ss-slider .n2_ss__touch_element>* {
    -webkit-tap-highlight-color: initial
}

.n2-ss-slider .n2-ow,.n2-ss-slider .n2-ow-all * {
    font-size: inherit;
    line-height: inherit;
    letter-spacing: inherit
}

.n2-ss-slider .n2-ow,.n2-ss-slider .n2-ow:before,.n2-ss-slider .n2-ow:after,.n2-ss-slider .n2-ow-all *,.n2-ss-slider .n2-ow-all :before,.n2-ss-slider .n2-ow-all :after {
    box-sizing: content-box
}

.n2-ss-slider .n2-ow:before,.n2-ss-slider .n2-ow:after,.n2-ss-slider .n2-ow-all :not(i):before,.n2-ss-slider .n2-ow-all :not(i):after {
    display: none
}

.n2-ss-slider a.n2-ow,.n2-ss-slider .n2-ow-all a {
    border: 0;
    -webkit-hyphens: manual;
    -moz-hyphens: manual;
    -ms-hyphens: manual;
    hyphens: manual
}

.n2-ss-slider a.n2-ow,.n2-ss-slider a.n2-ow:focus,.n2-ss-slider .n2-ow-all a,.n2-ss-slider .n2-ow-all a:focus {
    outline: 0!important;
    transition: none 0s;
    box-shadow: none;
    text-decoration: none
}

.n2-ss-slider path {
    transition: none 0s
}

.n2-ss-slider .n2-ow-all b,.n2-ss-slider .n2-ow-all i {
    color: inherit
}

.n2-ss-slider h1.n2-ow,.n2-ss-slider h2.n2-ow,.n2-ss-slider h3.n2-ow,.n2-ss-slider h4.n2-ow,.n2-ss-slider h5.n2-ow,.n2-ss-slider h6.n2-ow,.n2-ss-slider p.n2-ow,.n2-ss-slider .n2-ow-all h1,.n2-ss-slider .n2-ow-all h2,.n2-ss-slider .n2-ow-all h3,.n2-ss-slider .n2-ow-all h4,.n2-ss-slider .n2-ow-all h5,.n2-ss-slider .n2-ow-all h6,.n2-ss-slider .n2-ow-all p {
    margin: 0;
    padding-left: 0;
    padding-right: 0;
    height: auto;
    width: auto;
    border: 0;
    box-shadow: none;
    -webkit-hyphens: manual;
    -moz-hyphens: manual;
    -ms-hyphens: manual;
    hyphens: manual
}

.n2-ss-slider iframe {
    border: 0;
    min-height: 0
}

.n2-ss-slider iframe.n2-ow,.n2-ss-slider .n2-ow-all iframe {
    margin: 0
}

.n2-ss-slider img.n2-ow,.n2-ss-slider .n2-ow-all img,.n2-ss-slider svg.n2-ow,.n2-ss-slider .n2-ow-all svg {
    max-width: none;
    max-height: none;
    height: auto;
    box-shadow: none;
    border-radius: 0;
    background: 0 0;
    background: 0 0;
    padding: 0;
    margin: 0;
    border: 0;
    vertical-align: top
}

.n2-ss-slider picture.n2-ow,.n2-ss-slider .n2-ow-all picture {
    display: inline-block
}

.n2-ss-slider source {
    display: none
}

.n2-ss-slider ul.n2-ow,.n2-ss-slider li.n2-ow,.n2-ss-slider img.n2-ow,.n2-ss-slider a.n2-ow,.n2-ss-slider p.n2-ow,.n2-ss-slider ol.n2-ow,.n2-ss-slider textarea.n2-ow,.n2-ss-slider input.n2-ow,.n2-ss-slider button.n2-ow {
    transition: none 0s
}

.n2-ss-slider p {
    background-color: transparent
}

.n2-ss-slider ol {
    margin: 0
}

.n2-ss-slider {
    z-index: 3;
    position: relative;
    text-align: left;
    width: 100%;
    min-height: 1px
}

[dir=rtl] .n2-ss-slider {
    text-align: right
}

.n2-ss-slider :focus {
    outline: 0
}

.n2-ss-slider[data-responsive=fullpage] {
    min-height: calc(var(--target-height, 100vh) - var(--subtract, 0px) - var(--subtract-vertical-offset, 0px) - var(--subtract-vertical-widget, 0px))
}

.n2-ss-slider.n2-ss-slider--fullscreen[data-responsive=fullpage] {
    min-height: 100vh
}

.n2-ss-slider.n2-ss-slider--fullscreen .n2-ss-preserve-size--slider {
    display: none!important
}

[dir=rtl] .n2-ss-slider .n2-ss-slide-background {
    text-align: left
}

.n2-ss-slider.n2notransition * {
    transition: none 0s!important
}

.n2-ss-slider .n2-ss-preserve-size {
    width: 100%;
    visibility: hidden
}

.n2-ss-slider .n2-ss-preserve-size[data-related-device] {
    display: none
}

.n2-ss-slider .n2-ss-preserve-size[data-related-device=desktopPortrait] {
    display: block
}

.n2-ss-slider .n2-ss-slider-background-video {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%
}

.n2-ss-slider .n2-cover {
    object-fit: cover
}

.n2-ss-slider .n2-contain {
    object-fit: contain
}

.n2-ss-slider .n2-ss-slide {
    position: relative;
    overflow: hidden;
    text-align: center;
    --ssselfalign: center
}

.n2-ss-full-page--constrain-ratio .n2-ss-slide {
    overflow: visible
}

.n2-ss-slider .n2-ss-slide--focus {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    z-index: -99999
}

.n2-ss-slider .n2-ss-slide-thumbnail {
    display: none!important
}

.n2-ss-full-page--constrain-ratio .n2-ss-layers-container {
    clip-path: var(--ss-clip-path, inset(0px))
}

.n2-ss-slider .n2-ss-slide-background,.n2-ss-slider .n2-ss-slide-background * {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    text-align: left
}

.n2-ss-feature-post-bg-loader .n2-ss-slide-background {
    opacity: 0
}

.n2-ss-slider .n2-ss-slide-background {
    z-index: 10
}

.n2-ss-slider .n2-ss-slide-background .n2-ss-slide-background-color {
    z-index: 1
}

.n2-ss-slider .n2-ss-slide-background .n2-ss-slide-background-color[data-overlay="1"] {
    z-index: 7
}

.n2-ss-slider .n2-ss-slide-background .n2-ss-slide-background-video {
    z-index: 3
}

.n2-ss-slider .n2-ss-slide-background .n2-ss-slide-background-image {
    z-index: 5
}

.n2-ss-slider .n2-ss-slide-background-image {
    width: 100%;
    height: 100%;
    box-sizing: content-box;
    --ss-o-pos-x: 50%;
    --ss-o-pos-y: 50%
}

.n2-ss-slider .n2-ss-slide-background-image img {
    object-position: var(--ss-o-pos-x) var(--ss-o-pos-y)
}

.n2-ss-slider .n2-ss-slide-background-image img {
    width: 100%!important;
    height: 100%!important;
    object-fit: cover;
    color: RGBA(0,0,0,0)
}

.n2-ss-slider [data-mode=fit] .n2-ss-slide-background-image img {
    object-fit: contain
}

.n2-ss-slider [data-mode=center] .n2-ss-slide-background-image img {
    object-fit: none
}

.n2-ss-slider [data-mode=stretch] .n2-ss-slide-background-image img {
    object-fit: fill
}

.n2-ss-slider [data-mode=blurfit] .n2-ss-slide-background-image+.n2-ss-slide-background-image img {
    object-fit: contain
}

.n2-ss-slider .n2-ss-slide-background-video {
    object-fit: cover
}

.n2-ss-slider .n2-ss-slide-background-video[data-mode=fit] {
    object-fit: contain
}

.n2-ss-slider .n2-ss-layers-container {
    visibility: hidden;
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    z-index: 20;
    opacity: .001;
    transition: opacity .4s
}

.n2-ss-slider.n2-ss-loaded .n2-ss-layers-container {
    opacity: 1
}

.n2-ss-slider [onclick].n2-ss-layers-container {
    visibility: visible
}

.n2-ss-slider .n2-ss-layers-container>* {
    visibility: visible
}

.n2-ss-slider .n2-ss-layer {
    z-index: 2;
    --margin-top: 0px;
    --margin-right: 0px;
    --margin-bottom: 0px;
    --margin-left: 0px;
    width: calc(100% - var(--margin-right) - var(--margin-left) + var(--ss-safari-fix-225962, 0px));
    --ssfont-scale: 1;
    font-size: calc(100%*var(--ssfont-scale))
}

.n2-ss-slider .n2-ss-layer[data-pm=normal] {
    margin: var(--margin-top) var(--margin-right) var(--margin-bottom) var(--margin-left)
}

.n2-ss-slider .n2-ss-layer[data-pm=absolute] {
    position: absolute;
    left: 0;
    top: 0
}

.n2-ss-slider .n2-ss-layer[data-pm=absolute] picture {
    width: 100%
}

.n2-ss-slider .n2-ss-layer[data-pm=default],.n2-ss-slider .n2-ss-layer[data-pm=normal] {
    position: relative;
    min-height: 1px
}

.n2-ss-slider .n2-ss-layer-wrapper {
    width: 100%;
    height: 100%
}

.n2-ss-slider .n2-ss-layer-content .n2-ss-layer-wrapper,.n2-ss-slider .n2-ss-layer-row .n2-ss-layer-wrapper {
    height: auto;
    flex-grow: 1
}

.n2-ss-slider .n2-ss-layer.n2-ss-layer--need-height .n2-ss-layer-wrapper {
    height: 100%
}

.n2-ss-slider .n2-ss-layer[data-sstype=col]>.n2-ss-layer-wrapper,.n2-ss-slider .n2-ss-layer[data-sstype=content]>.n2-ss-layer-wrapper {
    flex: 1 1 auto;
    min-height: 100%;
    height: auto!important;
    display: flex;
    flex-direction: column
}

.n2-ss-no-bga-fixed *,.n2-ss-slider.n2-ss-mobileLandscape .n2-ss-slider-1,.n2-ss-slider.n2-ss-mobilePortrait .n2-ss-slider-1,.n2-ss-slider.n2-ss-tabletLandscape .n2-ss-slider-1,.n2-ss-slider.n2-ss-tabletPortrait .n2-ss-slider-1,.n2-ss-slider.n2-ss-mobileLandscape .n2-ss-slider-2,.n2-ss-slider.n2-ss-mobilePortrait .n2-ss-slider-2,.n2-ss-slider.n2-ss-tabletLandscape .n2-ss-slider-2,.n2-ss-slider.n2-ss-tabletPortrait .n2-ss-slider-2 {
    background-attachment: scroll!important
}

.n2-ss-slider .n2-ss-widget {
    position: relative;
    -webkit-backface-visibility: hidden;
    -webkit-perspective: 1000px;
    z-index: 1;
    font-size: 16px;
    transition: opacity .4s ease;
    opacity: 1;
    box-sizing: initial
}

.n2-ss-slider .n2-ss-widget[data-position=above],.n2-ss-slider .n2-ss-widget[data-position=below] {
    margin-left: auto;
    margin-right: auto
}

.n2-ss-slider .n2-ss-widget.n2-ss-widget-hidden {
    opacity: 0!important;
    pointer-events: none
}

.n2-ss-slider .n2-ss-slider-controls-above,.n2-ss-slider .n2-ss-slider-controls-below {
    display: flex;
    flex-flow: column;
    align-items: center
}

.n2-ss-slider .n2-ss-slider-controls-side {
    position: relative;
    display: flex
}

.n2-ss-slider .n2-ss-slider-controls-side>* {
    flex: 1 1 auto
}

.n2-ss-slider .n2-ss-slider-controls-left,.n2-ss-slider .n2-ss-slider-controls-right {
    flex: 0 0 auto;
    display: flex
}

.n2-ss-slider .n2-ss-slider-controls-left>*,.n2-ss-slider .n2-ss-slider-controls-right>* {
    flex: 1 1 auto
}

.n2-ss-slider .n2-ss-slider-controls-left>* {
    margin-right: var(--widget-offset)
}

.n2-ss-slider .n2-ss-slider-controls-right>* {
    margin-left: var(--widget-offset)
}

.n2-ss-slider .n2-ss-slider-controls-absolute-left,.n2-ss-slider .n2-ss-slider-controls-absolute-right {
    position: absolute;
    right: 100%;
    top: 0;
    height: 100%;
    display: flex;
    flex-flow: var(--ss-rr);
    align-items: center;
    visibility: hidden
}

[dir=rtl] .n2-ss-slider .n2-ss-slider-controls-absolute-left {
    justify-content: flex-start
}

[dir=rtl] .n2-ss-slider .n2-ss-slider-controls-absolute-right {
    justify-content: flex-end
}

.n2-ss-slider .n2-ss-slider-controls-absolute-left>*,.n2-ss-slider .n2-ss-slider-controls-absolute-right>* {
    visibility: visible
}

.n2-ss-slider .n2-ss-slider-controls-absolute-right {
    left: 100%;
    right: auto;
    flex-flow: row
}

.n2-ss-slider .n2-ss-slider-wrapper-outside,.n2-ss-slider .n2-ss-slider-wrapper-inside {
    position: relative;
    display: grid;
    grid-template-columns: 100%
}

.n2-ss-slider .n2-ss-slider-wrapper-inside .n2-ss-slider-controls {
    z-index: 10;
    position: absolute;
    width: 100%;
    height: 100%;
    visibility: hidden;
    display: flex
}

.n2-ss-slider .n2-ss-slider-wrapper-inside .n2-ss-slider-controls>* {
    visibility: visible
}

.n2-ss-slider .n2-ss-slider-wrapper-inside .n2-ss-slider-controls-advanced {
    display: block;
    opacity: 0;
    transition: opacity .4s
}

.n2-ss-slider .n2-ss-slider-wrapper-inside .n2-ss-slider-controls-advanced--ready {
    opacity: 1
}

.n2-ss-slider .n2-ss-slider-wrapper-inside .n2-ss-slider-controls-advanced>* {
    position: absolute!important
}

.n2-ss-slider .n2-ss-slider-controls-absolute-left-top {
    align-items: flex-start;
    flex-flow: var(--ss-r)
}

.n2-ss-slider .n2-ss-slider-controls-absolute-left-top>* {
    margin: var(--widget-offset) 0 0 var(--widget-offset)
}

.n2-ss-slider .n2-ss-slider-controls-absolute-center-top {
    align-items: center;
    flex-flow: column
}

.n2-ss-slider .n2-ss-slider-controls-absolute-center-top>*,.n2-ss-slider .n2-ss-slider-controls-below>* {
    margin-top: var(--widget-offset)
}

.n2-ss-slider .n2-ss-slider-controls-absolute-right-top {
    flex-flow: var(--ss-rr);
    align-items: flex-start
}

.n2-ss-slider .n2-ss-slider-controls-absolute-right-top>* {
    margin: var(--widget-offset) var(--widget-offset) 0 0
}

.n2-ss-slider .n2-ss-slider-controls-absolute-left-center {
    align-items: center;
    flex-flow: var(--ss-r)
}

.n2-ss-slider .n2-ss-slider-controls-absolute-left-center>*,.n2-ss-slider .n2-ss-slider-controls-absolute-right>* {
    margin-left: var(--widget-offset)
}

.n2-ss-slider .n2-ss-slider-controls-absolute-right-center {
    align-items: center;
    flex-flow: var(--ss-rr)
}

.n2-ss-slider .n2-ss-slider-controls-absolute-right-center>*,.n2-ss-slider .n2-ss-slider-controls-absolute-left>* {
    margin-right: var(--widget-offset)
}

.n2-ss-slider .n2-ss-slider-controls-absolute-left-bottom {
    align-items: flex-end;
    flex-flow: var(--ss-r)
}

.n2-ss-slider .n2-ss-slider-controls-absolute-left-bottom>* {
    margin: 0 0 var(--widget-offset) var(--widget-offset)
}

.n2-ss-slider .n2-ss-slider-controls-absolute-center-bottom {
    align-items: center;
    flex-flow: column-reverse
}

.n2-ss-slider .n2-ss-slider-controls-absolute-center-bottom>*,.n2-ss-slider .n2-ss-slider-controls-above>* {
    margin-bottom: var(--widget-offset)
}

.n2-ss-slider .n2-ss-slider-controls-absolute-right-bottom {
    align-items: flex-end;
    flex-flow: var(--ss-rr)
}

.n2-ss-slider .n2-ss-slider-controls-absolute-right-bottom>* {
    margin: 0 var(--widget-offset) var(--widget-offset) 0
}

.n2-ss-slider .n2-ss-slider-wrapper-inside .n2-ss-slider-controls-absolute-right-top,.n2-ss-slider .n2-ss-slider-wrapper-inside .n2-ss-slider-controls-absolute-left-top,.n2-ss-slider .n2-ss-slider-wrapper-inside .n2-ss-slider-controls-absolute-left-bottom,.n2-ss-slider .n2-ss-slider-wrapper-inside .n2-ss-slider-controls-absolute-right-bottom {
    z-index: 11
}

.n2-ss-slider .n2-ss-static-slide {
    justify-self: stretch;
    align-self: stretch;
    display: flex;
    flex-direction: column;
    position: relative;
    z-index: 25;
    -webkit-backface-visibility: hidden;
    text-align: center;
    --ssselfalign: center;
    opacity: .001;
    transition: opacity .4s
}

.n2-ss-slider.n2-ss-loaded .n2-ss-static-slide {
    opacity: 1
}

.n2-ss-slider .n2-ss-static-slide div[data-sstype=slide]>.n2-ss-layer,.n2-ss-slider .n2-ss-static-slide div[data-sstype=content][data-hasbackground="1"]>div.n2-ss-section-main-content,.n2-ss-slider .n2-ss-static-slide div[data-sstype=content] .n2-ss-section-main-content>* {
    visibility: visible
}

.n2-ss-slider .n2-ss-static-slide,.n2-ss-slider .n2-ss-static-slide div[data-sstype=slide],.n2-ss-slider .n2-ss-static-slide div[data-sstype=content][data-hasbackground="0"],.n2-ss-slider .n2-ss-static-slide div[data-sstype=content][data-hasbackground="0"] div.n2-ss-section-main-content {
    visibility: hidden
}

.n2-ss-slider .n2-ss-shape-divider {
    position: absolute;
    left: 0;
    width: 100%;
    height: 100px;
    pointer-events: none;
    z-index: 13
}

.n2-ss-slider .n2-ss-shape-divider-inner {
    height: 100%;
    transform-origin: center top
}

.n2-ss-slider .n2-ss-shape-divider-bottom .n2-ss-shape-divider-inner {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    transform-origin: center bottom
}

.n2-ss-slider .n2-ss-shape-divider svg {
    display: block;
    height: 100%;
    width: 100%
}

.n2-ss-slider .n2-ss-shape-divider-top {
    top: 0
}

.n2-ss-slider .n2-ss-shape-divider-bottom {
    bottom: 0
}

.n2-ss-slider .n2-ss-slide .nextend-slide-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    max-width: none!important;
    height: auto;
    background: rgba(0,0,0,.002)
}

.n2-ss-slider video.n2-ow {
    max-width: none
}

.n2-ss-slider .n2-ss-item-video-container {
    overflow: hidden;
    width: 100%;
    height: 100%
}

.n2-ss-slider .n2-ss-item-video-container video {
    width: 100%;
    height: 100%
}

.n2-ss-slider .n2-ss-item-iframe-wrapper {
    -webkit-overflow-scrolling: touch;
    overflow-y: auto;
    height: 100%
}

.n2-ss-slider .n2i {
    vertical-align: top
}

.n2-ss-slider iframe.n2-ow {
    max-width: none;
    display: block
}

.n2-ss-slider .n2-ss-item-iframe {
    height: 100%
}

.n2-ss-slider .n2-grabbing {
    cursor: grabbing!important
}

.n2-ss-slider .n2-grabbing .n2-ss-slide {
    pointer-events: none
}

.n2-ss-slider .n2-ss-item-input-form {
    display: flex;
    flex-flow: row
}

.n2-ss-slider .n2-ss-item-input-form .n2-input {
    flex: 1 1 auto
}

.n2-ss-slider .n2-ss-item-input-form .n2-form-button {
    margin: 0
}

.n2-ss-slider form.n2-ow {
    margin: 0;
    border: 0;
    padding: 0
}

.n2-ss-slider .n2-input {
    height: auto;
    width: auto;
    box-sizing: border-box;
    margin: 0;
    border: 0;
    padding: 0;
    display: inline-block;
    vertical-align: middle;
    white-space: normal;
    background: 0 0;
    line-height: 1;
    font-size: 13px;
    font-family: Arial,serif;
    box-shadow: none
}

.n2-ss-slider input.n2-ow:focus {
    outline: 0
}

.n2-ss-slider input.n2-ow[placeholder] {
    overflow: hidden;
    text-overflow: clip
}

.n2-ss-slider ::-moz-placeholder {
    text-overflow: clip;
    color: inherit
}

.n2-ss-slider input.n2-ow:-moz-placeholder {
    text-overflow: clip;
    color: inherit
}

.n2-ss-slider input.n2-ow::-webkit-input-placeholder,.n2-ss-slider input.n2-ow::placeholder {
    color: inherit
}

.n2-ss-slider table.n2-ow {
    table-layout: auto;
    margin: 0
}

.n2-ss-slider .n2-ow .n2-ss-thumbnail-type {
    width: 48px;
    height: 48px;
    margin-left: -24px;
    margin-top: -24px;
    position: absolute;
    left: 50%;
    top: 50%
}

.n2-ss-slider .n2-ss-section-main-content .n2-ss-item {
    float: none
}

.n2-ss-slider .n2-ss-layer[data-sstype=row].n2-ss-stretch-layer {
    display: flex;
    flex: 1 1 auto
}

.n2-ss-slider .n2-ss-layer[data-sstype=row].n2-ss-stretch-layer>.n2-ss-layer-wrapper {
    display: flex
}

.n2-ss-slider .n2-ss-layer-row {
    box-sizing: border-box;
    display: flex
}

.n2-ss-slider .n2-ss-layer-row-inner {
    position: relative;
    box-sizing: border-box;
    display: flex;
    visibility: hidden;
    width: 100%
}

.n2-ss-slider .n2-ss-layer-row-inner>* {
    visibility: visible
}

.n2-ss-slider .n2-ss-layer[data-sstype=row].n2-ss-stretch-layer .n2-ss-layer-row {
    height: auto;
    width: 100%
}

.n2-ss-slider .n2-ss-layer-row>.n2-ss-layer {
    box-sizing: border-box;
    width: 1px;
    flex: 1 1 auto
}

.n2-ss-slider .n2-ss-layer-row .n2-ss-item {
    float: none
}

.n2-ss-slider .n2-ss-layer[data-sstype=slide] {
    flex: 0 0 auto;
    position: relative;
    box-sizing: border-box;
    width: 100%;
    height: 100%;
    min-height: 100%;
    z-index: 2;
    display: flex;
    justify-content: center;
    flex-flow: column
}

.n2-ss-slider .n2-ss-layer-content .n2-ss-layer--auto:not([data-pm=absolute]) {
    width: auto!important
}

.n2-ss-slider .n2-ss-layer-content .n2-ss-layer[data-pm=normal] {
    max-width: calc(100% - var(--margin-right) - var(--margin-left) + var(--ss-safari-fix-225962, 0px))
}

.n2-ss-slider .n2-ss-layer[data-sstype=content] {
    flex: 0 0 auto;
    width: 100%;
    min-height: 100%;
    position: relative;
    z-index: 2;
    display: flex
}

.n2-ss-slider .n2-ss-layer-content {
    flex: 0 0 auto;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    box-sizing: border-box;
    position: relative;
    min-height: 10px
}

.n2-ss-slider .n2-ss-section-main-content {
    flex: 1 1 auto;
    display: flex;
    justify-content: center;
    flex-direction: column;
    min-height: 100%;
    max-width: 100%
}

.n2-ss-slider .n2-ss-layer-with-background {
    --n2bgimage: none;
    --n2bggradient: none;
    background-image: var(--n2bggradient),var(--n2bgimage);
    background-size: cover,cover;
    background-repeat: no-repeat,no-repeat;
    transition: all .3s;
    transition-property: border-color,background-color,border-radius,box-shadow
}

.n2-ss-slider .n2-ss-layer[data-sstype=col] {
    display: flex;
    flex-flow: column;
    order: 10;
    flex: 0 1 auto
}

.n2-ss-slider .n2-ss-layer--block>.n2-ss-layer-row>.n2-ss-layer-row-inner>.n2-ss-layer[data-sstype=col],.n2-ss-slider .n2-ss-layer--block>.n2-ss-layer-wrapper>.n2-ss-layer-row>.n2-ss-layer-row-inner>.n2-ss-layer[data-sstype=col] {
    flex: 1 1 auto
}

.n2-ss-slider .n2-ss-layer-col {
    box-sizing: border-box;
    position: relative;
    width: 100%;
    height: auto;
    flex: 1 1 auto
}

.n2-ss-slider .n2-ss-layer-col>.n2-ss-layer {
    flex: 0 0 auto
}

.n2-ss-slider .n2-ss-layer:not([data-sstype=col]) {
    align-self: var(--ssselfalign)
}

.n2-ss-slider .n2-ss-item-image-content {
    overflow: hidden;
    line-height: 0
}

.n2-ss-slider .n2-ss-img-wrapper,.n2-ss-slider .n2-ss-img-wrapper img {
    line-height: 0
}

.n2-ss-slider .n2-ss-item-image-content img {
    display: inline-block;
    max-width: 100%
}

.n2-ss-slider .n2-ss-item-image-content a {
    display: inline-block
}

.n2-ss-slider .n2-ss-item-image-content picture {
    width: 100%
}

.n2-ss-slider img.n2-ss-item-image-area {
    display: block;
    width: 100%!important;
    height: 100%!important
}

.n2-ss-slider .n2_ss_video_player {
    position: relative
}

.n2-ss-slider .n2_ss_video_player .n2_ss_video_player__placeholder {
    padding-top: 56.25%
}

.n2-ss-slider .n2_ss_video_player[data-aspect-ratio="16:10"] .n2_ss_video_player__placeholder {
    padding-top: 62.5%
}

.n2-ss-slider .n2_ss_video_player[data-aspect-ratio="4:3"] .n2_ss_video_player__placeholder {
    padding-top: 75%
}

.n2-ss-slider .n2_ss_video_player iframe,.n2-ss-slider .n2_ss_video_player video,.n2-ss-slider .n2_ss_video_player .n2_ss_video_player__cover {
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%
}

.n2-ss-slider .n2_ss_video_player .n2_ss_video_player__cover {
    z-index: 2;
    transform: translate3d(0,0,0)
}

.n2-ss-slider .n2_ss_video_player[data-aspect-ratio=fill] {
    min-height: 50px;
    height: 100%
}

.n2-ss-slider .n2_ss_video_player[data-aspect-ratio=fill] .n2_ss_video_player__placeholder,.n2-ss-slider .n2_ss_video_player[data-aspect-ratio=fill]>.n2_ss_video_player__placeholder,.n2-ss-slider .n2_ss_video_player[data-aspect-ratio=fill]>.n2_ss_video_player__placeholder {
    padding-top: 0
}

.n2-ss-slider .n2_ss_video_player[data-aspect-ratio=fill] video {
    position: static;
    height: auto
}

.n2-ss-slider .n2-ss-layer[data-pm=absolute]>.n2-ss-item>.n2_ss_video_player[data-aspect-ratio=fill]>video,.n2-ss-slider .n2-ss-layer[data-pm=absolute]>.n2_ss_video_player[data-aspect-ratio=fill]>video,.n2-ss-slider .n2_ss_video_player[data-aspect-ratio=fill]>video,.n2-ss-slider .n2_ss_video_player[data-aspect-ratio=fill]>video {
    position: static;
    height: 100%
}

.n2-ss-slider img.n2_ss_video_cover {
    display: block;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    object-fit: cover
}

.n2-ss-slider img.n2_ss_video_play_btn {
    position: absolute;
    left: 50%;
    top: 50%;
    z-index: 2;
    width: 48px;
    height: 48px;
    transform: translate(-50%,-50%)
}

.n2-ss-slider .n2-ss-animated-heading-wrapper,.n2-ss-slider .n2-ss-animated-heading-wrapper *,.n2-ss-slider .n2-ss-highlighted-heading-wrapper,.n2-ss-slider .n2-ss-highlighted-heading-wrapper * {
    text-decoration: none!important
}

.n2_clear {
    clear: both
}

[data-force-hidden],[data-force-hidden] * {
    visibility: hidden!important
}

.n2-ss-slider .n2-ss-reveal-clip {
    pointer-events: none;
    visibility: hidden;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 1000000;
    width: 100%;
    height: 100%;
    overflow: hidden
}

.n2-ss-slider .n2-ss-reveal-clip>* {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%
}

.n2-ss-slider .n2-widget-html {
    z-index: 10
}

.n2-ss-slider .n-particles-js-canvas-el {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 12
}

ss-text,ss-word,ss-char {
    position: relative;
    display: inline-block
}

ss-p {
    display: block
}

ss3-loader {
    display: none;
    place-content: center;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 100000
}

ss3-loader:after {
    content: '';
    display: block;
    border: 9px solid RGBA(0,0,0,.6);
    border-top: 9px solid #fff;
    border-radius: 50%;
    box-shadow: inset 0 0 0 1px RGBA(0,0,0,.6),0 0 0 1px RGBA(0,0,0,.6);
    width: 40px;
    height: 40px;
    animation: n2-ss-loader-spin 2s linear infinite
}

@keyframes n2-ss-loader-spin {
    0% {
        transform: rotate(0deg)
    }

    to {
        transform: rotate(360deg)
    }
}


<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css"
      integrity="sha512-SfTiTlX6kk+qitfevl/7LibUOeJWlt9rbyDn92a1DqWOw9vWG2MFoays0sgObmWazO5BQPiFucnnEAjpAB+/Sw=="
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    />
    <style>
      body{
          background: #fff!important;
      }
         html body{font-family: "Poppins", sans-serif!important;}
         .dropdown:hover,
         .navigation li:hover{
             color:#f96126;
         }
         .navigation li{padding: 6px 0;}
         .dropdown:hover .dropdown-menu{
             display: block!important;
         }
         .sub-dropdown-menu {
             display: none;
             position: absolute;
             right: 0;
             top: 0!important;
             background: rgba(0, 0, 0, 0.7);
             width: 100%;
             padding: 6px 0;
             list-style: none;
             margin: 0;
             border-radius: 5px;
         }
         .sub-dropdown:hover .sub-dropdown-menu{
             display: block;
         }
         .sub-dropdown-menu li {
             padding: 10px 0;
         }
         .sub-dropdown-menu li a {
             color: #ffffff;
             text-decoration: none;
             padding: 0px 20px;
         }
         .sub-dropdown a {
             display: flex!important;
             justify-content: space-between;
             align-items: center;
         }
          .wpb_wrapper aside.widget_nav_menu ul li a {
          color: white;
          border-bottom: 1px solid #666666 !important;
          }
          .wpb_wrapper aside.widget_nav_menu ul{
          border-bottom: 1px solid #666666 !important;
          }
          .post-navigation {
          display:none;
          }
          .toplogin {
          margin-bottom: 8px;
          margin-top: 8px;
          }
          .navbar {
          margin-bottom: 0;
          border-radius: 0;
          }
         
          footer {
          background-color: #fff;
          }
          .carousel-inner img {
          width: 100%;
          margin: auto;
          }
          .wpb_wrapper aside.widget_nav_menu ul li {
          padding: 4px 5px;
          list-style-type: disc;
          color: #fff;
          }
          .wpb_wrapper aside.widget_nav_menu ul {
          list-style-type: disc !important;
          padding-left: 31px !important;
          padding-top: 16px !important;
          padding-bottom: 5px !important;
          }
     
          @media (max-width: 600px) {
          .carousel-caption {
          display: none;
          }
          }
          @media (max-width: 768px){
          form.navbar-form.navbar-left{
          float:left;
          }
          .toplogin{float:right;}
          .logo_responsive{
          max-width:72px !important;
          margin-top: 8px;
          }
          .logo_name {
          max-width:76%;
          float:left;
          }
          .logo_name span {
          font-size: 10px !important;
          }
          .m_p0{padding:0;}
          .university{margin-bottom:0 !important;}
          .nav_logo {
          padding: 5px 0px;
          }
          }
          .wpb_wrapper aside.widget_nav_menu ul li ul,  .wpb_wrapper aside.widget_nav_menu ul li ul li:last-child{
          padding-bottom:0 !important;
          }
          .my-nav{
          background:#eee;
          }
          #mega-menu-wrap-primary #mega-menu-primary > li.mega-menu-item > a.mega-menu-link {
          padding-left:2px!important;
          }
          .my-nav .myn{
          padding:0 !important;
          margin:0 !important;
          }
          .my-nav li{
          list-style:none !important;
          border-bottom:1px solid #ddd !important;
          }
          .my-nav li:last-child{
          border-bottom:none;
          }
          .my-nav li a{
          font-size:14px !important;
          color:#000 !important;
          position:relative;
          z-index:99999 !important;
          }
          .my-nav .dropdown-menu li{
          background:#800000;
          }
          .my-nav .dropdown-menu a{
          color:#fff !important;
          }
          .navbar-toggle{
          background:#800000 !important;
          }
          .accordion_head {
          background-color: #800000;
          color: white;
          cursor: pointer;
          font-family: arial;
          font-size: 14px;
          margin: 0 0 1px 0;
          padding: 7px 11px;
          font-weight: bold;
          }
          .accordion_body {
          background: #eee;
          }
          .accordion_body p {
          padding: 18px 5px;
          margin: 0px;
          }
          .plusminus {
          float: right;
          }
          .myna{
          margin:0;
          padding:0;
          }
          .myna li{
          padding:7px 10px;
          }
          .bsac_right img {width:82%;}
          .bsac_rights img {width:30%;}
          @media only screen and (max-width: 600px) and (min-width: 320px) {  
            
          }
          @media only screen and (min-width: 393px)  {
          }
          span.hindiLogo {
     display: block;
     margin-bottom: 0;
     font-size: 24px;
     color: #ad0303;
     font-weight: 600;
 }
 
 li.sub-dropdown {
     position: relative;
 }
 
 .dropdown-menu li a{
     color:black!important;
 }
 
 
 .dropdown-menu li a:hover{
     color:#f96126!important;
 }
 
 
       </style>
     <style type="text/css">
          img.wp-smiley,
          img.emoji {
          display: inline !important;
          border: none !important;
          box-shadow: none !important;
          height: 1em !important;
          width: 1em !important;
          margin: 0 .07em !important;
          vertical-align: -0.1em !important;
          background: none !important;
          padding: 0 !important;
          }
       </style>
         <style type="text/css" id="wp-custom-css">
          .mega-study{
             Font-size : 14px;
             Color : #fff;
          Background : #ad0303 !important
          }
          .mega-oldwebsite {Font-size : 14px;
             Color : #fff;
             Background : #ad0303 !important}
          .navigation_nav{ z-index : 11;}
          h4.blink {
              color: #d40c0c;
              font-size: 22px;
          }
          .container-fluid>.navbar-collapse, .container-fluid>.navbar-header, .container>.navbar-collapse, .container>.navbar-header{margin-right:0;}
         @media only screen and (max-width: 600px) {
             .navbar-default .navbar-nav>li>a {
                 padding: 10px 14px!important;
                 display: flex;
                 justify-content: space-between;
                 align-items: center;
             }
             .dropdown .dropdown-menu {
                 width: 100%;
             }
             .navbar-nav {
                 padding: 7.5px 0 7.5px 15px;
                 margin: 0;
             }
             .navbar-form.navbar-left {
                 width: 73%;
             }
             .search {
                 width: 100%;
                 display: flex;
             }
             .search input {
                 width: 80%!important;
             }
             .navigation li {
                 padding: 0!important;
                 background: #0cd416;
                 border-bottom: none!important;
             }
             .navigation li a{
                 padding: 10px 10px;
                 background: #0cd448;
                 border-bottom: 1px solid #3545d9;
             }
             .dropdown-menu {
                 position: relative;
                 border: none!important;
                 margin-bottom: 10px!important;
             }
             .dropdown .dropdown-menu li a{
                 display: flex!important;
                 justify-content: space-between;
                 align-items: center;
                 width: 100%;
                 color: #000;
                 background: #fff!important;
             }
             .sub-dropdown-menu{
                 z-index: 999;
                 background: #fff;
                 width: 100%;
                 left: 0!important;
             }           
        
}
.logo-section {
    padding: 10px 0;
    background-color: #f8f9fa; /* Adjust as needed */
}

.logo-area {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.logo img, .logo-right img {
    max-width: 100%;
    height: auto;
}

.logo-text {
    text-align: center;
}

.logo-text h3 {
    font-size: 1.5rem;
    margin: 0;
}

.logo-text h2 {
    font-size: 1.2rem;
    margin: 0;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .logo-area {
        flex-direction: column;
        align-items: center;
    }

    .logo, .logo-right {
        text-align: center;
        margin-bottom: 10px;
    }

    .logo-text h3 {
        font-size: 1.2rem;
    }

    .logo-text h2 {
        font-size: 1rem;
    }

    .logo-right {
        order: -1; 
    }
}

@media (max-width: 576px) {
    .logo-text h3 {
        font-size: 1rem;
    }

    .logo-text h2 {
        font-size: 0.9rem;
    }
}
.top-header {
    background-color: #f8f9fa; 
    padding: 10px 0;
}

.top-menus ul.top-menu-list {
    display: flex;
    justify-content: space-between;
    align-items: center;
    list-style: none;
    padding: 0;
    margin: 0;
}

.top-menu-item {
    margin: 0 10px;
}

.top-menu-item a {
    color: #333; 
    text-decoration: none;
    font-size: 14px;
}

.social-icons-list {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0;
}

.social-icons-list li {
    margin: 0 5px;
}

.social-button {
    color: #fff; 
    background-color: #007bff; 
    padding: 5px 10px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    text-decoration: none;
}

.social-button:hover {
    background-color: #0056b3; 
}

/* Responsive Styles */
@media (max-width: 768px) {
    .top-menus ul.top-menu-list {
        flex-direction: column;
        align-items: flex-start;
    }

    .top-menu-item {
        margin: 5px 0;
    }

    .social-icons {
        margin-top: 10px;
    }

    .social-icons-list {
        justify-content: flex-start;
    }
}

@media (max-width: 576px) {
    .top-menu-item a {
        font-size: 12px; 
    }

    .social-button {
        width: 25px;
        height: 25px;
        padding: 4px 8px;
    }
}

        

       </style>
     
    <link href="/css/bootstrap.min.css" rel="stylesheet" />
    <link href="/css/owl.carousel.min.css" rel="stylesheet" />
    <link href="/css/owl.theme.default.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="/css/style.css" />
    
      <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">

  
    <title>Welcome To ANSISS</title>
    <link rel="icon" type="image/png" href="/img/LOGO1.png" />
    <script>(function () { this._N2 = this._N2 || { _r: [], _d: [], r: function () { this._r.push(arguments) }, d: function () { this._d.push(arguments) } } }).call(window); !function (a) { a.indexOf("Safari") > 0 && -1 === a.indexOf("Chrome") && document.documentElement.style.setProperty("--ss-safari-fix-225962", "1px") }(navigator.userAgent);</script>
    <script
       src="/plugins/smart-slider-3/n2.min.js"
      defer async></script>
<script src="/plugins/smart-slider-3/smartslider-frontend.min.js" defer async></script>
<script src="/plugins/smart-SliderType/ss-simple.min.js" defer async></script>
<script src="/plugins/smart-Widget/w-arrow-image.min.js" defer async></script>
<script>_N2.r('documentReady',function(){_N2.r(["documentReady","smartslider-frontend","SmartSliderWidgetArrowImage","ss-simple"],function(){new _N2.SmartSliderSimple('n2-ss-15',{"admin":false,"background.video.mobile":1,"loadingTime":2000,"alias":{"id":1,"smoothScroll":1,"slideSwitch":1,"scroll":1},"align":"normal","isDelayed":0,"responsive":{"mediaQueries":{"all":false,"desktopportrait":["(min-width: 1200px)"],"tabletportrait":["(orientation: landscape) and (max-width: 1199px) and (min-width: 901px)","(orientation: portrait) and (max-width: 1199px) and (min-width: 701px)"],"mobileportrait":["(orientation: landscape) and (max-width: 900px)","(orientation: portrait) and (max-width: 700px)"]},"base":{"slideOuterWidth":1360,"slideOuterHeight":700,"sliderWidth":1360,"sliderHeight":700,"slideWidth":1360,"slideHeight":700},"hideOn":{"desktopLandscape":false,"desktopPortrait":false,"tabletLandscape":false,"tabletPortrait":false,"mobileLandscape":false,"mobilePortrait":false},"onResizeEnabled":true,"type":"fullwidth","sliderHeightBasedOn":"real","focusUser":0,"focusEdge":"bottom-force","breakpoints":[{"device":"tabletPortrait","type":"max-screen-width","portraitWidth":1199,"landscapeWidth":1199},{"device":"mobilePortrait","type":"max-screen-width","portraitWidth":700,"landscapeWidth":900}],"enabledDevices":{"desktopLandscape":0,"desktopPortrait":1,"tabletLandscape":0,"tabletPortrait":1,"mobileLandscape":0,"mobilePortrait":1},"sizes":{"desktopPortrait":{"width":1360,"height":700,"max":3000,"min":1200},"tabletPortrait":{"width":701,"height":360,"customHeight":false,"max":1199,"min":701},"mobilePortrait":{"width":320,"height":164,"customHeight":false,"max":900,"min":320}},"overflowHiddenPage":0,"focus":{"offsetTop":"#wpadminbar","offsetBottom":""}},"controls":{"mousewheel":0,"touch":"horizontal","keyboard":0,"blockCarouselInteraction":0},"playWhenVisible":1,"playWhenVisibleAt":0.5,"lazyLoad":0,"lazyLoadNeighbor":0,"blockrightclick":0,"maintainSession":0,"autoplay":{"enabled":1,"start":1,"duration":2000,"autoplayLoop":1,"allowReStart":0,"pause":{"click":1,"mouse":"enter","mediaStarted":1},"resume":{"click":0,"mouse":"enter","mediaEnded":1,"slidechanged":0},"interval":1,"intervalModifier":"loop","intervalSlide":"current"},"perspective":1500,"layerMode":{"playOnce":0,"playFirstLayer":1,"mode":"skippable","inAnimation":"mainInEnd"},"bgAnimations":0,"mainanimation":{"type":"horizontal","duration":800,"delay":0,"ease":"easeOutQuad","shiftedBackgroundAnimation":0},"carousel":1,"initCallbacks":function(){new _N2.SmartSliderWidgetArrowImage(this)}})})});</script></head>
  
  <body>
    <section class="top-header">
      <div class="container-fluid">
          <div class="row">
              <div class="col-md-12 col-lg-12 col-sm-12 col-12">
                  <div class="top-menus">
                      <ul class="top-menu-list">
                          <li class="top-menu-item">
                              <a href="#"><i class="fa fa-phone" aria-hidden="true"></i> +91-0612-2219395, 2219226</a>
                          </li>
                          <li class="top-menu-item">
                              <a href="#"><i class="fa fa-envelope" aria-hidden="true"></i><EMAIL></a>
                          </li>
                          <li class="top-menu-item"><a href="#">Skip to main content</a></li>
                          <li class="top-menu-item"><a href="#">Screen Reader Access</a></li>
                          <li class="social-icons">
                              <ul class="social-icons-list">
                                  <li>
                                  <i class="fab fa-twitter"></i>
                                  </li>
                                  <li>
                                  <i class="fab fa-linkedin-in"></i>
                                  </li>
                                  <li>
                                  <i class="fab fa-facebook-f"></i>
                                  </li>
                                  <li>
                                      <a href="https://webmail.ansiss.res.in" class="social-button social-button--webmail"
                                          aria-label="Webmail">
                                          <i class="fas fa-envelope"></i>
                                      </a>
                                  </li>
                                  <li class="nav-item" id="google_translate_element"></li>
                              </ul>
                          </li>
                      </ul>
                  </div>
              </div>
          </div>
      </div>
  </section>
    <header class="logo-section"    >
      <div class="container-fluid">
          <div class="row">
              <div class="logo-area">
                  <div class="col-md-2 col-lg-2 col-sm-2 col-12">
                      <div class="logo">
                          <a href="#">
                              <img src="/img/LOGO1.png" alt="img-logo" class="img-fluid"
                                  style="filter: drop-shadow(0 0 0.5rem rgb(253, 253, 253));" />
                          </a>
                      </div>
                  </div>
                  <div class="col-md-8 col-lg-8 col-sm-8 col-12">
                      <div class="logo-text">
                          <h3>अनुग्रह नारायण सिंह समाज अध्ययन संस्थान</h3>
                          <h2 id="name-eng">A N Sinha Institute of Social Studies</h2>
                      </div>
                  </div>
                  <div class="col-md-2 col-lg-2 col-sm-2 col-6">
                      <div class="logo-right">
                          <img src="/img/directorhd.png" alt="img-logo" class="img-fluid" />
                      </div>
                  </div>
              </div>
          </div>
      </div>
  </header>
	
    <section class="navMenu" >
        <div class="container" >
          <div class="row">
            <nav class="navbar navbar-expand-lg">
               <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarTogglerDemo01" aria-controls="navbarTogglerDemo01" aria-expanded="false" aria-label="Toggle navigation">
                <i class="fas fa-bars"></i>
              </button>
             <div class="collapse navbar-collapse" id="bs-example-navbar-collapse-1">
              <div class="menu-primary-menu-container"><ul class="nav navbar-nav navigation main-nav"><li class=''><a class="dropdown-item" href="/">Home</a></li>
                <li class='dropdown'><a class="dropdown-toggle all-toggle" href="javascript:void(0)">About Us</a>
                <ul/ class="dropdown-menu" />
                <li class=''><a class="dropdown-item" href="/about">About Us</a></li>
                <li class=''><a class="dropdown-item" href="/bocmembers">BOC Members</a></li>
               
                <li class=''><a class="dropdown-item" href="/facultymember">Faculty Members</a></li>
                <li class=''><a class="dropdown-item" href="/doctoral_fellow">PHD Scholar</a></li>
                <li class=''><a class="dropdown-item" href="/director_tenior">Director Tenure</a></li>
                <li class=''><a class="dropdown-item" href="/Director_desk">Director Desk</a></li>
                <li class=''><a class="dropdown-item" href="/administration">Administration</a></li>
                </ul>
                </li>
                <li class='dropdown'><a class="dropdown-toggle all-toggle" href="javascript:void(0)">Infrastructure</a>
                <ul/ class="dropdown-menu" />
                <li class=''><a class="dropdown-item" href="/campus">Building & Campus</a></li>
              
                <li class=''><a class="dropdown-item" href="/ansissLibrary">ANSISS Library</a></li>
                <!-- <li class=''><a class="dropdown-item" href="/">About Library</a></li> -->
                <li class=''><a class="dropdown-item" href="http://eg4.nic.in/CGOVLIB1/OPAC/Default.aspx?LIB_CODE=ANSISS">ANSISS e-Library</a></li>
               
                <li class=''><a class="dropdown-item" href="/computerlab">Computer Centre</a></li>
                <li class=''><a class="dropdown-item" href="/facilites">ANSISS Facilities</a></li>
                
                </ul>
                </li>
                <li class='dropdown'><a class="dropdown-toggle all-toggle" href="javascript:void(0)">Activities</a>
                <ul/ class="dropdown-menu" />
                <li class=''><a class="dropdown-item" href="/Phd_Program">Ph. D Programme</a></li>
                <li class=''><a class="dropdown-item" href="/e_Content">e-Content</a></li>
                <li class=''><a class="dropdown-item" href="/completed_project">Completed Projects</a></li>
                <li class=''><a class="dropdown-item" href="/OnGoingProject">On Going Projects</a></li>
                <li class=''><a class="dropdown-item" href="/Workshop">Workshop/Training</a></li>
                <li class=''><a class="dropdown-item" href="/Dialogue_series">Dialogue Series</a></li>
                <li class=''><a class="dropdown-item" href="/internal_seminar">Internal Seminars</a></li>
                <li class=''><a class="dropdown-item" href="/workstation">Work Station</a></li>
                </ul>
                </li>
                <li class='dropdown'><a class="dropdown-toggle all-toggle" href="javascript:void(0)">Publications</a>
                <ul/ class="dropdown-menu" />
                <li class=''><a class="dropdown-item" href="/annualReport">Annual Report</a></li>
                <li class=''><a class="dropdown-item" href="/workingPaper">Working Paper</a></li>
                <li class=''><a class="dropdown-item" href="/newsLetter">Newsletters</a></li>
                </ul>
                </li>
                <li class=''><a class="dropdown-item" href="/journal_about">Journal</a></li>
                <li class=''><a class="dropdown-item" href="/carrier-view">Career</a></li>
                <li class=''><a class="dropdown-item" href="/gallary">Gallery<img style="width: 40px; display: inline-block;" src="/images/new-gif-image.gif" /></a></li>
  


</ul></div>                    
                 </div>
              
            </nav>
          </div>
        </div>
      </section>
   
<meta name='robots' content='max-image-preview:large' />
<link rel="alternate" type="application/rss+xml" title=" &raquo; Feed" href="" />
<link rel="alternate" type="application/rss+xml" title=" &raquo; Comments Feed" href="" />


		<style type="text/css"></style>
     <script>
     
        $( document ).ready(function() {
        $('.dropdown-toggle').append('<span class="caret"></span>');
        
        
        $(".dropdown-menu li ").addClass("sub-dropdown");
        $(".dropdown-menu li ul").addClass("sub-dropdown-menu");
        $(".dropdown-menu li ul").removeClass("dropdown-menu");
        $(".sub-dropdown-menu li").removeClass("sub-dropdown");
        $(".sub-dropdown-menu li a").removeClass("dropdown-item");
       
     
});


   </script>
   <script>
    document.addEventListener('DOMContentLoaded', function() {
      document.querySelector('.navbar-toggler').addEventListener('click', function() {
  
        var navbarNav = document.getElementById('bs-example-navbar-collapse-1');
        if (navbarNav.classList.contains('show')) {
          navbarNav.classList.remove('show');
        } else {
          navbarNav.classList.add('show');
        }
      });
    });
  </script>